name: E2E Tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    timeout-minutes: 20

    permissions:
      contents: read
      packages: read

    services:
      postgres:
        image: pgvector/pgvector:pg15
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: password123
          POSTGRES_DB: transcendence_e2e_db
        ports:
          - 5444:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      localstack:
        image: localstack/localstack:3.6.0
        env:
          SERVICES: dynamodb,logs,s3,sqs
          DEFAULT_REGION: us-east-1
          AWS_ACCESS_KEY_ID: test
          AWS_SECRET_ACCESS_KEY: test
        ports:
          - 4566:4566
          - 4571:4571

      opensearch:
        image: opensearchproject/opensearch:2.11.1
        env:
          discovery.type: single-node
          plugins.security.disabled: true
          OPENSEARCH_JAVA_OPTS: -Xms512m -Xmx512m
        ports:
          - '9200:9200'

    env:
      DATABASE_URL: postgresql://postgres:password123@localhost:5444/transcendence_e2e_db
      AWS_PAGER: ''
      AWS_ACCESS_KEY_ID: test
      AWS_SECRET_ACCESS_KEY: test
      AWS_ENDPOINT_URL: http://localhost:4566
      AWS_REGION: us-east-1
      AWS_DEFAULT_REGION: us-east-1
      GITHUB_TOKEN: ${{ secrets.GIT_TOKEN }}
      OPENSEARCH_NODE: http://localhost:9200

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          registry-url: https://npm.pkg.github.com/

      - name: Clean npm cache
        run: npm cache clean --force

      - name: Install Dependencies
        run: npm ci

      - name: Run Migrations
        run: npx prisma migrate deploy --schema=./prisma/schema

      - name: Create Dynamo workflow table in LocalStack
        run: |
          aws dynamodb create-table --cli-input-json file://./dynamo/workflow-definition.json
          echo "Dynamo workflow table created"
          aws dynamodb create-table --cli-input-json file://./dynamo/workflow-execution-definition.json
          echo "Dynamo workflow execution table created"
          aws dynamodb create-table --cli-input-json file://./dynamo/message-history-definition.json
          echo "Dynamo message history table created"
          aws dynamodb create-table --cli-input-json file://./dynamo/portfolio-item-custom-data-definition.json
          echo "Dynamo portfolio item custom data table created"
          aws dynamodb create-table --cli-input-json file://./dynamo/customer-channel-integration-data-definition.json
          echo "Dynamo customer channel integration data table created"
          aws dynamodb create-table --cli-input-json file://./dynamo/conversation-message-definition.json
          echo "Dynamo conversation message table created"
          aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/middleware-response-output-definition.json
          echo "Dynamo middleware response output table created"
          aws dynamodb create-table --cli-input-json file://${PWD}/dynamo/customer-preferences-definition.json
          echo "Dynamo customer preferences table created"

      - name: Create S3 buckets in LocalStack
        run: |
          echo "Creating S3 buckets..."
          aws s3api create-bucket --bucket transcendence-direct-message-files --region us-east-1
          aws s3api create-bucket --bucket transcendence-portfolio-import-files --region us-east-1

      - name: Create SQS queues in LocalStack
        run: |
          echo "Creating SQS queues..."
          aws sqs create-queue --queue-name fideleasy_portfolio_import 
          aws sqs create-queue --queue-name saleszap_portfolio_import 
          aws sqs create-queue --queue-name collectcash_portfolio_import 
          aws sqs create-queue --queue-name fideleasy_portfolio_workflow 
          aws sqs create-queue --queue-name saleszap_portfolio_workflow 
          aws sqs create-queue --queue-name collectcash_portfolio_workflow
          aws sqs create-queue --queue-name message_outgoing
          aws sqs create-queue --queue-name message_incoming
          aws sqs create-queue --queue-name data_insights_vector_store_hiring

      - name: Seed Database
        run: npm run local-infra:seed
        env:
          IMPORT_ITEM_QUEUE_PREFIX: portfolio-item-
          IMPORT_ITEM_QUEUE_SUFIX: sufix
          SQS_QUEUE_BASE_URL: http://localhost:4566/000000000000/

      - name: Run E2E Tests
        run: npm run test:e2e-ci-verbose
        env:
          NODE_ENV: automated-tests
          JWT_SECRET: ${{ secrets.JWT_SECRET }}
          DATABASE_URL: ${{ env.DATABASE_URL }}
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          INTELLIGENCE_SERVICE_URL: http://localhost:3010
          ORCHESTRATOR_SERVICE_URL: http://localhost:3010
          BUSINESS_BASE_SERVICE_URL: http://localhost:3010
          MESSAGEHUB_SERVICE_URL: http://localhost:3010
          TRANSCENDENCE_ADMIN_WEB_URL: http://localhost:3390
          ELEVENLABS_API_KEY: ${{ secrets.ELEVENLABS_API_KEY }}
          LOG_GROUP_NAME_TRANSCENDENCE: transcendence-staging
          LOG_STREAM_NAME_TRANSCENDENCE: transcendence-staging
          PORTFOLIO_IMPORT_FIDELEASY_QUEUE_URL: 'http://localhost:4566/000000000000/fideleasy_portfolio_import'
          PORTFOLIO_IMPORT_SALESZAP_QUEUE_URL: 'http://localhost:4566/000000000000/saleszap_portfolio_import'
          PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL: 'http://localhost:4566/000000000000/collectcash_portfolio_import'
          PORTFOLIO_WORKFLOW_FIDELEASY_QUEUE_URL: 'http://localhost:4566/000000000000/fideleasy_portfolio_workflow'
          PORTFOLIO_WORKFLOW_SALESZAP_QUEUE_URL: 'http://localhost:4566/000000000000/saleszap_portfolio_workflow'
          PORTFOLIO_WORKFLOW_COLLECTCASH_QUEUE_URL: 'http://localhost:4566/000000000000/collectcash_portfolio_workflow'
          DEFAULT_PORTFOLIO_PROCESSING_RATE_LIMIT: 100
          DEFAULT_PORTFOLIO_IDLE_AFTER: 7
          OUTGOING_MESSAGE_QUEUE_URL: 'http://localhost:4566/000000000000/message_outgoing'
          INCOMING_MESSAGE_QUEUE_URL: 'http://localhost:4566/000000000000/message_incoming'
          DATA_INSIGHTS_VECTOR_STORE_HIRING_QUEUE_URL: 'http://localhost:4566/000000000000/data_insights_vector_store_hiring'
          MAX_DELAY_BETWEEN_MESSAGES: 30
          SLACK_TOKEN: 'slack_token_fake'
          VONAGE_API_KEY: 'ebe08b1c'
          VONAGE_API_SECRET: 'kqWCQ2kHErHLorEL'
          VONAGE_SIGNATURE_SECRET: ''
          DIRECT_MESSAGE_FILES_BUCKET: 'transcendence-direct-message-files'
          PORTFOLIO_IMPORT_FILES_BUCKET: 'transcendence-portfolio-import-files'
          IMPORT_ITEM_QUEUE_PREFIX: 'portfolio-item-'
          IMPORT_ITEM_QUEUE_SUFIX: 'sufix'
          SQS_QUEUE_BASE_URL: 'http://localhost:4566/000000000000/'
          OPENSEARCH_NODE: http://localhost:9200
