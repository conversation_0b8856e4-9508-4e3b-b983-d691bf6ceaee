name: Push Workflows

on:
  push:
    branches:
      - main

jobs:
  build:
    name: Build
    uses: edutalent/central-workflows/.github/workflows/npm-build.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  eslint:
    name: ESL<PERSON>
    uses: edutalent/central-workflows/.github/workflows/eslint.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  truffleHog:
    name: TruffleHog
    uses: edutalent/central-workflows/.github/workflows/trufflehog-security-scan.yml@main

  unit-tests:
    name: Unit Tests
    needs: [build]
    uses: edutalent/central-workflows/.github/workflows/unit-tests.yml@main
    with:
      dynamic-env: |
        NODE_ENV=automated-tests
        INTELLIGENCE_SERVICE_URL=http://localhost:3010
        ORCHESTRATOR_SERVICE_URL=http://localhost:3010
        LOG_GROUP_NAME_TRANSCENDENCE=transcendence-staging
        LOG_STREAM_NAME_TRANSCENDENCE=transcendence-staging
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      ai-supplier-token: ${{ secrets.OPENAI_API_KEY }}
      elevenlabs-token: ${{ secrets.ELEVENLABS_API_KEY }}

  check-exact-dependencies:
    name: Check Exact Dependencies
    uses: edutalent/central-workflows/.github/workflows/check-exact-dependencies.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  audit:
    name: NPM Audit
    uses: edutalent/central-workflows/.github/workflows/npm-audit.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  check-env-vars:
    name: Check ECS Environment Variables
    uses: edutalent/central-workflows/.github/workflows/check-ecs-envs.yml@main
    with:
      aws-region: "us-east-1"
      task-definition-name: ${{ vars.AWS_ECS_TASK_PROD }}
    secrets:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}

  deploy-prod:
    name: Deploy to production
    needs: [build, eslint, truffleHog, unit-tests, check-exact-dependencies, check-env-vars]
    uses: edutalent/central-workflows/.github/workflows/deploy-ecs.yml@main
    with:
      ecr-repository-name: ${{ vars.AWS_ECR_REPO_PROD }}
      image-tag: "latest"
      dockerfile: "DockerfileProd"
      branch-ref: "main"
      aws-region: "us-east-1"
      ecs-cluster: ${{ vars.AWS_ECS_CLUSTER_PROD }}
      ecs-service: ${{ vars.AWS_ECS_SERVICE_PROD }}
      ecs-task-definition: ${{ vars.AWS_ECS_TASK_PROD }}
      node-version: "20"
      cloudwatch-log-group-name: "/ecs/task-transcendence-prod"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_PROD }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_PROD }}
      central-workflows-pat: ${{ secrets.CENTRAL_WORKFLOWS_PAT }}

