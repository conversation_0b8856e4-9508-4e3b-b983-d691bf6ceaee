name: PR Workflows

on:
  pull_request:
    branches: [main]
    types: [labeled]

concurrency:
  group: deploy-ecs-${{ github.ref }}
  cancel-in-progress: false

jobs:
  check-env-vars:
    name: Check ECS Environment Variables
    uses: edutalent/central-workflows/.github/workflows/check-ecs-envs.yml@main
    with:
      aws-region: "us-east-1"
      task-definition-name: ${{ vars.AWS_ECS_TASK_STAGING }}
    secrets:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}

  deploy-staging:
    name: Deploy to Staging
    if: github.event.action == 'labeled' && github.event.label.name == 'deploy/staging'
    needs: [check-env-vars]
    uses: edutalent/central-workflows/.github/workflows/deploy-ecs.yml@main
    with:
      ecr-repository-name: ${{ vars.AWS_ECR_REPO_STAGING }}
      image-tag: "latest"
      dockerfile: "DockerfileStaging"
      branch-ref: ${{ github.head_ref }}
      aws-region: "us-east-1"
      ecs-cluster: ${{ vars.AWS_ECS_CLUSTER_STAGING }}
      ecs-service: ${{ vars.AWS_ECS_SERVICE_STAGING }}
      ecs-task-definition: ${{ vars.AWS_ECS_TASK_STAGING }}
      node-version: "20"
      cloudwatch-log-group-name: "/ecs/task-transcendence-staging"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}
      central-workflows-pat: ${{ secrets.CENTRAL_WORKFLOWS_PAT }}

