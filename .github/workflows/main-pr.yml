name: PR Workflows

on:
  pull_request:
    branches:
      - main

jobs:
  build:
    name: Build
    if: github.event.action != 'labeled'
    uses: edutalent/central-workflows/.github/workflows/npm-build.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  eslint:
    name: ESLint
    if: github.event.action != 'labeled'
    uses: edutalent/central-workflows/.github/workflows/eslint.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  truffleHog:
    name: TruffleHog
    if: github.event.action != 'labeled'
    uses: edutalent/central-workflows/.github/workflows/trufflehog-security-scan.yml@main

  unit-tests:
    name: Unit Tests
    if: github.event.action != 'labeled'
    needs: [build]
    uses: edutalent/central-workflows/.github/workflows/unit-tests.yml@main
    with:
      dynamic-env: |
        NODE_ENV=automated-tests
        INTELLIGENCE_SERVICE_URL=http://localhost:3010
        ORCHESTRATOR_SERVICE_URL=http://localhost:3010
        LOG_GROUP_NAME_TRANSCENDENCE=transcendence-staging
        LOG_STREAM_NAME_TRANSCENDENCE=transcendence-staging
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}
      ai-supplier-token: ${{ secrets.OPENAI_API_KEY }}
      elevenlabs-token: ${{ secrets.ELEVENLABS_API_KEY }}

  check-exact-dependencies:
    name: Check Exact Dependencies
    if: github.event.action != 'labeled'
    uses: edutalent/central-workflows/.github/workflows/check-exact-dependencies.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  audit:
    name: NPM Audit
    if: github.event.action != 'labeled'
    uses: edutalent/central-workflows/.github/workflows/npm-audit.yml@main
    with:
      node-version: "20"
    secrets:
      github-token: ${{ secrets.GITHUB_TOKEN }}

  check-env-vars:
    name: Check ECS Environment Variables
    uses: edutalent/central-workflows/.github/workflows/check-ecs-envs.yml@main
    with:
      aws-region: "us-east-1"
      task-definition-name: ${{ vars.AWS_ECS_TASK_STAGING }}
    secrets:
      aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_STAGING }}
      aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_STAGING }}


