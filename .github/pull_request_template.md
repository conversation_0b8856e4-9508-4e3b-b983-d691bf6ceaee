## Title

- **Short and Clear:** The title should succinctly reflect the primary purpose of the PR.
- **Start with a Verb:** Using verbs like "Add", "<PERSON>move", "Refactor", or "Fix" can quickly give context about the nature of the change.
- **Follow the pattern:** [IssueType] [Issue Id] Title. Ex. [Task] [EA-123] Refactor getCategories() function
- **No Ending Period:** Titles are like headers, so they shouldn't have punctuation at the end.
- **Limit Length:** Aim for no more than 50 characters (not including IssueType and Issue Id prefixes) so that they display well on most interfaces.

## Description

- **Context:** Start with a brief explanation of why this PR is happening. What's the problem or need to be addressed?
- **Enumeration:** If there are multiple significant changes, enumerate them.
  Link to Tickets or Tasks: Include references or links to Jira Issues.
- **Deployment or Migration Instructions:** If there are special deployment steps or specific migrations, indicate them.

## Type of Change

- [ ] Feature: New functionality (non-breaking)
- [ ] Improvement: Minor enhancements or refactoring
- [ ] Bugfix: Non-breaking issue resolution
- [ ] Breaking Change: Alters existing behavior or APIs
- [ ] Security: Fixes vulnerabilities or improves security posture
- [ ] Deprecation: Marks features for removal
- [ ] Removal: Deletes deprecated or unused code
- [ ] Logs/Monitoring: Adds or adjusts logging
- [ ] Repo Setup: Configuration, CI/CD, dependencies, etc.

## Checklist

- [ ] Self-reviewed code
- [ ] Added or adjusted E2E tests
- [ ] Tested in staging (for PRs to main branch)
- [ ] Updated documentation (if applicable)
- [ ] Updated changelog (if necessary)

More information is available in [Confluence](https://edutalent.atlassian.net/wiki/spaces/EDUTALENT1/pages/32440321/Guidelines+for+contributions+in+our+repositories)
