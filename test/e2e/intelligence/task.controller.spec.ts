import { MessageType } from '@common/enums';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { UpdateTaskDto } from '@intelligence/application/dto/in/update-task.dto';
import { TaskDto } from '@intelligence/application/dto/task.dto';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import * as process from 'node:process';
import { ExecuteTaskRequestDto } from '@intelligence/application/dto/in/execute-task-request.dto';

describe('Task (e2e)', () => {
  let app: INestApplication;
  let intelligenceServiceUrl: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;

    intelligenceServiceUrl = process.env.INTELLIGENCE_SERVICE_URL.toString();
  });

  it('/v1/intelligence/tasks/:taskId (GET) - Find task By Id', async () => {
    const task = buildTradutorTaskFake(uuidv4());

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const taskGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/tasks/' + taskId)
      .expect(200);

    expect(taskGetResponse.body.data.description).toEqual(
      createdTaskResponse.body.data.description,
    );
    expect(taskGetResponse.body.data.agent).toEqual(createdTaskResponse.body.data.agent);
    expect(taskGetResponse.body.data.id).toEqual(taskId);
  });

  it('/v1/intelligence/tasks/:taskId (DELETE) - Soft Delete', async () => {
    const task = buildTradutorTaskFake(uuidv4());

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const taskGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/tasks/' + taskId)
      .expect(200);

    expect(taskGetResponse.body).toBeDefined();
    expect(taskGetResponse.body.data.status).toEqual('ACTIVE');

    await request(app.getHttpServer())
      .delete('/api/v1/intelligence/tasks/' + taskId)
      .expect(200);

    await request(app.getHttpServer())
      .get('/api/v1/intelligence/tasks/' + taskId)
      .expect(404);
  });

  it('/v1/intelligence/tasks (POST) - Create Task', async () => {
    const task = buildTradutorTaskFake(uuidv4());

    const response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.id).toBeDefined();
  });

  it('/v1/intelligence/tasks/:taskId (PUT) - Update Task', async () => {
    const task = buildTradutorTaskFake(uuidv4());

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const updatedTask: UpdateTaskDto = {
      description: 'Updated description',
      agent: uuidv4(),
      responseTemplate: 'Updated response template',
      managerAgentId: 'updated-agent-manager',
    };

    const updatedTaskResponse = await request(app.getHttpServer())
      .put('/api/v1/intelligence/tasks/' + taskId)
      .send(updatedTask)
      .expect(200);

    expect(updatedTaskResponse.body).toBeDefined();
    expect(updatedTaskResponse.body.data.description).toEqual(updatedTask.description);
    expect(updatedTaskResponse.body.data.agent).toEqual(updatedTask.agent);
    expect(updatedTaskResponse.body.data.responseTemplate).toEqual(updatedTask.responseTemplate);
    expect(updatedTaskResponse.body.data.managerAgentId).toEqual(updatedTask.managerAgentId);
    expect(updatedTaskResponse.body.data.id).toEqual(taskId);

    const taskGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/tasks/' + taskId)
      .expect(200);

    expect(taskGetResponse.body.data.description).toEqual(updatedTask.description);
    expect(taskGetResponse.body.data.agent).toEqual(updatedTask.agent);
    expect(taskGetResponse.body.data.responseTemplate).toEqual(updatedTask.responseTemplate);
    expect(taskGetResponse.body.data.managerAgentId).toEqual(updatedTask.managerAgentId);
    expect(taskGetResponse.body.data.id).toEqual(taskId);
  });

  it('/v1/intelligence/tasks/execute (POST) - Execute Task With Audio Input', async () => {
    const agent = buildTradutorAgentFake(MessageType.TEXT);

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    const task = buildTradutorTaskFake(agentId);

    const createTaskresponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createTaskresponse.body).toBeDefined();
    expect(createTaskresponse.body.data.id).toBeDefined();

    const taskId = createTaskresponse.body.data.id;
    const executeTaskDto = {
      taskId,
      thread: uuidv4(),
      params: { prompt: 'Convert this audio to text' },
      fileUrl: `${process.env.AWS_ENDPOINT_URL}/${process.env.DIRECT_MESSAGE_FILES_BUCKET}/interview-answer.mp3`,
      lang: 'pt-BR',
      inputType: MessageType.AUDIO,
    } as ExecuteTaskRequestDto;

    const executeTradutorResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks/execute')
      .send(executeTaskDto)
      .expect(201);

    expect(executeTradutorResponse.body).toBeDefined();

    const outputBuffer = executeTradutorResponse.body.output;

    const text = Buffer.from(outputBuffer).toString('utf8');

    expect(text).toBeDefined();
  });

  it('/v1/intelligence/tasks/execute (POST) - Should fail with invalid input for missing params in task', async () => {
    const agent = buildTradutorAgentFake(MessageType.TEXT);

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    const task = buildTaskFake(agentId);

    const createTaskresponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createTaskresponse.body).toBeDefined();
    expect(createTaskresponse.body.data.id).toBeDefined();

    const taskId = createTaskresponse.body.data.id;

    const executeTradutorResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks/execute')
      .send({
        taskId,
        thread: uuidv4(),
        params: { value: '1000' }, //missing key date according to task description
        fileUrl: intelligenceServiceUrl + '/audios/interview-answer.mp3',
        lang: 'pt-BR',
        inputType: MessageType.TEXT,
      })
      .expect(400);

    expect(executeTradutorResponse.body).toBeDefined();
    expect(executeTradutorResponse.body.message).toContain('Invalid values for keys: date');
  });

  it.skip('/v1/intelligence/tasks/execute (POST) - Execute Task With Audio Input and Audio Output', async () => {
    const agent = buildTradutorAgentFake(MessageType.AUDIO);

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    const task = buildTradutorTaskFake(agentId);

    const createTaskresponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createTaskresponse.body).toBeDefined();
    expect(createTaskresponse.body.data.id).toBeDefined();

    const taskId = createTaskresponse.body.data.id;

    const executeTradutorResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks/execute')
      .send({
        taskId,
        thread: uuidv4(),
        params: {},
        fileUrl: intelligenceServiceUrl + '/audios/interview-answer.mp3',
        lang: 'pt-BR',
        inputType: MessageType.AUDIO,
      })
      .expect(201);

    expect(executeTradutorResponse.body).toBeDefined();

    const outputBuffer = Buffer.from(executeTradutorResponse.body.output);
    expect(outputBuffer.length).toBeGreaterThan(1000);
  });

  it('/v1/intelligence/tasks/execute (POST) - Execute Simplified Task', async () => {
    const agent = buildTradutorAgentFake(MessageType.TEXT);

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    const task = buildTradutorTaskFake(agentId, '{"output":"[response]"}');

    const createTaskresponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createTaskresponse.body).toBeDefined();
    expect(createTaskresponse.body.data.id).toBeDefined();

    const taskId = createTaskresponse.body.data.id;

    const executeTradutorResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks/execute-simplified')
      .send({
        taskId,
        thread: uuidv4(),
        params: {
          prompt: 'cachorro',
        },
      })
      .expect(201);

    expect(executeTradutorResponse.body).toBeDefined();

    // expect(executeTradutorResponse.body.toLowerCase()).toBe('dog');
  });

  it('/v1/intelligence/tasks/:taskId/variables (GET) - Get task variables', async () => {
    // First create an agent with backstory variables
    const agent = buildTradutorAgentFake(MessageType.TEXT);
    agent.backstory = `You are a debt collector. You have access to the following variables:
    - NOME_DO_CLIENTE
    - CPF_DO_CLIENTE
    - CPF_DO_CLIENTE_DIGITOS
    - DATA_DE_NASCIMENTO_DO_CLIENTE
    - VALOR_DIVIDA_ORIGINAL
    - VALOR_DIVIDA_CORRIGIDO
    - DIAS_ATRASO
    - NOME_LOJA
    - VENC_PRIM_PARCELA
    - NOME_DO_CARTAO
    - ID_DO_CONTRATO
    - TITULOS`;

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    // Create a task with the agent
    const task: TaskDto = {
      description: `Create a text using the following variables: {{NOME_DO_CLIENTE}}, {{CPF_DO_CLIENTE}}, {{VALOR_DIVIDA_ORIGINAL}}`,
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const variablesResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/tasks/' + taskId + '/variables')
      .expect(200);

    expect(variablesResponse.body).toBeDefined();
    expect(variablesResponse.body.statusCode).toBe(200);
    expect(Array.isArray(variablesResponse.body.data)).toBe(true);

    // Verify that the response contains only the variables used in the task description
    const expectedVariables = [
      'NOME_DO_CLIENTE',
      'CPF_DO_CLIENTE',
      'VALOR_DIVIDA_ORIGINAL',
      'PHONE_NUMBER',
    ];

    // Check that all expected variables are present in the response
    expectedVariables.forEach(variable => {
      expect(variablesResponse.body.data).toContain(variable);
    });

    // Verify that no unexpected variables are present
    expect(variablesResponse.body.data.length).toBe(expectedVariables.length);
  });

  function buildTradutorAgentFake(messageTypeOutPut: MessageType): AgentDto {
    return {
      role: 'Tradutor',
      backstory: `You are a tradutor. Don't provide comments or suggestions, just translate the text.`,
      llmModel: 'gpt-4o-mini',
      outputType: messageTypeOutPut,
      lang: 'en-US',
      voice: 'Xb7hH8MSUJpSbSDYk0k2',
    };
  }

  function buildTradutorTaskFake(agentId: string, responseTemplate = null): TaskDto {
    return {
      description: `Translate the following text: {{prompt}}`,
      agent: agentId,
      responseTemplate,
      managerAgentId: null,
    };
  }

  function buildTaskFake(agentId: string, responseTemplate = null): TaskDto {
    return {
      description: `Create a text to say that the user needs to pay a value of {{value}} until a given date {{date}}`,
      agent: agentId,
      responseTemplate,
      managerAgentId: null,
    };
  }

  it('should fetch all tasks', async () => {
    const response = await request(app.getHttpServer()).get('/api/v1/intelligence/tasks');
    expect(response.status).toBe(200);
    expect(Array.isArray(response.body.data)).toBe(true);
    if (response.body.data.length > 0) {
      const task = response.body.data[0];
      expect(task).toHaveProperty('id');
      expect(task).toHaveProperty('name');
      expect(task).toHaveProperty('description');
      expect(task).toHaveProperty('agent');
    }
  });

  it('should create a new task and verify it appears in the list', async () => {
    const newTask: TaskDto = {
      name: 'Testing-task',
      description: 'This is a test task',
      agent: '01a06ec7-6194-4825-ac8b-a26e0461f6b2',
      responseTemplate: null,
      managerAgentId: null,
      status: 'ACTIVE',
    };

    const createResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(newTask)
      .expect(201);

    expect(createResponse.body.data).toHaveProperty('id');
    const createdTaskId = createResponse.body.data.id;

    const listResponse = await request(app.getHttpServer()).get('/api/v1/intelligence/tasks');
    expect(listResponse.status).toBe(200);
    expect(Array.isArray(listResponse.body.data)).toBe(true);

    const createdTask = listResponse.body.data.find(task => task.id === createdTaskId);
    expect(createdTask).toBeDefined();
    expect(createdTask.name).toBe(newTask.name);
    expect(createdTask.description).toBe(newTask.description);
  });
});
