import { INestApplication } from '@nestjs/common';
import request from 'supertest';

import { MessageType } from '@common/enums';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { UpdateAgentDto } from '@intelligence/application/dto/in/update-agent.dto';

describe('Agent (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/intelligence/agents (POST) - Create Agent', async () => {
    const agent = createAgentFake();

    const response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.id).toBeDefined();
  });

  it('/v1/intelligence/agents (POST) - Create Agent with name', async () => {
    const agent = createAgentFake('Fake agent');

    const response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.id).toBeDefined();
    expect(response.body.data.name).toEqual(agent.name);
  });

  it('/v1/intelligence/agents (POST) - Create Agent with name and lang that already exists (BAD REQUEST)', async () => {
    const agent = createAgentFake('agent-test');
    const agentWithoutName = createAgentFake();

    await request(app.getHttpServer()).post('/api/v1/intelligence/agents').send(agent).expect(201);
    await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agentWithoutName)
      .expect(201);

    await request(app.getHttpServer()).post('/api/v1/intelligence/agents').send(agent).expect(400);
  });

  it('/v1/intelligence/agents/:agentid (GET) - Find agent By Id', async () => {
    const agent = createAgentFake('new-agent-test');

    const createdAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createdAgentResponse.body).toBeDefined();
    expect(createdAgentResponse.body.data.id).toBeDefined();

    const agentId = createdAgentResponse.body.data.id;

    const agentGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/agents/' + agentId)
      .expect(200);

    expect(agentGetResponse.body).toBeDefined();
    expect(agentGetResponse.body.data.role).toEqual(createdAgentResponse.body.data.role);
    expect(agentGetResponse.body.data.backstory).toEqual(createdAgentResponse.body.data.backstory);
    expect(agentGetResponse.body.data.llmModel).toEqual(createdAgentResponse.body.data.llmModel);
    expect(agentGetResponse.body.data.outputType).toEqual(
      createdAgentResponse.body.data.outputType,
    );
    expect(agentGetResponse.body.data.lang).toEqual(createdAgentResponse.body.data.lang);
    expect(agentGetResponse.body.data.voice).toEqual(createdAgentResponse.body.data.voice);
    expect(agentGetResponse.body.data.id).toEqual(agentId);
    expect(agentGetResponse.body.data.name).toEqual(createdAgentResponse.body.data.name);
  });

  it('/v1/intelligence/agents/name/:name/lang/:lang (GET) - Find agent name and role', async () => {
    const agent = createAgentFake('find-agent-test');

    const createdAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createdAgentResponse.body).toBeDefined();
    expect(createdAgentResponse.body.data.id).toBeDefined();

    const agentId = createdAgentResponse.body.data.id;

    const agentGetResponse = await request(app.getHttpServer())
      .get(`/api/v1/intelligence/agents/name/${agent.name}/lang/${agent.lang}`)
      .expect(200);

    expect(agentGetResponse.body).toBeDefined();
    expect(agentGetResponse.body.data.role).toEqual(createdAgentResponse.body.data.role);
    expect(agentGetResponse.body.data.backstory).toEqual(createdAgentResponse.body.data.backstory);
    expect(agentGetResponse.body.data.llmModel).toEqual(createdAgentResponse.body.data.llmModel);
    expect(agentGetResponse.body.data.outputType).toEqual(
      createdAgentResponse.body.data.outputType,
    );
    expect(agentGetResponse.body.data.lang).toEqual(createdAgentResponse.body.data.lang);
    expect(agentGetResponse.body.data.voice).toEqual(createdAgentResponse.body.data.voice);
    expect(agentGetResponse.body.data.id).toEqual(agentId);
    expect(agentGetResponse.body.data.name).toEqual(createdAgentResponse.body.data.name);
  });

  it('/v1/intelligence/agents/:agentId (PUT) - Update Agent', async () => {
    const agent = createAgentFake();

    const createdAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createdAgentResponse.body).toBeDefined();
    expect(createdAgentResponse.body.data.id).toBeDefined();

    const agentId = createdAgentResponse.body.data.id;

    const updatedAgent: UpdateAgentDto = {
      role: 'updated role',
      backstory: 'Updated backstory.',
    };

    const updatedAgentResponse = await request(app.getHttpServer())
      .put('/api/v1/intelligence/agents/' + agentId)
      .send(updatedAgent)
      .expect(200);

    expect(updatedAgentResponse.body).toBeDefined();
    expect(updatedAgentResponse.body.data.role).toEqual(updatedAgent.role);
    expect(updatedAgentResponse.body.data.backstory).toEqual(updatedAgent.backstory);
    expect(updatedAgentResponse.body.data.llmModel).toEqual(
      createdAgentResponse.body.data.llmModel,
    );
    expect(updatedAgentResponse.body.data.outputType).toEqual(
      createdAgentResponse.body.data.outputType,
    );
    expect(updatedAgentResponse.body.data.lang).toEqual(createdAgentResponse.body.data.lang);
    expect(updatedAgentResponse.body.data.voice).toEqual(createdAgentResponse.body.data.voice);
    expect(updatedAgentResponse.body.data.id).toEqual(agentId);

    const agentGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/agents/' + agentId)
      .expect(200);

    expect(agentGetResponse.body).toBeDefined();
    expect(agentGetResponse.body.data.role).toEqual(updatedAgent.role);
    expect(agentGetResponse.body.data.backstory).toEqual(updatedAgent.backstory);
    expect(agentGetResponse.body.data.llmModel).toEqual(updatedAgentResponse.body.data.llmModel);
    expect(agentGetResponse.body.data.outputType).toEqual(
      updatedAgentResponse.body.data.outputType,
    );
    expect(agentGetResponse.body.data.lang).toEqual(updatedAgentResponse.body.data.lang);
    expect(agentGetResponse.body.data.voice).toEqual(updatedAgentResponse.body.data.voice);
    expect(agentGetResponse.body.data.id).toEqual(agentId);
  });

  it('/v1/intelligence/agents/:agentId (DELETE) - Soft Delete Agent', async () => {
    const agent = createAgentFake();

    const createResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createResponse.body).toBeDefined();
    expect(createResponse.body.data.id).toBeDefined();

    const agentId = createResponse.body.data.id;

    let agentGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/agents/' + agentId)
      .expect(200);

    expect(agentGetResponse.body).toBeDefined();
    expect(agentGetResponse.body.data.status).toEqual('ACTIVE');

    await request(app.getHttpServer())
      .delete('/api/v1/intelligence/agents/' + agentId)
      .expect(200);

    agentGetResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/agents/' + agentId)
      .expect(404);

    const agentGetAllResponse = await request(app.getHttpServer())
      .get('/api/v1/intelligence/agents/')
      .expect(200);

    const agentDeleted = agentGetAllResponse.body.data.find(agent => agent.id === agentId);

    expect(agentDeleted).toBeUndefined();
  });

  function createAgentFake(name?: string): AgentDto {
    return {
      role: 'debt negotiator',
      backstory:
        'Expert in debt negotiation and financial counseling. Skilled in understanding customer situations, offering flexible repayment options, and maintaining positive customer relationships. Background in financial services with a focus on empathetic communication and conflict resolution.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
      name,
    };
  }
});
