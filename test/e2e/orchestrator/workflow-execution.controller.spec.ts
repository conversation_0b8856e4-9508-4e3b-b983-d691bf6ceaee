import { MessageType, RoleType } from '@common/enums';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { TaskDto } from '@intelligence/application/dto/task.dto';
import { INestApplication } from '@nestjs/common';
import { SaveWorkflowRequestDto } from '@orchestrator/application/dto/in/save-workflow-request.dto';
import request from 'supertest';

describe('Workflow Execution (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/orchestrator/workflow-executions/:id (Get) - get execution details', async () => {
    const agent = createAgentFake();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const task = buildTradutorTaskFake(agentId);

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const workflow = createWorkflowFake(taskId);

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const responseGet = await request(app.getHttpServer())
      .get(
        `/api/v1/orchestrator/workflow-executions/${startWorkflowResponse.body.data.workflowExecutionId}`,
      )
      .expect(200);

    expect(responseGet.body).toBeDefined();
  });

  it('/v1/orchestrator/workflow-executions/conversation-history/:workflowExecutionId (POST) - Add TEXT message directly in history', async () => {
    const agent = createAgentFake();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const task = buildTradutorTaskFake(agentId);

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const workflow = createWorkflowFake(taskId);

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const executeStepOneResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send({
        workflowExecutionId,
        params: { prompt: 'gato' },
        lang: 'en',
        messageType: MessageType.TEXT,
      })
      .expect(201);

    expect(executeStepOneResponse.body).toBeDefined();
    expect(executeStepOneResponse.body.data.workflowExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionOrder).toBeDefined();
    expect(executeStepOneResponse.body.data.output).toBeDefined();
    expect(executeStepOneResponse.body.data.outputType).toBeDefined();

    const stepOneResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(stepOneResponseText).toContain('cat');

    const responseGet = await request(app.getHttpServer())
      .get(
        `/api/v1/orchestrator/workflow-executions/${startWorkflowResponse.body.data.workflowExecutionId}`,
      )
      .expect(200);

    expect(responseGet.body).toBeDefined();

    const sendDirectMessageDto = {
      message: 'Hello, how are you?',
      messageType: MessageType.TEXT,
      roleType: RoleType.ATTENDANT,
    };

    const responsePostDirectMessage = await request(app.getHttpServer())
      .post(
        `/api/v1/orchestrator/workflow-executions/conversation-history/${startWorkflowResponse.body.data.workflowExecutionId}`,
      )
      .send(sendDirectMessageDto)
      .expect(201);

    expect(responsePostDirectMessage.body).toBeDefined();

    const [sentMessageReturned] = responsePostDirectMessage.body.data.filter(message => {
      return message.messageText === sendDirectMessageDto.message;
    });

    expect(sentMessageReturned).toBeDefined();
    expect(sentMessageReturned.role).toBe(RoleType.ATTENDANT);
    expect(sentMessageReturned.messageType).toBe(MessageType.TEXT);
    expect(sentMessageReturned.messageText).toBe(sendDirectMessageDto.message);
  });

  it('/v1/orchestrator/workflow-executions/conversation-history/:workflowExecutionId (POST) - Add TEXT message with FILE directly in history', async () => {
    const agent = createAgentFake();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const task = buildTradutorTaskFake(agentId);

    const createdTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    expect(createdTaskResponse.body).toBeDefined();
    expect(createdTaskResponse.body.data.id).toBeDefined();

    const taskId = createdTaskResponse.body.data.id;

    const workflow = createWorkflowFake(taskId);

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const executeStepOneResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send({
        workflowExecutionId,
        params: { prompt: 'gato' },
        lang: 'en',
        messageType: MessageType.TEXT,
      })
      .expect(201);

    expect(executeStepOneResponse.body).toBeDefined();
    expect(executeStepOneResponse.body.data.workflowExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionOrder).toBeDefined();
    expect(executeStepOneResponse.body.data.output).toBeDefined();
    expect(executeStepOneResponse.body.data.outputType).toBeDefined();

    const stepOneResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(stepOneResponseText).toContain('cat');

    const responseGet = await request(app.getHttpServer())
      .get(
        `/api/v1/orchestrator/workflow-executions/${startWorkflowResponse.body.data.workflowExecutionId}`,
      )
      .expect(200);

    expect(responseGet.body).toBeDefined();

    const sendDirectMessageDto = {
      message: 'Aqui está o seu boleto para pagamento:',
      messageType: MessageType.PDF,
      roleType: RoleType.ATTENDANT,
      fileUrl: 'http://localhost:4566/transcendence-direct-message-files/boleto_teste.pdf',
    };

    const responsePostDirectMessage = await request(app.getHttpServer())
      .post(
        `/api/v1/orchestrator/workflow-executions/conversation-history/${startWorkflowResponse.body.data.workflowExecutionId}`,
      )
      .send(sendDirectMessageDto)
      .expect(201);

    expect(responsePostDirectMessage.body).toBeDefined();

    const [sentMessageReturned] = responsePostDirectMessage.body.data.filter(message => {
      return message.messageText === sendDirectMessageDto.message;
    });

    expect(sentMessageReturned).toBeDefined();
    expect(sentMessageReturned.role).toBe(RoleType.ATTENDANT);
    expect(sentMessageReturned.messageType).toBe(MessageType.PDF);
    expect(sentMessageReturned.messageText).toBe(sendDirectMessageDto.message);
    expect(sentMessageReturned.fileUrl).toBe(sendDirectMessageDto.fileUrl);
  });

  function createWorkflowFake(taskId: string): SaveWorkflowRequestDto {
    return {
      name: 'write-job-description',
      description:
        'Generate a rich job description considering the job title, job description, and job requirements.',
      steps: [
        {
          description: 'Define specific responsibilities and requirements.',
          order: 1,
          taskId: taskId,
        },
      ],
    };
  }

  function createAgentFake(): AgentDto {
    return {
      role: 'debt negotiator',
      backstory:
        'Expert in debt negotiation and financial counseling. Skilled in understanding customer situations, offering flexible repayment options, and maintaining positive customer relationships. Background in financial services with a focus on empathetic communication and conflict resolution.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };
  }

  function buildTradutorTaskFake(agentId: string): TaskDto {
    return {
      description: `Translate the following phrease: {{prompt}}`,
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };
  }
});
