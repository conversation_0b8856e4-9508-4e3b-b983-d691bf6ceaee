import { MessageType, MiddlewareToolCategory, MiddlewareType, RecordStatus } from '@common/enums';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { TaskDto } from '@intelligence/application/dto/task.dto';
import { INestApplication } from '@nestjs/common';
import { SaveWorkflowRequestDto } from '@orchestrator/application/dto/in/save-workflow-request.dto';
import { ExecuteWorkflowRequestDto } from '@orchestrator/application/dto/in/execute-workflow-request.dto';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';

describe('Workflow (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/health (GET)', () => {
    return request(app.getHttpServer())
      .get('/api/v1/orchestrator/workflows/health')
      .expect(200)
      .expect('OK');
  });

  it('/v1/orchestrator/workflows (POST) - create workflow', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const response = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.workflowId).toBeDefined();
  });

  it('/v1/orchestrator/workflows (GET) - get workflow details', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const responseGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.data.name).toEqual(workflow.name);
    expect(responseGet.body.data.description).toEqual(workflow.description);
    expect(responseGet.body.data.steps).toBeDefined();
    expect(responseGet.body.data.steps.length).toEqual(workflow.steps.length);
    expect(responseGet.body.data.steps[0].description).toEqual(workflow.steps[0].description);
    expect(responseGet.body.data.steps[0].order).toEqual(workflow.steps[0].order);
    expect(responseGet.body.data.steps[0].taskId).toEqual(workflow.steps[0].taskId);
    expect(responseGet.body.data.steps[1].description).toEqual(workflow.steps[1].description);
    expect(responseGet.body.data.steps[1].order).toEqual(workflow.steps[1].order);
    expect(responseGet.body.data.steps[1].taskId).toEqual(workflow.steps[1].taskId);
  });

  it('/v1/orchestrator/workflows/:workflowId (PUT) - update workflow', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    let responseGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.data.name).toEqual(workflow.name);
    expect(responseGet.body.data.description).toEqual(workflow.description);
    expect(responseGet.body.data.steps).toBeDefined();
    expect(responseGet.body.data.steps.length).toEqual(workflow.steps.length);
    expect(responseGet.body.data.steps[0].description).toEqual(workflow.steps[0].description);
    expect(responseGet.body.data.steps[0].order).toEqual(workflow.steps[0].order);
    expect(responseGet.body.data.steps[0].taskId).toEqual(workflow.steps[0].taskId);
    expect(responseGet.body.data.steps[1].description).toEqual(workflow.steps[1].description);
    expect(responseGet.body.data.steps[1].order).toEqual(workflow.steps[1].order);
    expect(responseGet.body.data.steps[1].taskId).toEqual(workflow.steps[1].taskId);

    const workflowUpdated = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    workflowUpdated.steps = workflowUpdated.steps.slice(0, 1);

    const updateWorkflowResponse = await request(app.getHttpServer())
      .put('/api/v1/orchestrator/workflows/' + createWorkflowResponse.body.data.workflowId)
      .send(workflowUpdated)
      .expect(200);

    expect(updateWorkflowResponse.body).toBeDefined();
    expect(updateWorkflowResponse.body.data.workflowId).toEqual(
      createWorkflowResponse.body.data.workflowId,
    );

    responseGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.data.name).toEqual(workflowUpdated.name);
    expect(responseGet.body.data.description).toEqual(workflowUpdated.description);
    expect(responseGet.body.data.steps).toBeDefined();
    expect(responseGet.body.data.steps.length).toEqual(workflowUpdated.steps.length);
  });

  it('/v1/orchestrator/workflows (DELETE) - delete workflow', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    let responseGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.data.status).toEqual(RecordStatus.ACTIVE);

    await request(app.getHttpServer())
      .delete(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    responseGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${createWorkflowResponse.body.data.workflowId}`)
      .expect(200);

    expect(responseGet.body).toBeDefined();
    expect(responseGet.body.data.status).toEqual(RecordStatus.DELETED);
  });

  it('/v1/orchestrator/workflows/start (POST) - start workflow', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();
    expect(startWorkflowResponse.body.data.stepExecutionId).toBeDefined();
    expect(startWorkflowResponse.body.data.stepExecutionOrder).toBeDefined();
  });

  it('/v1/orchestrator/workflows/execute (POST) - execute workflow first step', async () => {
    const agent = createTradutorAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const englishTradutorTask = buildTradutorToEnglishTask(agentId);

    const createEnglishTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(englishTradutorTask)
      .expect(201);

    expect(createEnglishTradutorTaskResponse.body).toBeDefined();
    expect(createEnglishTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorEnglishTaskId = createEnglishTradutorTaskResponse.body.data.id;

    const portuguseTradutorTask = buildTradutorToPortugueseTask(agentId);

    const createPortuguseTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(portuguseTradutorTask)
      .expect(201);

    expect(createPortuguseTradutorTaskResponse.body).toBeDefined();
    expect(createPortuguseTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorPortuguseTaskId = createPortuguseTradutorTaskResponse.body.data.id;

    const workflow = createTranslationLanguageWorkflow(
      tradutorEnglishTaskId,
      tradutorPortuguseTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const data = createFakeExecuteWorkflow(workflowExecutionId, 'gato', MessageType.TEXT);

    const response = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send(data)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.workflowExecutionId).toBeDefined();
    expect(response.body.data.stepExecutionId).toBeDefined();
    expect(response.body.data.stepExecutionOrder).toBeDefined();
    expect(response.body.data.output).toBeDefined();
    expect(response.body.data.outputType).toBeDefined();

    const outputBuffer = response.body.data.output;

    const outputText = Buffer.from(outputBuffer).toString('utf8');
    expect(outputText).toContain('cat');
  });

  it('/v1/orchestrator/workflows/execute (POST) - execute workflow first step with user prompt multiple interations', async () => {
    const agent = createTradutorAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const englishTradutorTask = buildTradutorToEnglishTask(agentId);

    const createEnglishTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(englishTradutorTask)
      .expect(201);

    expect(createEnglishTradutorTaskResponse.body).toBeDefined();
    expect(createEnglishTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorEnglishTaskId = createEnglishTradutorTaskResponse.body.data.id;

    const portuguseTradutorTask = buildTradutorToPortugueseTask(agentId);

    const createPortuguseTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(portuguseTradutorTask)
      .expect(201);

    expect(createPortuguseTradutorTaskResponse.body).toBeDefined();
    expect(createPortuguseTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorPortuguseTaskId = createPortuguseTradutorTaskResponse.body.data.id;

    const workflow = createTranslationLanguageWorkflow(
      tradutorEnglishTaskId,
      tradutorPortuguseTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const userInputs = ['passaro', 'gato'];

    const expectedOutputs = ['bird', 'cat'];

    for (let i = 0; i < userInputs.length; i++) {
      const inputText = userInputs[i];
      const response = await request(app.getHttpServer())
        .post('/api/v1/orchestrator/workflows/execute')
        .send(createFakeExecuteWorkflow(workflowExecutionId, inputText, MessageType.TEXT))
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.data.workflowExecutionId).toBeDefined();
      expect(response.body.data.stepExecutionId).toBeDefined();
      expect(response.body.data.stepExecutionOrder).toBeDefined();
      expect(response.body.data.output).toBeDefined();
      expect(response.body.data.outputType).toBeDefined();

      const outputBuffer = response.body.data.output;

      const outputText = Buffer.from(outputBuffer).toString('utf8');
      expect(outputText).toContain(expectedOutputs[i]);
    }
  });

  it('/v1/orchestrator/workflows/execute-next-step (POST) - execute all steps in workflow', async () => {
    const agent = createTradutorAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const englishTradutorTask = buildTradutorToEnglishTask(agentId);

    const createEnglishTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(englishTradutorTask)
      .expect(201);

    expect(createEnglishTradutorTaskResponse.body).toBeDefined();
    expect(createEnglishTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorEnglishTaskId = createEnglishTradutorTaskResponse.body.data.id;

    const portuguseTradutorTask = buildTradutorToPortugueseTask(agentId);

    const createPortuguseTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(portuguseTradutorTask)
      .expect(201);

    expect(createPortuguseTradutorTaskResponse.body).toBeDefined();
    expect(createPortuguseTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorPortuguseTaskId = createPortuguseTradutorTaskResponse.body.data.id;

    const workflow = createTranslationLanguageWorkflow(
      tradutorEnglishTaskId,
      tradutorPortuguseTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const data = createFakeExecuteWorkflow(workflowExecutionId, 'gato', MessageType.TEXT);

    const executeStepOneResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send(data)
      .expect(201);

    expect(executeStepOneResponse.body).toBeDefined();
    expect(executeStepOneResponse.body.data.workflowExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionOrder).toBeDefined();
    expect(executeStepOneResponse.body.data.output).toBeDefined();
    expect(executeStepOneResponse.body.data.outputType).toBeDefined();

    const stepOneResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(stepOneResponseText).toContain('cat');

    for (let index = 1; index < workflow.steps.length; index++) {
      const executeTwoStepResponse = await request(app.getHttpServer())
        .post('/api/v1/orchestrator/workflows/execute-next-step')
        .send({
          fileUrl: null,
          workflowExecutionId,
          params: {
            prompt: stepOneResponseText,
          },
          lang: 'en',
          messageType: MessageType.TEXT,
        })
        .expect(201);

      expect(executeTwoStepResponse.body).toBeDefined();
      expect(executeTwoStepResponse.body.data.output).toBeDefined();
      expect(executeTwoStepResponse.body.data.thread).toBeDefined();
      expect(executeTwoStepResponse.body.data.outputType).toBeDefined();

      const stepTwoResponseText = Buffer.from(executeTwoStepResponse.body.data.output).toString(
        'utf8',
      );
      expect(stepTwoResponseText).toContain('gato');
    }
  });

  it('/v1/orchestrator/workflows/execute-previous-step (POST) - execute the previous step in workflow', async () => {
    const agent = createTradutorAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const englishTradutorTask = buildTradutorToEnglishTask(agentId);

    const createEnglishTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(englishTradutorTask)
      .expect(201);

    expect(createEnglishTradutorTaskResponse.body).toBeDefined();
    expect(createEnglishTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorEnglishTaskId = createEnglishTradutorTaskResponse.body.data.id;

    const portuguseTradutorTask = buildTradutorToPortugueseTask(agentId);

    const createPortuguseTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(portuguseTradutorTask)
      .expect(201);

    expect(createPortuguseTradutorTaskResponse.body).toBeDefined();
    expect(createPortuguseTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorPortuguseTaskId = createPortuguseTradutorTaskResponse.body.data.id;

    const workflow = createTranslationLanguageWorkflow(
      tradutorEnglishTaskId,
      tradutorPortuguseTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const data = createFakeExecuteWorkflow(workflowExecutionId, 'gato', MessageType.TEXT);

    const executeStepOneResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send(data)
      .expect(201);

    expect(executeStepOneResponse.body).toBeDefined();
    expect(executeStepOneResponse.body.data.workflowExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionOrder).toBeDefined();
    expect(executeStepOneResponse.body.data.output).toBeDefined();
    expect(executeStepOneResponse.body.data.outputType).toBeDefined();

    const stepOneResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(stepOneResponseText).toContain('cat');

    for (let index = 1; index < workflow.steps.length; index++) {
      const executeTwoStepResponse = await request(app.getHttpServer())
        .post('/api/v1/orchestrator/workflows/execute-next-step')
        .send({
          fileUrl: null,
          workflowExecutionId,
          params: {
            prompt: stepOneResponseText,
          },
          lang: 'en',
          messageType: MessageType.TEXT,
        })
        .expect(201);

      expect(executeTwoStepResponse.body).toBeDefined();
      expect(executeTwoStepResponse.body.data.output).toBeDefined();
      expect(executeTwoStepResponse.body.data.thread).toBeDefined();
      expect(executeTwoStepResponse.body.data.outputType).toBeDefined();

      const stepTwoResponseText = Buffer.from(executeTwoStepResponse.body.data.output).toString(
        'utf8',
      );
      expect(stepTwoResponseText).toContain('gato');
    }

    let workflowExecutionInfoGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflow-executions/${workflowExecutionId}`)
      .expect(200);

    expect(workflowExecutionInfoGet.body).toBeDefined();

    let stepExecutionInfo = workflowExecutionInfoGet.body.data.stepExecutions.find(
      stepExecution =>
        stepExecution.id === workflowExecutionInfoGet.body.data.currentStepExecutionId,
    );

    expect(stepExecutionInfo.order).toEqual(workflow.steps.length);

    const executePreviousStepResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute-previous-step')
      .send({
        workflowExecutionId,
        params: {
          prompt: 'gato',
        },
        fileUrl: null,
        lang: 'en',
        messageType: MessageType.TEXT,
      })
      .expect(201);

    expect(executePreviousStepResponse.body).toBeDefined();

    const previousStepResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(previousStepResponseText).toContain('cat');

    workflowExecutionInfoGet = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflow-executions/${workflowExecutionId}`)
      .expect(200);

    expect(workflowExecutionInfoGet.body).toBeDefined();

    stepExecutionInfo = workflowExecutionInfoGet.body.data.stepExecutions.find(
      stepExecution =>
        stepExecution.id === workflowExecutionInfoGet.body.data.currentStepExecutionId,
    );

    expect(stepExecutionInfo.order).toEqual(workflow.steps.length - 1);
  });

  it('/v1/orchestrator/workflows/execute (POST) - execute workflow with post execution middleware', async () => {
    const agent = createWriterAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    const welcomeMessageTask = buildWelcomeMessageTask(agentId);

    const welcomeMessageCreatedTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(welcomeMessageTask)
      .expect(201);

    expect(welcomeMessageCreatedTaskResponse.body).toBeDefined();
    expect(welcomeMessageCreatedTaskResponse.body.data.id).toBeDefined();

    const welcomeMessageTaskId = welcomeMessageCreatedTaskResponse.body.data.id;

    const checkEntusiasmLevelTask = buildCheckMessageEntusiasmTask(agentId);

    const checkEntusiasmLevelTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(checkEntusiasmLevelTask)
      .expect(201);

    expect(checkEntusiasmLevelTaskResponse.body).toBeDefined();
    expect(checkEntusiasmLevelTaskResponse.body.data.id).toBeDefined();

    const checkEntusiasmLevelTaskId = checkEntusiasmLevelTaskResponse.body.data.id;

    const workflow = createWelcomePhraseWorkflowWithMiddleware(
      welcomeMessageTaskId,
      checkEntusiasmLevelTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const data = createFakeExecuteWorkflow(workflowExecutionId, '', MessageType.TEXT);

    const response = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send(data)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.workflowExecutionId).toBeDefined();
    expect(response.body.data.stepExecutionId).toBeDefined();
    expect(response.body.data.stepExecutionOrder).toBeDefined();
    expect(response.body.data.output).toBeDefined();
    expect(response.body.data.outputType).toBeDefined();

    expect(response.body.data.middlewaresResponse).toBeDefined();
    expect(response.body.data.middlewaresResponse['check-entusiasm-level']).toBeDefined();

    const outputBuffer = response.body.data.output;

    const outputText = Buffer.from(outputBuffer).toString('utf8');
    expect(outputText.length).toBeGreaterThan(20);
  });

  it('/v1/orchestrator/workflows/restart-current-step (POST) - restart current step in workflow', async () => {
    const agent = createTradutorAgent();

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;
    const englishTradutorTask = buildTradutorToEnglishTask(agentId);

    const createEnglishTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(englishTradutorTask)
      .expect(201);

    expect(createEnglishTradutorTaskResponse.body).toBeDefined();
    expect(createEnglishTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorEnglishTaskId = createEnglishTradutorTaskResponse.body.data.id;

    const portuguseTradutorTask = buildTradutorToPortugueseTask(agentId);

    const createPortuguseTradutorTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(portuguseTradutorTask)
      .expect(201);

    expect(createPortuguseTradutorTaskResponse.body).toBeDefined();
    expect(createPortuguseTradutorTaskResponse.body.data.id).toBeDefined();

    const tradutorPortuguseTaskId = createPortuguseTradutorTaskResponse.body.data.id;

    const workflow = createTranslationLanguageWorkflow(
      tradutorEnglishTaskId,
      tradutorPortuguseTaskId,
    );

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const startWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/start')
      .send({ workflowId: createWorkflowResponse.body.data.workflowId })
      .expect(201);

    expect(startWorkflowResponse.body).toBeDefined();
    expect(startWorkflowResponse.body.data.workflowExecutionId).toBeDefined();

    const workflowExecutionId = startWorkflowResponse.body.data.workflowExecutionId;

    const executeStepOneResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows/execute')
      .send({
        workflowExecutionId,
        params: { prompt: 'gato' },
        lang: 'en',
        messageType: MessageType.TEXT,
      })
      .expect(201);

    expect(executeStepOneResponse.body).toBeDefined();
    expect(executeStepOneResponse.body.data.workflowExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionId).toBeDefined();
    expect(executeStepOneResponse.body.data.stepExecutionOrder).toBeDefined();
    expect(executeStepOneResponse.body.data.output).toBeDefined();
    expect(executeStepOneResponse.body.data.outputType).toBeDefined();

    const stepOneResponseText = Buffer.from(executeStepOneResponse.body.data.output).toString(
      'utf8',
    );

    expect(stepOneResponseText.toLowerCase()).toContain('cat');

    const restartCurrentStepResponse = await request(app.getHttpServer())
      .post(`/api/v1/orchestrator/workflows/restart-current-step/${workflowExecutionId}`)
      .expect(201);

    expect(restartCurrentStepResponse.body).toBeDefined();
    expect(restartCurrentStepResponse.body.data.workflowExecutionId).toBeDefined();
    expect(restartCurrentStepResponse.body.data.stepExecutionId).toBeDefined();
    expect(restartCurrentStepResponse.body.data.stepExecutionOrder).toBeDefined();

    const restartCurrentStepResponseText = Buffer.from(
      executeStepOneResponse.body.data.output,
    ).toString('utf8');

    expect(restartCurrentStepResponseText.toLowerCase()).toContain('cat');
  });

  it('/v1/orchestrator/workflows/:workflowId/variables (GET) - get workflow variables', async () => {
    // Create agent with variables in backstory
    const agent = {
      role: 'variable-test-agent',
      backstory: 'I am an agent that uses {{userName}} and {{companyName}} in my responses.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    // Create tasks with variables
    const task1 = {
      description: 'Process data for {{userId}} with {{dataType}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTask1Response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task1)
      .expect(201);

    expect(createTask1Response.body).toBeDefined();
    expect(createTask1Response.body.data.id).toBeDefined();

    const task1Id = createTask1Response.body.data.id;

    const task2 = {
      description: 'Analyze results for {{projectId}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTask2Response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task2)
      .expect(201);

    expect(createTask2Response.body).toBeDefined();
    expect(createTask2Response.body.data.id).toBeDefined();

    const task2Id = createTask2Response.body.data.id;

    // Create workflow with the tasks
    const workflow = {
      name: 'variable-test-workflow',
      description: 'Workflow to test variable extraction',
      steps: [
        {
          description: 'First step with variables',
          order: 1,
          taskId: task1Id,
        },
        {
          description: 'Second step with variables',
          order: 2,
          taskId: task2Id,
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const workflowId = createWorkflowResponse.body.data.workflowId;

    // Test the variables endpoint
    const response = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${workflowId}/variables`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(Array.isArray(response.body.data)).toBe(true);

    // Check that all expected variables are present
    // Variables should only come from the FIRST step (order = 1)
    // From agent backstory: {{userName}} and {{companyName}}
    // From first task description: {{userId}} and {{dataType}}
    // Plus hardcoded: PHONE_NUMBER
    const expectedVariables = ['USERNAME', 'COMPANYNAME', 'USERID', 'DATATYPE', 'PHONE_NUMBER'];
    expectedVariables.forEach(variable => {
      expect(response.body.data).toContain(variable);
    });

    // Check that there are no unexpected variables
    expect(response.body.data.length).toBe(expectedVariables.length);
  });

  it('/v1/orchestrator/workflows/:workflowId/template-csv (GET) - download template csv', async () => {
    // Create agent with variables in backstory
    const agent = {
      role: 'variable-test-agent',
      backstory: 'I am an agent that uses {{userName}} and {{companyName}} in my responses.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    expect(createAgentResponse.body).toBeDefined();
    expect(createAgentResponse.body.data.id).toBeDefined();

    const agentId = createAgentResponse.body.data.id;

    // Create tasks with variables
    const task1 = {
      description: 'Process data for {{userId}} with {{dataType}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTask1Response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task1)
      .expect(201);

    expect(createTask1Response.body).toBeDefined();
    expect(createTask1Response.body.data.id).toBeDefined();

    const task1Id = createTask1Response.body.data.id;

    const task2 = {
      description: 'Analyze results for {{projectId}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTask2Response = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task2)
      .expect(201);

    expect(createTask2Response.body).toBeDefined();
    expect(createTask2Response.body.data.id).toBeDefined();

    const task2Id = createTask2Response.body.data.id;

    // Create workflow with the tasks
    const workflow = {
      name: 'variable-test-workflow',
      description: 'Workflow to test variable extraction',
      steps: [
        {
          description: 'First step with variables',
          order: 1,
          taskId: task1Id,
        },
        {
          description: 'Second step with variables',
          order: 2,
          taskId: task2Id,
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(createWorkflowResponse.body).toBeDefined();
    expect(createWorkflowResponse.body.data.workflowId).toBeDefined();

    const workflowId = createWorkflowResponse.body.data.workflowId;

    // Test the template csv endpoint
    const response = await request(app.getHttpServer())
      .get(`/api/v1/orchestrator/workflows/${workflowId}/template-csv`)
      .expect(200);

    expect(response.headers['content-type']).toBe('text/csv');
    expect(response.headers['content-disposition']).toBe('attachment; filename="template.csv"');
    expect(response.body).toBeDefined();

    // Convert the response buffer to string and check content
    // Variables should only come from the FIRST step (order = 1)
    // From agent backstory: {{userName}} and {{companyName}}
    // From first task description: {{userId}} and {{dataType}}
    // Plus hardcoded: PHONE_NUMBER
    const csvContent = response.text;
    expect(csvContent).toContain('USERNAME');
    expect(csvContent).toContain('COMPANYNAME');
    expect(csvContent).toContain('USERID');
    expect(csvContent).toContain('DATATYPE');
    expect(csvContent).toContain('PHONE_NUMBER');

    // Should NOT contain PROJECTID since it's from the second step
    expect(csvContent).not.toContain('PROJECTID');
  });

  function createTranslationLanguageWorkflow(
    taskTradutorToEnglish: string,
    taskTradutorToPortuguese: string,
  ) {
    return {
      name: 'translate-text-workflow',
      description:
        'This workflow is responsible for translating a text from one language to another language.',
      steps: [
        {
          description: 'Translate text to english',
          order: 1,
          taskId: taskTradutorToEnglish,
        },
        {
          description: 'Translate the text to Braziliam portuguese',
          order: 2,
          taskId: taskTradutorToPortuguese,
        },
      ],
    };
  }

  function createTradutorAgent() {
    return {
      role: 'translator',
      backstory: 'Expert in tradution of languages, specialized in texts translation.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };
  }

  function buildTradutorToEnglishTask(agentId: string) {
    const task = {
      description: `Translate the following text to english: {{prompt}}. Without provide any suggestions or comments.`,
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    return task;
  }

  function buildTradutorToPortugueseTask(agentId: string) {
    const task = {
      description: `Translate the following text to portuguese brazil: {{prompt}}. Without provide any suggestions or comments.`,
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    return task;
  }

  function createWelcomePhraseWorkflowWithMiddleware(
    welcomeMessageTaskId: string,
    checkEntusiasmLevelTaskId: string,
  ): SaveWorkflowRequestDto {
    return {
      name: 'generate-onboarding-phrase',
      description: 'Generate onboarding phrase.',
      steps: [
        {
          description: 'Welcome phrase generator.',
          order: 1,
          taskId: welcomeMessageTaskId,
          middlewares: [
            {
              category: MiddlewareToolCategory.INTEGRATION,
              taskId: checkEntusiasmLevelTaskId,
              name: 'check-entusiasm-level',
              description: 'check generated phrase entusiasm level',
              type: MiddlewareType.POST_EXECUTION,
              showOff: true,
            },
          ],
        },
      ],
    };
  }

  function createWriterAgent(): AgentDto {
    return {
      role: 'writer expert',
      backstory:
        'You are a writing expert, able to write great messages, and announcements, and able to analyze the enthusiasm level of texts.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };
  }

  function buildWelcomeMessageTask(agentId: string): TaskDto {
    return {
      description: `Write a welcome message to send in Slack announcing our new employee "Anne" who is the new software developer junior in our engineering team in our startup DigAI.`,
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };
  }

  function buildCheckMessageEntusiasmTask(agentId: string): TaskDto {
    return {
      description: `Analyze the last message of the below conversation and set the enthusiasm level of the phrase's last message between 1 to 10. Conversation in JSON format: 
      {{conversationHistory}}`,
      agent: agentId,
      responseTemplate: '{ "enthusiasmLevel": "[1-10 calculated entusiasm level]" }',
      managerAgentId: null,
    };
  }

  function createFakeExecuteWorkflow(
    workflowExecutionId: string,
    prompt: string,
    messageType: MessageType,
  ): ExecuteWorkflowRequestDto {
    return {
      workflowExecutionId,
      params: { prompt },
      lang: 'en',
      fileUrl: null,
      messageType,
    };
  }
});
