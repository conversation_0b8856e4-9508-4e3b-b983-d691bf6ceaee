require('tsconfig-paths/register');

import { INestApplication, ValidationPipe, VersioningType } from '@nestjs/common';
import { AppModule } from '@app.module';
import { NestFactory } from '@nestjs/core';

// eslint-disable-next-line no-restricted-imports
import { execSync } from 'child_process';
import { PrismaExceptionFilter } from '@common/exception/filters/prisma-exceptions.filter';
import { BusinessExceptionFilter } from '@common/exception/filters/business-exceptions.filter';
import { DynamoExceptionFilter } from '@common/exception/filters/dynamo-exceptions.filter';
import { OpenAIExceptionFilter } from '@common/exception/filters/openai-exceptions.filter';
import { AuthExceptionFilter } from '@common/exception/filters/auth-exceptions.filter';

declare global {
  interface BigInt {
    toJSON(): number;
  }
}

BigInt.prototype.toJSON = function () {
  return Number(this);
};

module.exports = async () => {
  if (process.env.NODE_ENV === 'test-local') {
    await setupTestDatabase();
  }

  const { app }: { app: INestApplication } = await createTestApp();

  global.__NEST_APP__ = app;

  app.enableShutdownHooks();
};

const createTestApp = async function createTestApp(): Promise<{
  app: INestApplication;
}> {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix('api');
  app.enableVersioning({
    type: VersioningType.URI,
  });
  app.useGlobalFilters(
    app.get(AuthExceptionFilter),
    app.get(PrismaExceptionFilter),
    app.get(BusinessExceptionFilter),
    app.get(DynamoExceptionFilter),
    app.get(OpenAIExceptionFilter),
  );

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: false, // Allow unknown properties for dynamic properties testing
      transform: true,
      forbidNonWhitelisted: false,
    }),
  );

  await app.listen(3010);

  return { app };
};

async function setupTestDatabase() {
  try {
    execSync('TZ=UTC bash scripts/run-e2e-tests.sh', { stdio: 'inherit' });
  } catch (error) {
    process.exit(1);
  }
}
