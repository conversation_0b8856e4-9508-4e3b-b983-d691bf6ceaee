import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('Metrics controller (e2e)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;
    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    authToken = accessToken;
  });

  describe('GET /v1/message-hub/metrics/answer-messages-sent', () => {
    it('should return total messages sent for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/answer-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalAnswerMessagesSent');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/answer-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });

  describe('GET /v1/message-hub/metrics/first-messages-sent', () => {
    it('should return total messages sent for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/first-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalFirstMessagesSent');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/first-messages-sent')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });

  describe('GET /v1/message-hub/metrics/messages-received', () => {
    it('should return total messages received for the given date range', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      const response = await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/messages-received')
        .query({
          dateStart,
          dateEnd,
        })
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toBe(200);
      expect(response.body.data).toHaveProperty('totalMessagesReceived');
    });

    it('should return 401 when no auth token is provided', async () => {
      const dateStart = '2024-01-01';
      const dateEnd = '2024-01-31';

      await request(app.getHttpServer())
        .get('/api/v1/message-hub/metrics/messages-received')
        .query({
          dateStart,
          dateEnd,
        })
        .expect(401);
    });
  });
});
