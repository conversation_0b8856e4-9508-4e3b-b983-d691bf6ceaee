import { CommunicationChannel } from '@common/enums';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { CustomerCommunicationChannelResponseDto } from '@message-hub/application/dto/out/customer-communication-channel-response.dto';

describe('Customer communication channel controller (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/message-hub/communication-channel (POST) - Create a customer communication channel without error', async () => {
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/communication-channel`)
      .send({
        customerId: uuidv4(),
        portfolioId: uuidv4(),
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        integrationData: {
          data: 'data',
          token: 'bearer token',
        },
      })
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data).toBeDefined();
    expect(response.body.data.id).toBeDefined();
    expect(response.body.data.customerId).toBeDefined();
    expect(response.body.data.portfolioId).toBeDefined();
    expect(response.body.data.communicationChannel).toBe(CommunicationChannel.WHATSAPPSELFHOSTED);
    expect(response.body.data.integrationData).toBeDefined();
    expect(response.body.data.integrationData.data).toBe('data');
    expect(response.body.data.integrationData.token).toBe('bearer token');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();
  });

  it('/v1/message-hub/communication-channel (POST) - Create an existing communication channel for a customer BAD REQUEST ', async () => {
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/communication-channel`)
      .send({
        customerId: uuidv4(),
        portfolioId: uuidv4(),
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        integrationData: 'non object integration data',
      })
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(400);
    expect(response.body.error).toBe('Bad Request');
    expect(response.body.message).toBeDefined();
    expect(response.body.message[0]).toBe('integrationData must be an object');
  });

  it('/v1/message-hub/communication-channel (POST) - Create an existing communication channel for a customer BAD REQUEST ', async () => {
    const createdCustomerCommunicationChannel = await createCustomerCommunicationChannel();

    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/communication-channel`)
      .send({
        customerId: createdCustomerCommunicationChannel.customerId,
        portfolioId: createdCustomerCommunicationChannel.portfolioId,
        communicationChannel: createdCustomerCommunicationChannel.communicationChannel,
        integrationData: {
          anyWay: {
            text: 'doest matter object text',
          },
        },
      })
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(400);
    expect(response.body.timestamp).toBeDefined();
    expect(response.body.message).toBeDefined();
    expect(response.body.message.length).toBe(1);
    expect(response.body.message[0]).toBe('PrismaExceptionP2002 - Esse registro já existe');
  });

  it('/v1/message-hub/communication-channel/:customerCommunicationChannelId (GET) - Retrieve an existing communication channel without errors ', async () => {
    const createdCustomerCommunicationChannel = await createCustomerCommunicationChannel();

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/communication-channel/${createdCustomerCommunicationChannel.id}`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.id).toBe(createdCustomerCommunicationChannel.id);
    expect(response.body.data.customerId).toBe(createdCustomerCommunicationChannel.customerId);
    expect(response.body.data.portfolioId).toBe(createdCustomerCommunicationChannel.portfolioId);
    expect(response.body.data.communicationChannel).toBe(
      createdCustomerCommunicationChannel.communicationChannel,
    );
    expect(response.body.data.integrationData).toBeDefined();
    expect(response.body.data.integrationData.data).toBe('data');
    expect(response.body.data.integrationData.token).toBe('bearer token');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();
  });

  it('/v1/message-hub/communication-channel//customers/:customerId (GET) - Retrieve all customer communication channels by customer ID', async () => {
    const createdCustomerCommunicationChannel = await createCustomerCommunicationChannel();
    const createdCustomerCommunicationChannel2 = await createCustomerCommunicationChannel(
      createdCustomerCommunicationChannel.customerId,
    );

    const response = await request(app.getHttpServer())
      .get(
        `/api/v1/message-hub/communication-channel/customers/${createdCustomerCommunicationChannel.customerId}`,
      )
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.length).toBe(2);
    expect(response.body.data[0].id).toBe(createdCustomerCommunicationChannel.id);
    expect(response.body.data[0].customerId).toBe(createdCustomerCommunicationChannel.customerId);
    expect(response.body.data[0].portfolioId).toBe(createdCustomerCommunicationChannel.portfolioId);
    expect(response.body.data[0].communicationChannel).toBe(
      createdCustomerCommunicationChannel.communicationChannel,
    );
    expect(response.body.data[0].integrationData).toBeDefined();
    expect(response.body.data[0].integrationData.data).toBe('data');
    expect(response.body.data[0].integrationData.token).toBe('bearer token');
    expect(response.body.data[0].createdAt).toBeDefined();
    expect(response.body.data[0].updatedAt).toBeDefined();

    expect(response.body.data[1].id).toBe(createdCustomerCommunicationChannel2.id);
    expect(response.body.data[1].customerId).toBe(createdCustomerCommunicationChannel2.customerId);
    expect(response.body.data[1].portfolioId).toBe(
      createdCustomerCommunicationChannel2.portfolioId,
    );
    expect(response.body.data[1].communicationChannel).toBe(
      createdCustomerCommunicationChannel2.communicationChannel,
    );
    expect(response.body.data[1].integrationData).toBeDefined();
    expect(response.body.data[1].integrationData.data).toBe('data');
    expect(response.body.data[1].integrationData.token).toBe('bearer token');
    expect(response.body.data[1].createdAt).toBeDefined();
    expect(response.body.data[1].updatedAt).toBeDefined();
  });

  it('/v1/message-hub/communication-channel/:customerCommunicationChannelId (DELETE) - Delete an existing communication channel without errors ', async () => {
    const createdCustomerCommunicationChannel = await createCustomerCommunicationChannel();

    const response = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/communication-channel/${createdCustomerCommunicationChannel.id}`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toBe(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.id).toBe(createdCustomerCommunicationChannel.id);
    expect(response.body.data.customerId).toBe(createdCustomerCommunicationChannel.customerId);
    expect(response.body.data.portfolioId).toBe(createdCustomerCommunicationChannel.portfolioId);
    expect(response.body.data.communicationChannel).toBe(
      createdCustomerCommunicationChannel.communicationChannel,
    );
    expect(response.body.data.integrationData).toBeDefined();
    expect(response.body.data.integrationData.data).toBe('data');
    expect(response.body.data.integrationData.token).toBe('bearer token');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();

    const responseDelete = await request(app.getHttpServer())
      .delete(`/api/v1/message-hub/communication-channel/${createdCustomerCommunicationChannel.id}`)
      .expect(200);

    expect(responseDelete.body).toBeDefined();
    expect(responseDelete.body.statusCode).toBe(200);
    expect(responseDelete.body.data).toBeDefined();
    expect(responseDelete.body.data.id).toBe(createdCustomerCommunicationChannel.id);
    expect(responseDelete.body.data.customerId).toBe(
      createdCustomerCommunicationChannel.customerId,
    );
    expect(responseDelete.body.data.portfolioId).toBe(
      createdCustomerCommunicationChannel.portfolioId,
    );
    expect(responseDelete.body.data.communicationChannel).toBe(
      createdCustomerCommunicationChannel.communicationChannel,
    );
    expect(responseDelete.body.data.integrationData).toBeDefined();
    expect(responseDelete.body.data.integrationData.data).toBe('data');
    expect(responseDelete.body.data.integrationData.token).toBe('bearer token');
    expect(responseDelete.body.data.createdAt).toBeDefined();
    expect(responseDelete.body.data.updatedAt).toBeDefined();

    const notFoundResponse = await request(app.getHttpServer())
      .get(`/api/v1/message-hub/communication-channel/${createdCustomerCommunicationChannel.id}`)
      .expect(404);

    expect(notFoundResponse.body).toBeDefined();
    expect(notFoundResponse.body.statusCode).toBe(404);
    expect(notFoundResponse.body.timestamp).toBeDefined();
    expect(notFoundResponse.body.message).toBeDefined();
    expect(notFoundResponse.body.message.length).toBe(1);
    expect(notFoundResponse.body.message[0]).toBe(
      `Customer communication channel with id ${createdCustomerCommunicationChannel.id} not found`,
    );
    expect(notFoundResponse.body.data.path).toBeDefined();
    expect(notFoundResponse.body.data.path).toBe(
      `/api/v1/message-hub/communication-channel/${createdCustomerCommunicationChannel.id}`,
    );
  });

  async function createCustomerCommunicationChannel(
    customerId?: string,
  ): Promise<CustomerCommunicationChannelResponseDto> {
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/communication-channel`)
      .send({
        customerId: customerId || uuidv4(),
        portfolioId: uuidv4(),
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        integrationData: {
          data: 'data',
          token: 'bearer token',
        },
      })
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data).toBeDefined();
    expect(response.body.data.id).toBeDefined();
    expect(response.body.data.customerId).toBe(customerId || response.body.data.customerId);
    expect(response.body.data.portfolioId).toBeDefined();
    expect(response.body.data.communicationChannel).toBe(CommunicationChannel.WHATSAPPSELFHOSTED);
    expect(response.body.data.integrationData).toBeDefined();
    expect(response.body.data.integrationData.data).toBe('data');
    expect(response.body.data.integrationData.token).toBe('bearer token');
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();

    return response.body.data;
  }
});
