import { CommunicationChannel, MessageType } from '@common/enums';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';

describe('Message controller (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/message-hub/message (POST) - send message', async () => {
    const response = await request(app.getHttpServer())
      .post(`/api/v1/message-hub/message`)
      .send({
        customerId: uuidv4(),
        to: '5511123456789',
        messageType: MessageType.TEXT,
        message: 'Hello World',
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        isFirstMessage: true,
      })
      .expect(201);

    expect(response.body).toBeDefined();
  });
});
