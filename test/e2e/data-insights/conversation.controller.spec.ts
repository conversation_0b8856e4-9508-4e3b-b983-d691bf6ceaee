import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import {
  ConversationAgentName,
  ConversationMessageType,
  ConversationType,
  LangType,
  MessageType,
  RecordStatus,
} from '@common/enums';
import { CreateConversationMessageDto } from '@data-insights/application/dto/in/create-conversation-message.dto';
import { CreateConversationDto } from '@data-insights/application/dto/in/create-conversation.dto';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('ConversationController (e2e)', () => {
  let app: INestApplication;
  let agentId: string;
  const baseUrl = '/api/v1/data-insights/conversations';
  const emailMock = '<EMAIL>';

  beforeAll(async () => {
    app = global.__NEST_APP__;

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .set('Authorization', `Bear<PERSON> ${accessToken}`)
      .send({
        role: 'hiring specialist',
        name: ConversationAgentName.HIRING_DATA_INSIGHTS,
        backstory: 'testAgent',
        llmModel: 'gpt-4o-mini',
        outputType: MessageType.TEXT,
        lang: LangType.EN_US,
      });
    agentId = body.data.id;
  });

  it('/v1/data-insights/conversations (POST) - Create Conversation', async () => {
    const conversation = createConversationFake();

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(201);

    expect(body).toBeDefined();
    expect(body.data.id).toBeDefined();
    expect(body.data.sessionId).toBeDefined();
    expect(body.data.originId).toEqual(conversation.originId);
    expect(body.data.businessUserId).toEqual(conversation.businessUserId);
    expect(body.data.agentId).toEqual(agentId);
    expect(body.data.subject).toBeNull();
    expect(body.data.type).toEqual(conversation.type);
    expect(body.data.status).toEqual(RecordStatus.ACTIVE);
    expect(body.data.createdAt).toBeDefined();
    expect(body.data.updatedAt).toBeDefined();
    expect(body.data.messages.length).toEqual(0);
  });

  it('/v1/data-insights/conversations (POST) - Create Conversation with incorrect type (BAD REQUEST)', async () => {
    const conversation = {
      businessUserId: uuidv4(),
      agentName: ConversationAgentName.TEST_DATA,
      lang: LangType.PT_BR,
      originId: uuidv4(),
      type: 'incorrect type',
    };

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(400);

    expect(body).toBeDefined();
    expect(body.statusCode).toBe(400);
    expect(body.error).toBe('Bad Request');
    expect(body.message).toBeDefined();
    expect(body.message[0]).toBe('type must be a valid ConversationType');
  });

  it('/v1/data-insights/conversations (POST) - Create conversation with no existing agent (Error)', async () => {
    const conversation: CreateConversationDto = {
      businessUserId: uuidv4(),
      agentName: ConversationAgentName.TEST_DATA,
      lang: LangType.PT_BR,
      originId: uuidv4(),
      type: ConversationType.HIRING,
    };

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(404);

    expect(body.message[0]).toEqual(
      `Agent with name ${conversation.agentName} and lang ${conversation.lang} not found`,
    );
    expect(body.data.path).toEqual(baseUrl);
  });

  it('/v1/data-insights/conversations/originId/:originId (GET) - Find conversation by originId', async () => {
    const originId = uuidv4();
    const firstConversation = createConversationFake(originId);
    const secondConversation = createConversationFake();

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    await Promise.all([
      request(app.getHttpServer())
        .post(baseUrl)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(firstConversation)
        .expect(201),

      request(app.getHttpServer())
        .post(baseUrl)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(secondConversation)
        .expect(201),
    ]);

    const { body } = await request(app.getHttpServer())
      .get(`${baseUrl}/originId/${originId}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(body).toBeDefined();
    expect(body.data.length).toEqual(1);
    expect(body.data[0].originId).toEqual(originId);
  });

  it('/v1/data-insights/conversations/:coversationId (GET) - Find conversation by id', async () => {
    const conversation = createConversationFake();

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body: createConversationBody } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(201);
    const id = createConversationBody.data.id;

    await request(app.getHttpServer())
      .post(`${baseUrl}/messages`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        sessionId: createConversationBody.data.sessionId,
        text: 'Hello',
        type: ConversationMessageType.HUMAN,
      } as CreateConversationMessageDto)
      .expect(201);

    const { body } = await request(app.getHttpServer())
      .get(`${baseUrl}/${id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    expect(body).toBeDefined();
    expect(body.data.id).toBeDefined();
    expect(body.data.sessionId).toBeDefined();
    expect(body.data.originId).toEqual(conversation.originId);
    expect(body.data.businessUserId).toEqual(conversation.businessUserId);
    expect(body.data.agentId).toBeDefined();
    expect(body.data.subject).toBeNull();
    expect(body.data.type).toEqual(conversation.type);
    expect(body.data.messages.length).toEqual(1);
    expect(body.data.messages[0].type).toEqual(ConversationMessageType.HUMAN);
    expect(body.data.messages[0].text).toEqual('Hello');
  });

  it('/v1/data-insights/conversations/:coversationId (PUT) - Update conversation subject', async () => {
    const conversation = createConversationFake();

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const createConversationResponse = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(201);
    const id = createConversationResponse.body.data.id;

    const subject = 'test update subject';

    const { body } = await request(app.getHttpServer())
      .put(`${baseUrl}/${id}/subject`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ subject })
      .expect(200);

    expect(body).toBeDefined();
    expect(body.data.id).toBeDefined();
    expect(body.data.subject).toEqual(subject);
  });

  it('/v1/data-insights/conversations/:coversationId (DELETE) - Delete conversation', async () => {
    const conversation = createConversationFake();

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(conversation)
      .expect(201);
    const id = body.data.id;

    await request(app.getHttpServer())
      .delete(`${baseUrl}/${id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(200);

    const { body: getConversationBody } = await request(app.getHttpServer())
      .delete(`${baseUrl}/${id}`)
      .set('Authorization', `Bearer ${accessToken}`)
      .expect(404);

    expect(getConversationBody.statusCode).toBe(404);
    expect(getConversationBody).toBeDefined();
    expect(getConversationBody.data).toBeDefined();
    expect(getConversationBody.message).toBeDefined();
    expect(getConversationBody.message.length).toBe(1);
    expect(getConversationBody.message[0]).toEqual(`Conversation with id ${id} not found`);
    expect(getConversationBody.data).toBeDefined();
    expect(getConversationBody.data.path).toBeDefined();
    expect(getConversationBody.data.path).toBe(`${baseUrl}/${id}`);
  });

  const createConversationFake = (originId?: string): CreateConversationDto => {
    return {
      businessUserId: uuidv4(),
      agentName: ConversationAgentName.HIRING_DATA_INSIGHTS,
      lang: LangType.EN_US,
      originId: originId || uuidv4(),
      type: ConversationType.HIRING,
    };
  };
});
