import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { INestApplication } from '@nestjs/common';
import {
  ConversationAgentName,
  ConversationMessageType,
  ConversationType,
  LangType,
} from '@common/enums';
import { CreateConversationMessageDto } from '@data-insights/application/dto/in/create-conversation-message.dto';
import { CreateConversationDto } from '@data-insights/application/dto/in/create-conversation.dto';
import { ChatDto } from '@data-insights/application/dto/in/chat.dto';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('Conversation Message (e2e)', () => {
  let app: INestApplication;
  const baseUrl = '/api/v1/data-insights/conversations/messages';
  let conversationId: string;
  const emailMock = '<EMAIL>';

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/data-insights/conversations/messages (POST) - Create conversation message', async () => {
    const message = createConversationMessageFake(ConversationMessageType.HUMAN, 'Hello');

    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body: createResponseBody } = await request(app.getHttpServer())
      .post(baseUrl)
      .set('Authorization', `Bearer ${accessToken}`)
      .send(message)
      .expect(201);

    expect(createResponseBody).toBeDefined();
    expect(createResponseBody.data.id).toBeDefined();
    expect(createResponseBody.data.messages.length).toEqual(1);
    expect(createResponseBody.data.messages[0].type).toEqual(message.type);
    expect(createResponseBody.data.messages[0].text).toEqual(message.text);
  });

  it('/v1/data-insights/conversations/messages/chat (POST) - Send a message', async () => {
    const { accessToken } = await getAuthCredentials(app, emailMock, 'P@ssw0rd123');

    const { body } = await request(app.getHttpServer())
      .post('/api/v1/data-insights/conversations')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        businessUserId: uuidv4(),
        agentName: ConversationAgentName.HIRING_DATA_INSIGHTS,
        lang: LangType.PT_BR,
        originId: uuidv4(),
        type: ConversationType.HIRING,
      } as CreateConversationDto);
    conversationId = body.data.id;

    const { body: chatResponseBody } = await request(app.getHttpServer())
      .post(`${baseUrl}/chat`)
      .set('Authorization', `Bearer ${accessToken}`)
      .send({
        conversationId,
        input: 'Hello chat',
      } as ChatDto)
      .expect(201);

    expect(chatResponseBody).toBeDefined();
    expect(chatResponseBody.data?.text).toBeDefined();
    expect(typeof chatResponseBody.data?.text).toEqual('string');
    expect(chatResponseBody.data.type).toEqual(ConversationMessageType.AI);
  });

  const createConversationMessageFake = (
    type: ConversationMessageType,
    text: string,
    sessionId?: string,
  ): CreateConversationMessageDto => {
    return {
      sessionId: sessionId || uuidv4(),
      type,
      text,
    };
  };
});
