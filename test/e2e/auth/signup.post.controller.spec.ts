import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { UserAccountDto } from '@auth/application/dto/in/user-account.dto';
import { AccountRole, AccountStatus, UserRoleInAccount, UserStatus } from '@common/enums';

describe('UserController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  function customerDataFake(whatsappPhone: string = '*************'): any {
    return {
      whatsappPhone,
      name: 'Empresa Fictícia LTDA',
      cnpj: '12.345.678/0001-90',
      phone: '+*************',
      segment: 'collectcash',
    };
  }

  const userData = {
    email: '<EMAIL>',
    firstname: '<PERSON>',
    lastname: '<PERSON>',
    password: 'Senha123@',
    passwordConfirmation: 'Senha123@',
  };

  const accountData = {
    nickname: 'empresa_ficticia',
  };

  it('/v1/auth/users/signup (POST) - Create new user and account', async () => {
    const customerData = customerDataFake();
    const fakeUserAccountDto: UserAccountDto = {
      customerData,
      accountData,
      userData,
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/users/signup')
      .send(fakeUserAccountDto)
      .expect(201);

    const savedCustomer = {
      id: expect.any(String),
      name: fakeUserAccountDto.customerData.name,
      cnpj: fakeUserAccountDto.customerData.cnpj,
      email: fakeUserAccountDto.userData.email,
      phone: fakeUserAccountDto.customerData.phone,
      whatsappPhone: fakeUserAccountDto.customerData.whatsappPhone,
      segment: fakeUserAccountDto.customerData.segment,
    };

    const savedAccount = {
      id: expect.any(String),
      name: fakeUserAccountDto.accountData.nickname,
      role: AccountRole.BASIC,
      status: AccountStatus.ACTIVE,
      customerId: expect.any(String),
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    };

    const savedUser = {
      id: expect.any(String),
      email: fakeUserAccountDto.userData.email,
      firstname: fakeUserAccountDto.userData.firstname,
      lastname: fakeUserAccountDto.userData.lastname,
      roleInAccount: UserRoleInAccount.ADMIN,
      accountId: expect.any(String),
      status: UserStatus.ACTIVE,
      createdAt: expect.any(String),
      updatedAt: expect.any(String),
    };

    expect(response.body).toBeDefined();
    expect(response.body.data).toBeDefined();
    expect(response.body.data.customer).toBeDefined();
    expect(response.body.data.account).toBeDefined();
    expect(response.body.data.user).toBeDefined();

    expect(response.body.data.customer).toMatchObject(savedCustomer);

    expect(response.body.data.account).toMatchObject(savedAccount);

    expect(response.body.data.user).toMatchObject(savedUser);

    expect(response.body.data.account.customerId).toBe(response.body.data.customer.id);
    expect(response.body.data.user.accountId).toBe(response.body.data.account.id);
  });

  it('/v1/auth/users/signup (POST) - Create User with existing email (BAD REQUEST)', async () => {
    const customerData = customerDataFake('*************');
    const fakeUserAccountDto: UserAccountDto = {
      customerData,
      accountData: {
        nickname: 'empresa_ficticia_test',
      },
      userData: { ...userData, email: '<EMAIL>' },
    };

    await request(app.getHttpServer())
      .post('/api/v1/auth/users/signup')
      .send(fakeUserAccountDto)
      .expect(201);

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/users/signup')
      .send(fakeUserAccountDto)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.message).toBeDefined();
    expect(response.body.message.length).toBeGreaterThan(0);
    expect(response.body.statusCode).toEqual(400);
    expect(response.body.message[0]).toMatch(/PrismaExceptionP2002 - Esse registro já existe/);
  });
});
