import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('SessionController (e2e)', () => {
  let app: INestApplication;
  let accessJWTToken: string;
  let refreshJWTToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;

    const { accessToken, refreshToken } = await getAuthCredentials(
      app,
      '<EMAIL>',
      'password123',
    );
    accessJWTToken = accessToken;
    refreshJWTToken = refreshToken;
  });

  it('/v1/auth/session/refresh-access-token (POST) - refresh token successfully', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/refresh-access-token')
      .set('Authorization', `Bearer ${refreshJWTToken}`)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(201);
    expect(response.body.data).toBeDefined();
    expect(response.body.data).toEqual('Access token successfully refreshed');

    const cookies = response.headers['set-cookie'];
    expect(cookies).toBeDefined();

    const cookiesArray = Array.isArray(cookies) ? cookies : [cookies];
    expect(cookiesArray.length).toBeGreaterThan(0);

    const accessTokenCookie = cookiesArray.find(cookie => cookie.startsWith('digai.accessToken='));

    expect(accessTokenCookie).toBeDefined();

    expect(accessTokenCookie).toMatch(/HttpOnly/);

    // Verify accessToken is different from the previous one
    const accessTokenResponse = accessTokenCookie.split(';')[0].split('=')[1];
    expect(accessTokenResponse).not.toEqual(accessJWTToken);
  });
});
