import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { LoginDto } from '@auth/application/dto/in/login.dto';
import { AuthExceptionStatus } from '@common/exception/types/AuthException';
import { logger } from '@edutalent/commons-sdk';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('UserController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/auth/session/login (POST) - Get UNAUTHORIZED because user does not exist', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'nonePaswword',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(401);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(401);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.USER_NOT_FOUND);
    expect(response.body.message).toEqual([
      'Invalid credentials. Check that your email and password are correct.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get UNAUTHORIZED because password is wrong', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'wrongPassword',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(401);

    logger.error(`Get UNAUTHORIZED because password is wrong: ${JSON.stringify(response.body)}`);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(401);
    expect(response.body.data.errorStatus).toEqual(
      AuthExceptionStatus.EMAIL_AND_PASSWORD_DO_NOT_MATCH,
    );
    expect(response.body.message).toEqual([
      'Invalid credentials. Check that your email and password are correct.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get UNAUTHORIZED because user is SUSPENDED', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(401);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(401);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.SUSPENDED_ACCESS);
    expect(response.body.message).toEqual([
      'Your account has been suspended. Please contact support.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get BAD REQUEST because user is PENDING', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(400);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.PENDING_CONFIRMATION);
    expect(response.body.message).toEqual([
      'Your account is pending confirmation. Please check your email.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get BAD REQUEST because user is DELETED', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(400);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.DELETED_ACCOUNT);
    expect(response.body.message).toEqual([
      'We could not determine the status of your account. Please contact support.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get INTERNAL SERVER ERROR because user status is UNKNOWN', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(500);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(500);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.UNKNOWN_STATUS);
    expect(response.body.message).toEqual([
      'We could not determine the status of your account. Please contact support.',
    ]);
  });

  it('/v1/auth/session/login (POST) - Get NOT FOUND because account user association was not found', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(404);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(404);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.ACCOUNT_NOT_FOUND);
    expect(response.body.message).toEqual(['Business account not found. Please contact support.']);
  });

  it('/v1/auth/session/login (POST) - Get BAD_REQUEST because account user association is not active', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(400);
    expect(response.body.data.errorStatus).toEqual(AuthExceptionStatus.ACCOUNT_INACTIVE);
    expect(response.body.message).toEqual(['Inactive account. Please contact support.']);
  });

  it('/v1/auth/session/login (POST) - login successfully', async () => {
    const login: LoginDto = {
      email: '<EMAIL>',
      password: 'password123',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login')
      .send(login)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(201);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.userId).toEqual(expect.any(String));
    expect(response.body.data.email).toEqual('<EMAIL>');
    expect(response.body.data.firstname).toEqual('John');
    expect(response.body.data.lastname).toEqual('Doe');

    const cookies = response.headers['set-cookie'];
    expect(cookies).toBeDefined();

    const cookiesArray = Array.isArray(cookies) ? cookies : [cookies];
    expect(cookiesArray.length).toBeGreaterThan(0);

    const accessTokenCookie = cookiesArray.find(cookie => cookie.startsWith('digai.accessToken='));
    const refreshTokenCookie = cookiesArray.find(cookie =>
      cookie.startsWith('digai.refreshToken='),
    );

    expect(accessTokenCookie).toBeDefined();
    expect(refreshTokenCookie).toBeDefined();

    expect(accessTokenCookie).toMatch(/HttpOnly/);
    expect(refreshTokenCookie).toMatch(/HttpOnly/);
  });

  it('/v1/auth/session/login/email-only (POST) - login with only email successfully', async () => {
    const { accessToken } = await getAuthCredentials(
      app,
      '<EMAIL>',
      'P@ssw0rd123',
    );

    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/login/email-only')
      .set('Authorization', `Bearer ${accessToken}`)
      .send({ email: '<EMAIL>' })
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(201);
    expect(response.body.data).toBeDefined();
    expect(response.body.data.userId).toEqual(expect.any(String));
    expect(response.body.data.email).toEqual('<EMAIL>');
    expect(response.body.data.firstname).toEqual('John');
    expect(response.body.data.lastname).toEqual('Doe');

    const cookies = response.headers['set-cookie'];
    expect(cookies).toBeDefined();

    const cookiesArray = Array.isArray(cookies) ? cookies : [cookies];
    expect(cookiesArray.length).toBeGreaterThan(0);

    const accessTokenCookie = cookiesArray.find(cookie => cookie.startsWith('digai.accessToken='));
    const refreshTokenCookie = cookiesArray.find(cookie =>
      cookie.startsWith('digai.refreshToken='),
    );

    expect(accessTokenCookie).toBeDefined();
    expect(refreshTokenCookie).toBeDefined();

    expect(accessTokenCookie).toMatch(/HttpOnly/);
    expect(refreshTokenCookie).toMatch(/HttpOnly/);
  });
});
