import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('SessionController (e2e)', () => {
  let app: INestApplication;
  let accessJWTToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;

    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    accessJWTToken = accessToken;
  });

  it('/v1/auth/session/logout (POST) - logout successfully', async () => {
    const response = await request(app.getHttpServer())
      .post('/api/v1/auth/session/logout')
      .set('Authorization', `Bearer ${accessJWTToken}`)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(201);
    expect(response.body.data).toBeDefined();
    expect(response.body.data).toEqual('User successfully logged out');

    // Verify that the cookies are cleared
    const setCookieHeader = response.headers['set-cookie'];

    expect(
      setCookieHeader.includes('digai.accessToken=;') ||
        !setCookieHeader.includes('digai.accessToken'),
    ).toBe(true);

    expect(
      setCookieHeader.includes('digai.refreshToken=;') ||
        !setCookieHeader.includes('digai.refreshToken'),
    ).toBe(true);
  });
});
