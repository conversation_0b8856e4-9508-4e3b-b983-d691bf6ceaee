import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('SessionController (e2e)', () => {
  let app: INestApplication;
  let refreshJWTToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;

    const { refreshToken } = await getAuthCredentials(app, '<EMAIL>', 'password123');
    refreshJWTToken = refreshToken;
  });

  it('/v1/auth/session/token-is-valid (GET) - Get true', async () => {
    const response = await request(app.getHttpServer())
      .get('/api/v1/auth/session/token-is-valid')
      .set('Authorization', `Bearer ${refreshJWTToken}`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(200);
    expect(response.body.data).toBeDefined();
    expect(response.body.data).toMatchObject({ valid: true });
  });
});
