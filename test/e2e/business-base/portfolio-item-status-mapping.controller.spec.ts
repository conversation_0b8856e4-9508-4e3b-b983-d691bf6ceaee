import { PortfolioItemStatusMappingDto } from '@business-base/application/dto/in/portfolio-item-status-mapping.dto';
import { INestApplication } from '@nestjs/common';
import * as uuid from 'uuid';
import request from 'supertest';
import { PortfolioItemStatus } from '@common/enums';

describe('PortfolioItemStausMapping (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/business-base/portfolio/items/status-mapping (POST) - Create Portfolio Item Status Mapping', async () => {
    const portfolioItemStatusMapping = createPortfolioItemStatusMappingFake();

    const response = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMapping)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.workflowId).toBeDefined();
    expect(response.body.data.workflowId).toEqual(portfolioItemStatusMapping.workflowId);
    expect(response.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMapping.postExecutionResponse,
    );
    expect(response.body.data.responseKey).toEqual(portfolioItemStatusMapping.responseKey);
    expect(response.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMapping.portfolioItemStatus,
    );
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();
  });

  it('/v1/business-base/portfolio/items/status-mapping (POST) - Create Portfolio Item Status Mapping that alread exists (BAD REQUEST)', async () => {
    const portfolioItemStatusMapping = createPortfolioItemStatusMappingFake();

    const response = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMapping)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.workflowId).toBeDefined();
    expect(response.body.data.workflowId).toEqual(portfolioItemStatusMapping.workflowId);
    expect(response.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMapping.postExecutionResponse,
    );
    expect(response.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMapping.portfolioItemStatus,
    );
    expect(response.body.data.createdAt).toBeDefined();
    expect(response.body.data.updatedAt).toBeDefined();

    const responseBadResquest = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMapping)
      .expect(400);

    expect(responseBadResquest.body).toBeDefined();
    expect(responseBadResquest.body.statusCode).toEqual(400);
    expect(responseBadResquest.body.message).toBeDefined();
    expect(responseBadResquest.body.message.length).toBeGreaterThan(0);
    expect(responseBadResquest.body.message[0]).toEqual(
      'PrismaExceptionP2002 - Esse registro já existe',
    );
    expect(responseBadResquest.body.data).toBeDefined();
    expect(responseBadResquest.body.data.path).toEqual(
      '/api/v1/business-base/portfolio/items/status-mapping',
    );
  });

  it('/v1/business-base/portfolio/items/status-mapping (POST) - Create Portfolio Item Status Mapping with a non existing ENUM (BAD REQUEST)', async () => {
    const portfolioItemStatusMapping = {
      workflowId: uuid.v4(),
      postExecutionResponse: 'postExecutionResponse',
      responseKey: 'responseKey',
      portfolioItemStatus: 'NOT_EXISTENT_ENUM',
    };

    const response = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMapping)
      .expect(400);

    expect(response.body).toBeDefined();
    expect(response.body.statusCode).toEqual(400);
    expect(response.body.message).toBeDefined();
    expect(response.body.message.length).toBeGreaterThan(0);
    expect(response.body.message[0]).toEqual(
      'portfolioItemStatus must be a valid PortfolioItemStatus',
    );
  });

  it('/api/v1/business-base/portfolio/items/status-mapping/:workflowId - List PortfolioItemStatusMapping by workflow id', async () => {
    const portfolioItemStatusMappingOne = createPortfolioItemStatusMappingFake();
    const portfolioItemStatusMappingTwo = {
      ...createPortfolioItemStatusMappingFake(PortfolioItemStatus.SUCCEED),
      workflowId: portfolioItemStatusMappingOne.workflowId,
    };

    const createdPortfolioItemStatusMappingOne = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMappingOne)
      .expect(201);

    const createdPortfolioItemStatusMappingTwo = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMappingTwo)
      .expect(201);

    expect(createdPortfolioItemStatusMappingOne.body).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.workflowId).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.workflowId).toEqual(
      portfolioItemStatusMappingOne.workflowId,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.responseKey).toEqual(
      portfolioItemStatusMappingOne.responseKey,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMappingOne.portfolioItemStatus,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.createdAt).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.updatedAt).toBeDefined();

    expect(createdPortfolioItemStatusMappingTwo.body).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.workflowId).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.workflowId).toEqual(
      portfolioItemStatusMappingTwo.workflowId,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.responseKey).toEqual(
      portfolioItemStatusMappingTwo.responseKey,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMappingTwo.portfolioItemStatus,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.createdAt).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.updatedAt).toBeDefined();

    const listPortfolioItemStatusMappingResponse = await request(app.getHttpServer())
      .get(
        '/api/v1/business-base/portfolio/items/status-mapping/' +
          portfolioItemStatusMappingOne.workflowId,
      )
      .expect(200);

    expect(listPortfolioItemStatusMappingResponse.body).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data.length).toEqual(2);
    expect(listPortfolioItemStatusMappingResponse.body.data[0].workflowId).toEqual(
      portfolioItemStatusMappingOne.workflowId,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingOne.portfolioItemStatus,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].createdAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[0].updatedAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[1].workflowId).toEqual(
      portfolioItemStatusMappingTwo.workflowId,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].postExecutionResponse).toEqual(
      portfolioItemStatusMappingTwo.postExecutionResponse,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].responseKey).toEqual(
      portfolioItemStatusMappingTwo.responseKey,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingTwo.portfolioItemStatus,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].createdAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[1].updatedAt).toBeDefined();
  });

  it('/api/v1/business-base/portfolio/items/status-mapping/:workflowId (DELETE) - Delete portfolioItemStatusMapping by workflowId', async () => {
    const portfolioItemStatusMappingOne = createPortfolioItemStatusMappingFake();
    const portfolioItemStatusMappingTwo = {
      ...createPortfolioItemStatusMappingFake(PortfolioItemStatus.SUCCEED),
      workflowId: portfolioItemStatusMappingOne.workflowId,
    };

    const createdPortfolioItemStatusMappingOne = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMappingOne)
      .expect(201);

    const createdPortfolioItemStatusMappingTwo = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio/items/status-mapping')
      .send(portfolioItemStatusMappingTwo)
      .expect(201);

    expect(createdPortfolioItemStatusMappingOne.body).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.workflowId).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.workflowId).toEqual(
      portfolioItemStatusMappingOne.workflowId,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.responseKey).toEqual(
      portfolioItemStatusMappingOne.responseKey,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMappingOne.portfolioItemStatus,
    );
    expect(createdPortfolioItemStatusMappingOne.body.data.createdAt).toBeDefined();
    expect(createdPortfolioItemStatusMappingOne.body.data.updatedAt).toBeDefined();

    expect(createdPortfolioItemStatusMappingTwo.body).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.workflowId).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.workflowId).toEqual(
      portfolioItemStatusMappingTwo.workflowId,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.postExecutionResponse).toEqual(
      portfolioItemStatusMappingTwo.postExecutionResponse,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.responseKey).toEqual(
      portfolioItemStatusMappingTwo.responseKey,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.portfolioItemStatus).toEqual(
      portfolioItemStatusMappingTwo.portfolioItemStatus,
    );
    expect(createdPortfolioItemStatusMappingTwo.body.data.createdAt).toBeDefined();
    expect(createdPortfolioItemStatusMappingTwo.body.data.updatedAt).toBeDefined();

    const listPortfolioItemStatusMappingResponse = await request(app.getHttpServer())
      .get(
        '/api/v1/business-base/portfolio/items/status-mapping/' +
          portfolioItemStatusMappingOne.workflowId,
      )
      .expect(200);

    expect(listPortfolioItemStatusMappingResponse.body).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data.length).toEqual(2);
    expect(listPortfolioItemStatusMappingResponse.body.data[0].workflowId).toEqual(
      portfolioItemStatusMappingOne.workflowId,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].responseKey).toEqual(
      portfolioItemStatusMappingOne.responseKey,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingOne.portfolioItemStatus,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[0].createdAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[0].updatedAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[1].workflowId).toEqual(
      portfolioItemStatusMappingTwo.workflowId,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].postExecutionResponse).toEqual(
      portfolioItemStatusMappingTwo.postExecutionResponse,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].responseKey).toEqual(
      portfolioItemStatusMappingTwo.responseKey,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingTwo.portfolioItemStatus,
    );
    expect(listPortfolioItemStatusMappingResponse.body.data[1].createdAt).toBeDefined();
    expect(listPortfolioItemStatusMappingResponse.body.data[1].updatedAt).toBeDefined();

    const deletePortfolioItemStatusMappingresponse = await request(app.getHttpServer())
      .delete(
        '/api/v1/business-base/portfolio/items/status-mapping/' +
          portfolioItemStatusMappingOne.workflowId,
      )
      .expect(200);

    expect(deletePortfolioItemStatusMappingresponse.body).toBeDefined();
    expect(deletePortfolioItemStatusMappingresponse.body.data).toBeDefined();
    expect(deletePortfolioItemStatusMappingresponse.body.data.length).toEqual(2);
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].workflowId).toEqual(
      portfolioItemStatusMappingOne.workflowId,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].postExecutionResponse).toEqual(
      portfolioItemStatusMappingOne.postExecutionResponse,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].responseKey).toEqual(
      portfolioItemStatusMappingOne.responseKey,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingOne.portfolioItemStatus,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].createdAt).toBeDefined();
    expect(deletePortfolioItemStatusMappingresponse.body.data[0].updatedAt).toBeDefined();
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].workflowId).toEqual(
      portfolioItemStatusMappingTwo.workflowId,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].postExecutionResponse).toEqual(
      portfolioItemStatusMappingTwo.postExecutionResponse,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].responseKey).toEqual(
      portfolioItemStatusMappingTwo.responseKey,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].portfolioItemStatus).toEqual(
      portfolioItemStatusMappingTwo.portfolioItemStatus,
    );
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].createdAt).toBeDefined();
    expect(deletePortfolioItemStatusMappingresponse.body.data[1].updatedAt).toBeDefined();

    const listPortfolioItemStatusMappingResponseAgain = await request(app.getHttpServer())
      .get(
        '/api/v1/business-base/portfolio/items/status-mapping/' +
          portfolioItemStatusMappingOne.workflowId,
      )
      .expect(200);

    expect(listPortfolioItemStatusMappingResponseAgain.body).toBeDefined();
    expect(listPortfolioItemStatusMappingResponseAgain.body.data).toBeDefined();
    expect(listPortfolioItemStatusMappingResponseAgain.body.data.length).toEqual(0);
  });

  function createPortfolioItemStatusMappingFake(
    portfolioItemStaus: PortfolioItemStatus = PortfolioItemStatus.PENDING,
  ): PortfolioItemStatusMappingDto {
    return {
      workflowId: uuid.v4(),
      postExecutionResponse: 'postExecutionResponse',
      responseKey: 'responseKey',
      portfolioItemStatus: portfolioItemStaus,
    };
  }
});
