import { CustomerDto } from '@business-base/application/dto/customer.dto';
import { UpdateCustomerDto } from '@business-base/application/dto/in/update-customer.dto';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';

describe('Customer (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  it('/v1/business-base/customers (POST) - Create Customer', async () => {
    const customer = createCustomerFake();

    const response = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(response.body).toBeDefined();
    expect(response.body.data.id).toBeDefined();
    expect(response.body.data.name).toEqual(customer.name);
    expect(response.body.data.cnpj).toEqual(customer.cnpj);
    expect(response.body.data.email).toEqual(customer.email);
    expect(response.body.data.phone).toEqual(customer.phone);
    expect(response.body.data.whatsappPhone).toEqual(customer.whatsappPhone);
  });

  it('/v1/business-base/customers (POST) - Create Customer with existing email (BAD REQUEST)', async () => {
    const customer = createCustomerFake('<EMAIL>', '5522994345679');

    const customerCreationResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(customerCreationResponse.body).toBeDefined();
    expect(customerCreationResponse.body.data.id).toBeDefined();
    expect(customerCreationResponse.body.data.name).toEqual(customer.name);
    expect(customerCreationResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(customerCreationResponse.body.data.email).toEqual(customer.email);
    expect(customerCreationResponse.body.data.phone).toEqual(customer.phone);
    expect(customerCreationResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);

    const customer2 = { ...customer, whatsappPhone: '5522994345680' };
    const secondCreationResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer2)
      .expect(400);

    expect(secondCreationResponse.body).toBeDefined();
    expect(secondCreationResponse.body.statusCode).toEqual(400);
    expect(secondCreationResponse.body.message).toBeDefined();
    expect(secondCreationResponse.body.message.length).toBeGreaterThan(0);
    expect(secondCreationResponse.body.message[0]).toEqual(
      'PrismaExceptionP2002 - Esse registro já existe',
    );
    expect(secondCreationResponse.body.data).toBeDefined();
    expect(secondCreationResponse.body.data.path).toEqual('/api/v1/business-base/customers');
  });

  it('/v1/negotation/customers/:customerId (GET) - Find customer By Id', async () => {
    const customer = createCustomerFake('<EMAIL>', '5511912345680');

    const createdCustomerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(createdCustomerResponse.body).toBeDefined();
    expect(createdCustomerResponse.body.data.id).toBeDefined();

    const customerId = createdCustomerResponse.body.data.id;

    const customerGetResponse = await request(app.getHttpServer())
      .get('/api/v1/business-base/customers/' + customerId)
      .expect(200);

    expect(customerGetResponse.body).toBeDefined();
    expect(customerGetResponse.body.data.name).toEqual(createdCustomerResponse.body.data.name);
    expect(customerGetResponse.body.data.cnpj).toEqual(createdCustomerResponse.body.data.cnpj);
    expect(customerGetResponse.body.data.email).toEqual(createdCustomerResponse.body.data.email);
    expect(customerGetResponse.body.data.phone).toEqual(createdCustomerResponse.body.data.phone);
    expect(customerGetResponse.body.data.whatsappPhone).toEqual(
      createdCustomerResponse.body.data.whatsappPhone,
    );
    expect(customerGetResponse.body.data.id).toEqual(createdCustomerResponse.body.data.id);
  });

  it('/v1/business-base/customers/:customerId (PUT) - Update Customer', async () => {
    const customer = createCustomerFake('<EMAIL>', '5511912345681');

    const createdCustomerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(createdCustomerResponse.body).toBeDefined();
    expect(createdCustomerResponse.body.data.id).toBeDefined();

    const customerId = createdCustomerResponse.body.data.id;

    const updatedCustomer: UpdateCustomerDto = {
      name: 'updated name',
      email: '<EMAIL>',
    };

    const updatedCustomerResponse = await request(app.getHttpServer())
      .put('/api/v1/business-base/customers/' + customerId)
      .send(updatedCustomer)
      .expect(200);

    expect(updatedCustomerResponse.body).toBeDefined();
    expect(updatedCustomerResponse.body.data.name).toEqual(updatedCustomer.name);
    expect(updatedCustomerResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(updatedCustomerResponse.body.data.email).toEqual(updatedCustomer.email);
    expect(updatedCustomerResponse.body.data.phone).toEqual(customer.phone);
    expect(updatedCustomerResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);
    expect(updatedCustomerResponse.body.data.id).toEqual(customerId);

    const customerGetResponse = await request(app.getHttpServer())
      .get('/api/v1/business-base/customers/' + customerId)
      .expect(200);

    expect(customerGetResponse.body).toBeDefined();
    expect(customerGetResponse.body.data.name).toEqual(updatedCustomer.name);
    expect(customerGetResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(customerGetResponse.body.data.email).toEqual(updatedCustomer.email);
    expect(customerGetResponse.body.data.phone).toEqual(customer.phone);
    expect(customerGetResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);
    expect(customerGetResponse.body.data.id).toEqual(customerId);
  });

  it('/v1/business-base/customers/:customerId (DELETE) - Delete Customer', async () => {
    const customer = createCustomerFake('<EMAIL>', '5511912345682');

    const createdCustomerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(createdCustomerResponse.body).toBeDefined();
    expect(createdCustomerResponse.body.data.id).toBeDefined();

    const customerId = createdCustomerResponse.body.data.id;

    await request(app.getHttpServer())
      .delete('/api/v1/business-base/customers/' + customerId)
      .expect(200);

    await request(app.getHttpServer())
      .get('/api/v1/business-base/customers/' + customerId)
      .expect(404);
  });

  it('/v1/business-base/customers/:customerId/workflows (POST) - Create Customer Workflows', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const workflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(workflowResponse.body).toBeDefined();
    expect(workflowResponse.body.data.workflowId).toBeDefined();
    expect(workflowResponse.body.data.name).toEqual(workflow.name);

    const customer = createCustomerFake('<EMAIL>', '5511912345683');

    const customerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(customerResponse.body).toBeDefined();
    expect(customerResponse.body.data.id).toBeDefined();
    expect(customerResponse.body.data.name).toEqual(customer.name);
    expect(customerResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(customerResponse.body.data.email).toEqual(customer.email);
    expect(customerResponse.body.data.phone).toEqual(customer.phone);
    expect(customerResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);

    const customerWorkflowResponse = await request(app.getHttpServer())
      .post(
        `/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows/${workflowResponse.body.data.workflowId}`,
      )
      .expect(201);

    expect(customerWorkflowResponse.body).toBeDefined();
    expect(customerWorkflowResponse.body.data.id).toBeDefined();
    expect(customerWorkflowResponse.body.data.customerId).toEqual(customerResponse.body.data.id);
    expect(customerWorkflowResponse.body.data.workflowId).toEqual(
      workflowResponse.body.data.workflowId,
    );
    expect(customerWorkflowResponse.body.data.workflowName).toEqual(
      workflowResponse.body.data.name,
    );
    expect(customerWorkflowResponse.body.data.workflowDescription).toEqual(
      workflowResponse.body.data.description,
    );
    expect(customerWorkflowResponse.body.data.createdAt).toBeDefined();
    expect(customerWorkflowResponse.body.data.updatedAt).toBeDefined();
  });

  it('/v1/business-base/customers/:customerId/workflows (POST) - Create Customer Workflows that already exists (BAD REQUEST)', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const workflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(workflowResponse.body).toBeDefined();
    expect(workflowResponse.body.data.workflowId).toBeDefined();
    expect(workflowResponse.body.data.name).toEqual(workflow.name);

    const customer = createCustomerFake('<EMAIL>', '5511912345684');

    const customerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(customerResponse.body).toBeDefined();
    expect(customerResponse.body.data.id).toBeDefined();
    expect(customerResponse.body.data.name).toEqual(customer.name);
    expect(customerResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(customerResponse.body.data.email).toEqual(customer.email);
    expect(customerResponse.body.data.phone).toEqual(customer.phone);
    expect(customerResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);

    const customerWorkflowResponse = await request(app.getHttpServer())
      .post(
        `/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows/${workflowResponse.body.data.workflowId}`,
      )
      .expect(201);

    expect(customerWorkflowResponse.body).toBeDefined();
    expect(customerWorkflowResponse.body.data.id).toBeDefined();
    expect(customerWorkflowResponse.body.data.customerId).toEqual(customerResponse.body.data.id);
    expect(customerWorkflowResponse.body.data.workflowId).toEqual(
      workflowResponse.body.data.workflowId,
    );
    expect(customerWorkflowResponse.body.data.workflowName).toEqual(
      workflowResponse.body.data.name,
    );
    expect(customerWorkflowResponse.body.data.workflowDescription).toEqual(
      workflowResponse.body.data.description,
    );
    expect(customerWorkflowResponse.body.data.createdAt).toBeDefined();
    expect(customerWorkflowResponse.body.data.updatedAt).toBeDefined();

    const customerWorkflowResponseAlreadyExists = await request(app.getHttpServer())
      .post(
        `/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows/${workflowResponse.body.data.workflowId}`,
      )
      .expect(400);

    expect(customerWorkflowResponseAlreadyExists.body).toBeDefined();
    expect(customerWorkflowResponseAlreadyExists.body.statusCode).toEqual(400);
    expect(customerWorkflowResponseAlreadyExists.body.message).toBeDefined();
    expect(customerWorkflowResponseAlreadyExists.body.message.length).toBeGreaterThan(0);
    expect(customerWorkflowResponseAlreadyExists.body.message[0]).toEqual(
      'PrismaExceptionP2002 - Esse registro já existe',
    );
    expect(customerWorkflowResponseAlreadyExists.body.data).toBeDefined();
    expect(customerWorkflowResponseAlreadyExists.body.data.path).toEqual(
      `/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows/${workflowResponse.body.data.workflowId}`,
    );
  });

  it('/v1/business-base/customers/:customerId/workflows (GET) - List Customer Workflows', async () => {
    const workflow = createTranslationLanguageWorkflow(uuidv4(), uuidv4());

    const workflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    expect(workflowResponse.body).toBeDefined();
    expect(workflowResponse.body.data.workflowId).toBeDefined();
    expect(workflowResponse.body.data.name).toEqual(workflow.name);

    const customer = createCustomerFake('<EMAIL>', '5511912345685');

    const customerResponse = await request(app.getHttpServer())
      .post('/api/v1/business-base/customers')
      .send(customer)
      .expect(201);

    expect(customerResponse.body).toBeDefined();
    expect(customerResponse.body.data.id).toBeDefined();
    expect(customerResponse.body.data.name).toEqual(customer.name);
    expect(customerResponse.body.data.cnpj).toEqual(customer.cnpj);
    expect(customerResponse.body.data.email).toEqual(customer.email);
    expect(customerResponse.body.data.phone).toEqual(customer.phone);
    expect(customerResponse.body.data.whatsappPhone).toEqual(customer.whatsappPhone);

    const customerWorkflowResponse = await request(app.getHttpServer())
      .post(
        `/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows/${workflowResponse.body.data.workflowId}`,
      )
      .expect(201);

    expect(customerWorkflowResponse.body).toBeDefined();
    expect(customerWorkflowResponse.body.data.id).toBeDefined();
    expect(customerWorkflowResponse.body.data.customerId).toEqual(customerResponse.body.data.id);
    expect(customerWorkflowResponse.body.data.workflowId).toEqual(
      workflowResponse.body.data.workflowId,
    );
    expect(customerWorkflowResponse.body.data.workflowName).toEqual(
      workflowResponse.body.data.name,
    );
    expect(customerWorkflowResponse.body.data.workflowDescription).toEqual(
      workflowResponse.body.data.description,
    );
    expect(customerWorkflowResponse.body.data.createdAt).toBeDefined();
    expect(customerWorkflowResponse.body.data.updatedAt).toBeDefined();

    const customerWorkflowsResponse = await request(app.getHttpServer())
      .get(`/api/v1/business-base/customers/${customerResponse.body.data.id}/workflows`)
      .expect(200);

    expect(customerWorkflowsResponse.body).toBeDefined();
    expect(customerWorkflowsResponse.body.data).toBeDefined();
    expect(customerWorkflowsResponse.body.data.length).toEqual(1);
    expect(customerWorkflowsResponse.body.data[0].id).toBeDefined();
    expect(customerWorkflowsResponse.body.data[0].customerId).toEqual(
      customerResponse.body.data.id,
    );
    expect(customerWorkflowsResponse.body.data[0].workflowId).toEqual(
      workflowResponse.body.data.workflowId,
    );
    expect(customerWorkflowsResponse.body.data[0].workflowName).toEqual(
      workflowResponse.body.data.name,
    );
    expect(customerWorkflowsResponse.body.data[0].workflowDescription).toEqual(
      workflowResponse.body.data.description,
    );
    expect(customerWorkflowsResponse.body.data[0].createdAt).toBeDefined();
    expect(customerWorkflowsResponse.body.data[0].updatedAt).toBeDefined();
  });

  function createTranslationLanguageWorkflow(
    taskTradutorToEnglish: string,
    taskTradutorToPortuguese: string,
  ) {
    return {
      name: 'translate-text-workflow',
      description:
        'This workflow is responsible for translating a text from one language to another language.',
      steps: [
        {
          description: 'Translate text to english',
          order: 1,
          taskId: taskTradutorToEnglish,
        },
        {
          description: 'Translate the text to Braziliam portuguese',
          order: 2,
          taskId: taskTradutorToPortuguese,
        },
      ],
    };
  }

  function createCustomerFake(
    email: string = '<EMAIL>',
    whatsappPhone: string = '+55 11 91234-5678',
  ): CustomerDto {
    return {
      email,
      whatsappPhone,
      name: 'John Doe Company',
      cnpj: '12.345.678/0001-95',
      phone: '+55 11 91234-5679',
      segment: 'collectcash',
    };
  }
});
