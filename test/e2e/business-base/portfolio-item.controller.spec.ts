import { PortfolioItemDto } from '@business-base/application/dto/in/portfolio-item.dto';
import { PortfolioItemStatus } from '@common/enums';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { PortfolioDto } from '@business-base/application/dto/in/portfolio.dto';
import { CommunicationChannel, MessageType } from '@common/enums';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('PortfolioItem (e2e)', () => {
  let app: INestApplication;
  let lineCount: number;
  let testWorkflowId: string;
  let testPortfolioId: string;
  let testCustomer: { login: string; password: string; token: string; customerId: string };

  beforeAll(async () => {
    app = global.__NEST_APP__;
    lineCount = 0;

    // Create agent with variables in backstory
    const agent = {
      role: 'portfolio-item-test-agent',
      backstory: 'I am an agent that processes portfolio item data.',
      llmModel: 'gpt-4o-mini',
      outputType: MessageType.TEXT,
      lang: 'en',
      voice: null,
    };

    const createAgentResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/agents')
      .send(agent)
      .expect(201);

    const agentId = createAgentResponse.body.data.id;

    // Create task with variables matching our custom CSV headers (name, email)
    const task = {
      description: 'Process portfolio item data for {{name}} with email {{email}}',
      agent: agentId,
      responseTemplate: null,
      managerAgentId: null,
    };

    const createTaskResponse = await request(app.getHttpServer())
      .post('/api/v1/intelligence/tasks')
      .send(task)
      .expect(201);

    const taskId = createTaskResponse.body.data.id;

    // Create workflow with the task
    const workflow = {
      name: 'portfolio-item-test-workflow',
      description: 'Workflow to test portfolio item operations',
      steps: [
        {
          description: 'Process portfolio item data',
          order: 1,
          taskId: taskId,
        },
      ],
    };

    const createWorkflowResponse = await request(app.getHttpServer())
      .post('/api/v1/orchestrator/workflows')
      .send(workflow)
      .expect(201);

    testWorkflowId = createWorkflowResponse.body.data.workflowId;

    // Create customer for tests
    testCustomer = await createCustomerFake(
      'Portfolio Item Test Customer',
      'portfolio_item_test',
      '<EMAIL>',
      '12.345.678/0001-90',
      '+5511987654321',
    );

    // Create portfolio for tests
    const portfolio = createPortfolioFake('Portfolio Item Test Portfolio', 5, false);

    const { body: portfolioResponse } = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolios/import')
      .attach('file', `${__dirname}/data/test-portfolio-item.csv`)
      .field('name', portfolio.name)
      .field('workflowId', testWorkflowId)
      .field('workExpression', portfolio.workExpression)
      .field('executeImmediately', portfolio.executeImmediately)
      .field('idleAfter', portfolio.idleAfter)
      .field('communicationChannel', CommunicationChannel.WHATSAPPSELFHOSTED)
      .set('Authorization', `Bearer ${testCustomer.token}`)
      .expect(201);

    testPortfolioId = portfolioResponse.data.id;
  });

  it('/v1/business-base/portfolio-items (POST) - Create PortfolioItem', async () => {
    const portfolioItem = createPortfolioItemfake();

    const body = await createPortfolioItemDb(portfolioItem);

    expect(body).toBeDefined();
    expect(body.data.id).toBeDefined();
    expect(body.data.createdAt).toBeDefined();
    expect(body.data.updatedAt).toBeDefined();
    expect(body.data.portfolioId).toEqual(portfolioItem.portfolioId);
    expect(body.data.currentStatus).toEqual('PENDING');
    expect(body.data.phoneNumber).toEqual(portfolioItem.phoneNumber);
    expect(body.data.lastInteraction).toBeNull();
  });

  it('/v1/negotation/portfolio-items/:portfolioItemId (GET) - Find portfolioItem By Id', async () => {
    const portfolioItem = createPortfolioItemfake();

    const createBody = await createPortfolioItemDb(portfolioItem);

    expect(createBody).toBeDefined();
    expect(createBody.data.id).toBeDefined();

    const portfolioItemId = createBody.data.id;

    const { body: getBody } = await request(app.getHttpServer())
      .get(`/api/v1/business-base/portfolio-items/${portfolioItemId}`)
      .expect(200);

    expect(getBody).toBeDefined();
    expect(getBody.data.id).toBeDefined();
    expect(getBody.data.createdAt).toBeDefined();
    expect(getBody.data.updatedAt).toBeDefined();
    expect(getBody.data.customData).toBeDefined();
    expect(getBody.data.portfolioId).toEqual(portfolioItem.portfolioId);
    expect(getBody.data.currentStatus).toEqual('PENDING');
    expect(getBody.data.phoneNumber).toEqual(portfolioItem.phoneNumber);
    expect(getBody.data.lastInteraction).toBeNull();
  });

  it('/v1/business-base/portfolio-items/:portfolioItemId (PUT) - Update PortfolioItem to IN_PROGRESS', async () => {
    const portfolioItem = createPortfolioItemfake();

    const createBody = await createPortfolioItemDb(portfolioItem);

    expect(createBody).toBeDefined();
    expect(createBody.data.id).toBeDefined();

    const portfolioItemId = createBody.data.id;

    const { body: putBody } = await request(app.getHttpServer())
      .put(`/api/v1/business-base/portfolio-items/${portfolioItemId}/execute`)
      .expect(200);

    expect(putBody).toBeDefined();
    expect(putBody.data.id).toBeDefined();
    expect(putBody.data.currentStatus).toEqual(PortfolioItemStatus.IN_PROGRESS);

    const { body: getExecutionHistoryBody } = await request(app.getHttpServer())
      .get(
        `/api/v1/business-base/portfolio/items/execution/history/portfolio-item/${portfolioItemId}`,
      )
      .expect(200);

    expect(getExecutionHistoryBody).toBeDefined();
    expect(getExecutionHistoryBody.data.length).toEqual(1);
    expect(getExecutionHistoryBody.data[0].newStatus).toEqual(PortfolioItemStatus.IN_PROGRESS);
    expect(getExecutionHistoryBody.data[0].oldStatus).toEqual(PortfolioItemStatus.PENDING);
  });

  it('/v1/business-base/portfolio-items/:portfolioItemId (PUT) - Update PortfolioItem to PAUSED', async () => {
    const portfolioItem = createPortfolioItemfake();

    const createBody = await createPortfolioItemDb(portfolioItem);

    expect(createBody).toBeDefined();
    expect(createBody.data.id).toBeDefined();

    const portfolioItemId = createBody.data.id;

    const { body: putBody } = await request(app.getHttpServer())
      .put(`/api/v1/business-base/portfolio-items/${portfolioItemId}/pause`)
      .expect(200);

    expect(putBody).toBeDefined();
    expect(putBody.data.id).toBeDefined();
    expect(putBody.data.currentStatus).toEqual(PortfolioItemStatus.PAUSED);

    const { body: getExecutionHistoryBody } = await request(app.getHttpServer())
      .get(
        `/api/v1/business-base/portfolio/items/execution/history/portfolio-item/${portfolioItemId}`,
      )
      .expect(200);

    expect(getExecutionHistoryBody).toBeDefined();
    expect(getExecutionHistoryBody.data.length).toEqual(1);
    expect(getExecutionHistoryBody.data[0].newStatus).toEqual(PortfolioItemStatus.PAUSED);
    expect(getExecutionHistoryBody.data[0].oldStatus).toEqual(PortfolioItemStatus.PENDING);
  });

  it('/v1/business-base/portfolio-items/:portfolioItemId (PUT) - Update PortfolioItem to CANCELLED', async () => {
    const portfolioItem = createPortfolioItemfake();

    const createBody = await createPortfolioItemDb(portfolioItem);

    expect(createBody).toBeDefined();
    expect(createBody.data.id).toBeDefined();

    const portfolioItemId = createBody.data.id;

    const { body: putBody } = await request(app.getHttpServer())
      .put(`/api/v1/business-base/portfolio-items/${portfolioItemId}/cancel`)
      .expect(200);

    expect(putBody).toBeDefined();
    expect(putBody.data.id).toBeDefined();
    expect(putBody.data.currentStatus).toEqual(PortfolioItemStatus.CANCELLED);

    const { body: getExecutionHistoryBody } = await request(app.getHttpServer())
      .get(
        `/api/v1/business-base/portfolio/items/execution/history/portfolio-item/${portfolioItemId}`,
      )
      .expect(200);

    expect(getExecutionHistoryBody).toBeDefined();
    expect(getExecutionHistoryBody.data.length).toEqual(1);
    expect(getExecutionHistoryBody.data[0].newStatus).toEqual(PortfolioItemStatus.CANCELLED);
    expect(getExecutionHistoryBody.data[0].oldStatus).toEqual(PortfolioItemStatus.PENDING);
  });

  it('/v1/business-base/portfolio-items/:portfolioItemId (DELETE) - Delete PortfolioItem', async () => {
    const portfolioItem = createPortfolioItemfake();

    const createBody = await createPortfolioItemDb(portfolioItem);

    expect(createBody).toBeDefined();
    expect(createBody.data.id).toBeDefined();

    const portfolioItemId = createBody.data.id;

    const { body: deleteBody } = await request(app.getHttpServer())
      .delete(`/api/v1/business-base/portfolio-items/${portfolioItemId}`)
      .expect(200);

    expect(deleteBody).toBeDefined();
    expect(deleteBody.data.id).toBeDefined();

    const { body: getBody } = await request(app.getHttpServer())
      .get('/api/v1/business-base/portfolio-items/' + portfolioItemId)
      .expect(200);

    expect(getBody).toBeDefined();
    expect(getBody.data.currentStatus).toEqual('CANCELLED');
  });

  function createPortfolioFake(
    name: string,
    idleAfter: number,
    executeImmediately: boolean = false,
  ): PortfolioDto {
    return new PortfolioDto(
      name,
      testWorkflowId,
      '*/5 * * * *',
      CommunicationChannel.WHATSAPPSELFHOSTED,
      idleAfter,
      executeImmediately,
    );
  }

  async function createCustomerFake(
    name: string,
    nickname: string,
    email: string,
    cnpj: string,
    phone: string,
  ): Promise<{ login: string; password: string; token: string; customerId: string }> {
    const customerUserRequest = {
      customerData: {
        cnpj,
        email,
        name,
        phone,
        whatsappPhone: phone,
        segment: 'collectcash',
      },
      accountData: {
        nickname,
      },
      userData: {
        email,
        firstname: name,
        lastname: 'Doe',
        password: 'P@ssw0rd123',
        passwordConfirmation: 'P@ssw0rd123',
      },
    };

    const { body } = await request(app.getHttpServer())
      .post('/api/v1/auth/users/signup')
      .send(customerUserRequest)
      .expect(201);

    const { accessToken } = await getAuthCredentials(app, email, 'P@ssw0rd123');

    return {
      login: email,
      password: 'P@ssw0rd123',
      token: accessToken,
      customerId: body.data.customer.id,
    };
  }

  function createPortfolioItemfake(mockPortfolioId = testPortfolioId): PortfolioItemDto {
    return new PortfolioItemDto(
      mockPortfolioId,
      '+*************',
      {
        name: 'John Doe',
        email: '<EMAIL>',
      },
      lineCount++,
    );
  }

  async function createPortfolioItemDb(portfolioItem: PortfolioItemDto) {
    const { body: createBody } = await request(app.getHttpServer())
      .post('/api/v1/business-base/portfolio-items')
      .send(portfolioItem)
      .expect(201);

    return createBody;
  }
});
