import { Test, TestingModule } from '@nestjs/testing';
import { OutgoingMessageAdapter } from '@message-hub/infrastructure/adapters/db/outgoing-message.adapter';
import { PrismaService } from '@common/prisma/prisma.service';
import { CommunicationChannel } from '@common/enums';

describe('OutgoingMessageAdapter', () => {
  let adapter: OutgoingMessageAdapter;

  const mockPrismaService = {
    client: {
      $transaction: jest.fn(),
      customerPhone: {
        findFirst: jest.fn(),
      },
    },
  };

  // Use a fixed date for all tests
  const FIXED_DATE = new Date('2025-05-28T11:00:00.000Z');

  beforeAll(() => {
    jest.useFakeTimers();
    jest.setSystemTime(FIXED_DATE);
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OutgoingMessageAdapter,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    adapter = module.get<OutgoingMessageAdapter>(OutgoingMessageAdapter);
  });

  describe('insertOutgoingMessage', () => {
    const baseMessageData = {
      customerId: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
      from: '5511915183129',
      to: '5511999999991',
      messageType: 'TEXT',
      message: 'Test message',
      channel: CommunicationChannel.WHATSAPPSELFHOSTED,
      status: 'ACTIVE',
      isFirstMessage: false,
      apiUrl: 'http://localhost:3012',
      fileUrl: null,
    };

    const randomDelay = 60;

    it('should insert message when within daily limit', async () => {
      // Mock customer phone with daily limit of 5
      mockPrismaService.client.customerPhone.findFirst.mockResolvedValue({
        phoneNumber: '5511915183129',
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        dailyLimit: 5,
      });

      // Mock transaction with proper query responses
      mockPrismaService.client.$transaction.mockImplementation(async callback => {
        const mockTx = {
          $queryRaw: jest
            .fn()
            .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // For time to go query
            .mockResolvedValueOnce([{ count: 3 }]), // For message count query
          $executeRaw: jest.fn(),
          customerPhone: mockPrismaService.client.customerPhone,
        };
        return callback(mockTx);
      });

      await adapter.insertOutgoingMessage(baseMessageData, randomDelay);

      expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      const txCallback = mockPrismaService.client.$transaction.mock.calls[0][0];
      const mockTx = {
        $queryRaw: jest
          .fn()
          .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }])
          .mockResolvedValueOnce([{ count: 3 }]),
        $executeRaw: jest.fn(),
        customerPhone: mockPrismaService.client.customerPhone,
      };
      await txCallback(mockTx);
      expect(mockTx.$executeRaw).toHaveBeenCalled();
    });

    it('should handle different channels correctly', async () => {
      // Test with SMS channel
      const smsMessageData = {
        ...baseMessageData,
        channel: CommunicationChannel.SMS_VONAGE,
        apiUrl: 'sms://send',
      };

      mockPrismaService.client.customerPhone.findFirst.mockResolvedValue({
        phoneNumber: '5511915183129',
        communicationChannel: CommunicationChannel.SMS_VONAGE,
        dailyLimit: 3,
      });

      // Mock transaction with proper query responses
      mockPrismaService.client.$transaction.mockImplementation(async callback => {
        const mockTx = {
          $queryRaw: jest
            .fn()
            .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // For time to go query
            .mockResolvedValueOnce([{ count: 2 }]), // For message count query
          $executeRaw: jest.fn(),
          customerPhone: mockPrismaService.client.customerPhone,
        };
        return callback(mockTx);
      });

      await adapter.insertOutgoingMessage(smsMessageData, randomDelay);

      expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      const txCallback = mockPrismaService.client.$transaction.mock.calls[0][0];
      const mockTx = {
        $queryRaw: jest
          .fn()
          .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }])
          .mockResolvedValueOnce([{ count: 2 }]),
        $executeRaw: jest.fn(),
        customerPhone: mockPrismaService.client.customerPhone,
      };
      await txCallback(mockTx);
      expect(mockTx.$executeRaw).toHaveBeenCalled();
    });

    it('should throw error when customer phone not found', async () => {
      const firstMessageData = { ...baseMessageData, isFirstMessage: true };
      mockPrismaService.client.customerPhone.findFirst.mockResolvedValue(null);

      await expect(adapter.insertOutgoingMessage(firstMessageData, randomDelay)).rejects.toThrow(
        `Customer phone not found for number: ${baseMessageData.from} and channel: ${baseMessageData.channel}`,
      );
    });

    it('should handle first message scheduling correctly', async () => {
      const firstMessageData = {
        ...baseMessageData,
        isFirstMessage: true,
      };

      mockPrismaService.client.customerPhone.findFirst.mockResolvedValue({
        phoneNumber: '5511915183129',
        communicationChannel: CommunicationChannel.WHATSAPPSELFHOSTED,
        dailyLimit: 5,
      });

      // Mock transaction with proper query responses
      mockPrismaService.client.$transaction.mockImplementation(async callback => {
        const mockTx = {
          $queryRaw: jest
            .fn()
            .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }]) // For time to go query
            .mockResolvedValueOnce([{ count: 0 }]), // For message count query
          $executeRaw: jest.fn(),
          customerPhone: mockPrismaService.client.customerPhone,
        };
        return callback(mockTx);
      });

      await adapter.insertOutgoingMessage(firstMessageData, randomDelay);

      expect(mockPrismaService.client.$transaction).toHaveBeenCalled();
      const txCallback = mockPrismaService.client.$transaction.mock.calls[0][0];
      const mockTx = {
        $queryRaw: jest
          .fn()
          .mockResolvedValueOnce([{ maxTimeToGo: new Date(FIXED_DATE) }])
          .mockResolvedValueOnce([{ count: 0 }]),
        $executeRaw: jest.fn(),
        customerPhone: mockPrismaService.client.customerPhone,
      };
      await txCallback(mockTx);
      expect(mockTx.$executeRaw).toHaveBeenCalled();
    });
  });
});
