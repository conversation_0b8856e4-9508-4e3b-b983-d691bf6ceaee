import { Test, TestingModule } from '@nestjs/testing';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { MessageHistoryResponseDto } from '@business-base/misc/interfaces/in/message-history-response.dto';
import { MessageType, RoleType } from '@common/enums';
import { BusinessExceptionStatus } from '@common/exception/types/BusinessException';
import { PortfolioItemExecutionHistoryUseCase } from '@business-base/application/use-cases/portfolio-item-execution-history.use-case';
import { SQSService } from '@common/sqs/sqs.service';
import { S3Service } from '@common/s3/s3.service';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { StatisticalDataUseCase } from '@business-base/application/use-cases/statistical-data.use-case';

describe('PortfolioItemUseCase', () => {
  let useCase: PortfolioItemUseCase;
  let mockPortfolioItemAdapter;
  let mockPortfolioAdapter;
  let mockInfraConversationHistoryAdapter;
  let mockMessageHubOutgoingMessageAdapter;
  let mockPortfolioItemWorkflowExecutionAdapter;
  let mockCustomerPreferencesPort;
  let mockPortfolioItemScheduledFollowUpAdapter;
  let mockPortfolioItemCustomDataAdapter;
  let mockMiddlewareResponseOutputAdapter;
  let mockCustomerAdapter;
  let mockInfraMessageHubAdapter;
  let mockPortfolioItemExecutionHistoryAdapter;
  let mockInfraWorkflowAdapter;
  let mockCollectCashStatsPort;

  beforeEach(async () => {
    mockPortfolioItemAdapter = {
      get: jest.fn(),
    };

    mockPortfolioAdapter = {
      get: jest.fn(),
    };

    mockInfraConversationHistoryAdapter = {
      retrieveConversationHistoryByWorkflowExecutionId: jest.fn(),
    };

    mockMessageHubOutgoingMessageAdapter = {
      getOutgoingMessagesByCustomerId: jest.fn(),
    };

    mockPortfolioItemWorkflowExecutionAdapter = {
      getAll: jest.fn(),
    };

    mockCustomerPreferencesPort = {
      getById: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
    };

    mockPortfolioItemScheduledFollowUpAdapter = {
      getAll: jest.fn(),
    };

    mockPortfolioItemCustomDataAdapter = {
      get: jest.fn(),
    };

    mockMiddlewareResponseOutputAdapter = {
      get: jest.fn(),
    };

    mockCustomerAdapter = {
      get: jest.fn(),
    };

    mockInfraMessageHubAdapter = {
      get: jest.fn(),
    };

    mockPortfolioItemExecutionHistoryAdapter = {
      get: jest.fn(),
    };

    mockInfraWorkflowAdapter = {
      startWorkflow: jest.fn(),
      executeWorkflow: jest.fn(),
      getWorkflowById: jest.fn(),
      getWorkflowVariables: jest.fn(),
    };

    mockCollectCashStatsPort = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PortfolioItemUseCase,
        PortfolioItemExecutionHistoryUseCase,
        SQSService,
        S3Service,
        CustomerPreferencesUseCase,
        StatisticalDataUseCase,
        {
          provide: 'PortfolioItemPort',
          useValue: mockPortfolioItemAdapter,
        },
        {
          provide: 'PortfolioItemCustomDataPort',
          useValue: mockPortfolioItemCustomDataAdapter,
        },
        {
          provide: 'MiddlewareResponseOutputPort',
          useValue: mockMiddlewareResponseOutputAdapter,
        },
        {
          provide: 'InfraConversationHistoryPort',
          useValue: mockInfraConversationHistoryAdapter,
        },
        {
          provide: 'PortfolioItemWorkflowExecutionPort',
          useValue: mockPortfolioItemWorkflowExecutionAdapter,
        },
        {
          provide: 'PortfolioItemScheduledFollowUpPort',
          useValue: mockPortfolioItemScheduledFollowUpAdapter,
        },
        {
          provide: 'CustomerPreferencesPort',
          useValue: mockCustomerPreferencesPort,
        },
        {
          provide: 'CustomerPort',
          useValue: mockCustomerAdapter,
        },
        {
          provide: 'PortfolioPort',
          useValue: mockPortfolioAdapter,
        },
        {
          provide: 'InfraMessageHubPort',
          useValue: mockInfraMessageHubAdapter,
        },
        {
          provide: 'MessageHubOutgoingMessagePort',
          useValue: mockMessageHubOutgoingMessageAdapter,
        },
        {
          provide: 'PortfolioItemExecutionHistoryPort',
          useValue: mockPortfolioItemExecutionHistoryAdapter,
        },
        {
          provide: 'InfraWorkflowPort',
          useValue: mockInfraWorkflowAdapter,
        },
        {
          provide: 'CollectCashStatsPort',
          useValue: mockCollectCashStatsPort,
        },
      ],
    }).compile();

    useCase = module.get<PortfolioItemUseCase>(PortfolioItemUseCase);
  });

  describe('findConversationHistoryById', () => {
    const portfolioItemId = 'test-portfolio-item-id';
    const portfolioId = 'test-portfolio-id';
    const customerId = 'test-customer-id';
    const workflowExecutionId = 'test-workflow-execution-id';

    const mockPortfolioItem = {
      id: portfolioItemId,
      portfolioId: portfolioId,
    };

    const mockPortfolio = {
      id: portfolioId,
      customerId: customerId,
    };

    const mockWorkflowExecutions = [{ workflowExecutionId: workflowExecutionId }];

    const mockConversationHistory: MessageHistoryResponseDto[] = [
      {
        id: '1',
        role: RoleType.USER,
        messageText: 'Hello',
        messageType: MessageType.TEXT,
        lang: 'en',
        createdAt: new Date(),
        updatedAt: new Date(),
        sent: undefined,
        sent_at: undefined,
        time_to_go: new Date(),
      },
      {
        id: '2',
        role: RoleType.ATTENDANT,
        messageText: 'Hi there',
        messageType: MessageType.TEXT,
        lang: 'en',
        createdAt: new Date(),
        updatedAt: new Date(),
        sent: undefined,
        sent_at: undefined,
        time_to_go: new Date(),
      },
    ];

    const mockMessageStatuses = [
      {
        message: 'Hello',
        sent: true,
        sent_at: new Date(),
        time_to_go: new Date(),
      },
      {
        message: 'Hi there',
        sent: false,
        sent_at: null,
        time_to_go: new Date(),
      },
    ];

    beforeEach(() => {
      jest.clearAllMocks();
      mockPortfolioItemAdapter.get.mockResolvedValue(mockPortfolioItem);
      mockPortfolioAdapter.get.mockResolvedValue(mockPortfolio);
      mockPortfolioItemWorkflowExecutionAdapter.getAll.mockResolvedValue(mockWorkflowExecutions);
      mockInfraConversationHistoryAdapter.retrieveConversationHistoryByWorkflowExecutionId.mockResolvedValue(
        mockConversationHistory,
      );
      mockMessageHubOutgoingMessageAdapter.getOutgoingMessagesByCustomerId.mockResolvedValue(
        mockMessageStatuses,
      );
    });

    it('should match messages by content and enrich with status', async () => {
      const result = await useCase.findConversationHistoryById(portfolioItemId);

      expect(result).toHaveLength(2);
      expect(result[0].messageText).toBe('Hello');
      expect(result[0].sent).toBe(true);
      expect(result[0].sent_at).toBeDefined();
      expect(result[0].time_to_go).toBeDefined();

      expect(result[1].messageText).toBe('Hi there');
      expect(result[1].sent).toBe(false);
      expect(result[1].sent_at).toBeNull();
      expect(result[1].time_to_go).toBeDefined();
    });

    it('should handle case when no matching status is found', async () => {
      mockMessageHubOutgoingMessageAdapter.getOutgoingMessagesByCustomerId.mockResolvedValue([
        {
          message: 'Different message',
          sent: true,
          sent_at: new Date(),
          time_to_go: new Date(),
        },
      ]);

      const result = await useCase.findConversationHistoryById(portfolioItemId);

      expect(result).toHaveLength(2);
      expect(result[0].sent).toBeUndefined();
      expect(result[0].sent_at).toBeUndefined();
      expect(result[0].time_to_go).toBeDefined();
    });

    it('should throw BusinessException when portfolio item is not found', async () => {
      mockPortfolioItemAdapter.get.mockResolvedValue(null);

      await expect(useCase.findConversationHistoryById(portfolioItemId)).rejects.toMatchObject({
        message: `PortfolioItem with id ${portfolioItemId} not found`,
        status: BusinessExceptionStatus.ITEM_NOT_FOUND,
      });

      expect(mockPortfolioItemAdapter.get).toHaveBeenCalledWith(portfolioItemId);
      expect(mockPortfolioAdapter.get).not.toHaveBeenCalled();
    });

    it('should throw BusinessException when portfolio is not found', async () => {
      mockPortfolioItemAdapter.get.mockResolvedValue(mockPortfolioItem);
      mockPortfolioAdapter.get.mockResolvedValue(null);

      await expect(useCase.findConversationHistoryById(portfolioItemId)).rejects.toMatchObject({
        message: `Portfolio not found for portfolioItem ${portfolioItemId}`,
        status: BusinessExceptionStatus.ITEM_NOT_FOUND,
      });

      expect(mockPortfolioItemAdapter.get).toHaveBeenCalledWith(portfolioItemId);
      expect(mockPortfolioAdapter.get).toHaveBeenCalledWith(mockPortfolioItem.portfolioId);
      expect(mockPortfolioItemWorkflowExecutionAdapter.getAll).not.toHaveBeenCalled();
    });
  });
});
