import { TaskService } from '@intelligence/domain/services/task.service';

describe('Task Service', () => {
  let taskService: TaskService;

  beforeEach(() => {
    taskService = new TaskService();
  });

  it('should return a compiled backstory', () => {
    const result = taskService.compileTaskDescription('Fake task description with: {{key}}', {
      key: 'value',
    });
    expect(result).toBe(`Fake task description with: value\n`);
  });
});
