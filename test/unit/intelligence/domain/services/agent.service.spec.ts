import { AgentService } from '@intelligence/domain/services/agent.service';

describe('AgentService', () => {
  let agentService: AgentService;

  beforeEach(() => {
    agentService = new AgentService();
  });

  it('should return a compiled backstory', () => {
    const result = agentService.compileBackstory('Fake backstory agent.', 'pt_BR', {});
    expect(result).toBe(`Fake backstory agent.\nThe output message must be in pt_BR language.`);
  });
});
