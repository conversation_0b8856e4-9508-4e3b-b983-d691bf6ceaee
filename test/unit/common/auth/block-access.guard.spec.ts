import { BlockAccessGuard } from '@common/auth/block-access.guard';
import { Reflector } from '@nestjs/core';
import { ExecutionContext } from '@nestjs/common';
import { IS_BLOCKED } from '@common/auth/decorators/block-access.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthExceptionStatus } from '@common/exception/types/AuthException';

describe('Common - BlockAccessGuard', () => {
  let guard: BlockAccessGuard;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
    guard = new BlockAccessGuard(reflector);
  });

  const mockExecutionContext = (
    handlerMetadata: any = {},
    controllerMetadata: any = {},
    requestData: any = {},
  ) => {
    return {
      getHandler: jest.fn().mockReturnValue(handlerMetadata),
      getClass: jest.fn().mockReturnValue(controllerMetadata),
      switchToHttp: () => ({
        getRequest: () => ({
          ...requestData,
        }),
      }),
    } as unknown as ExecutionContext;
  };

  it('should allow access if BlockAccessGuard is excluded', async () => {
    const excludeGuardsMetadata = { [EXCLUDE_GUARDS_KEY]: ['BlockAccessGuard'] };
    const context = mockExecutionContext({}, excludeGuardsMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return ['BlockAccessGuard'];
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(EXCLUDE_GUARDS_KEY, [
      {},
      excludeGuardsMetadata,
    ]);
  });

  it('should allow access if the route is not blocked and no exclusion', async () => {
    const context = mockExecutionContext({}, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === IS_BLOCKED) {
        return false;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_BLOCKED, [{}, {}]);
  });

  it('should allow access if the route is blocked on handler but BlockAccessGuard is excluded', async () => {
    const handlerMetadata = { [IS_BLOCKED]: true };
    const excludeGuardsMetadata = { [EXCLUDE_GUARDS_KEY]: ['BlockAccessGuard'] };
    const context = mockExecutionContext(handlerMetadata, excludeGuardsMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return ['BlockAccessGuard'];
      }
      if (key === IS_BLOCKED) {
        return true;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(EXCLUDE_GUARDS_KEY, [
      handlerMetadata,
      excludeGuardsMetadata,
    ]);
  });

  it('should throw AuthException if the route is blocked on handler', async () => {
    const handlerMetadata = { [IS_BLOCKED]: true };
    const requestData = { url: '/blocked-route' };
    const context = mockExecutionContext(handlerMetadata, {}, requestData);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === IS_BLOCKED) {
        return true;
      }
      return undefined;
    });

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'This functionality is blocked',
      status: AuthExceptionStatus.BLOCKED_ENDPOINT,
      reason: `User trying to access blocked endpoint. Route ${requestData.url}`,
      code: '6d2a317c-5b0b-41cf-a005-0d251cf3075b',
    });

    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_BLOCKED, [handlerMetadata, {}]);
  });

  it('should throw AuthException if the route is blocked on controller', async () => {
    const controllerMetadata = { [IS_BLOCKED]: true };
    const requestData = { url: '/blocked-controller-route' };
    const context = mockExecutionContext({}, controllerMetadata, requestData);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === IS_BLOCKED) {
        return true;
      }
      return undefined;
    });

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'This functionality is blocked',
      status: AuthExceptionStatus.BLOCKED_ENDPOINT,
      reason: `User trying to access blocked endpoint. Route ${requestData.url}`,
      code: '6d2a317c-5b0b-41cf-a005-0d251cf3075b',
    });

    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_BLOCKED, [{}, controllerMetadata]);
  });

  it('should prioritize handler metadata over controller metadata for blocking', async () => {
    const handlerMetadata = { [IS_BLOCKED]: false };
    const controllerMetadata = { [IS_BLOCKED]: true };
    const requestData = { url: '/handler-overrides-controller' };
    const context = mockExecutionContext(handlerMetadata, controllerMetadata, requestData);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === IS_BLOCKED) {
        return false; // Handler overrides controller
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_BLOCKED, [
      handlerMetadata,
      controllerMetadata,
    ]);
  });

  it('should allow access if IS_BLOCKED metadata is undefined', async () => {
    const context = mockExecutionContext({}, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === IS_BLOCKED) {
        return undefined;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_BLOCKED, [{}, {}]);
  });
});
