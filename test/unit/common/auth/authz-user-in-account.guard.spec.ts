import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { Reflector } from '@nestjs/core';
import { ExecutionContext } from '@nestjs/common';
import { UserRoleInAccount } from '@common/enums';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthExceptionStatus } from '@common/exception/types/AuthException';

describe('Common - AuthzUserInAccountGuard', () => {
  let guard: AuthzUserInAccountGuard;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
    guard = new AuthzUserInAccountGuard(reflector);
  });

  const mockExecutionContext = (
    handlerMetadata: any = {},
    controllerMetadata: any = {},
    user: any = {},
  ) => {
    return {
      getHandler: jest.fn().mockReturnValue(handlerMetadata),
      getClass: jest.fn().mockReturnValue(controllerMetadata),
      switchToHttp: () => ({
        getRequest: () => ({
          user,
        }),
      }),
    } as unknown as ExecutionContext;
  };

  it('should allow access if the route is public (handler)', async () => {
    const handlerMetadata = { [IS_PUBLIC_KEY]: true };
    const context = mockExecutionContext(handlerMetadata, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return true;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_PUBLIC_KEY, [handlerMetadata, {}]);
  });

  it('should allow access if the controller is public', async () => {
    const controllerMetadata = { [IS_PUBLIC_KEY]: true };
    const context = mockExecutionContext({}, controllerMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return true;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_PUBLIC_KEY, [
      {},
      controllerMetadata,
    ]);
  });

  it('should allow access if AuthzUserInAccountGuard is excluded', async () => {
    const excludeGuardsMetadata = { [EXCLUDE_GUARDS_KEY]: ['AuthzUserInAccountGuard'] };
    const context = mockExecutionContext({}, excludeGuardsMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }
      if (key === EXCLUDE_GUARDS_KEY) {
        return ['AuthzUserInAccountGuard'];
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(EXCLUDE_GUARDS_KEY, [
      {},
      excludeGuardsMetadata,
    ]);
  });

  it('should deny access if no required roles are defined', async () => {
    const context = mockExecutionContext({}, {}, { roleInAccount: 'USER' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'userRoleInAccount') {
        return undefined;
      }
      return undefined;
    });

    await expect(guard.canActivate(mockExecutionContext(context))).rejects.toMatchObject({
      name: 'AuthzUserInAccountGuard::no-user-role-in-route',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.UNDEFINED_USER_ROLE_IN_ROUTE,
      code: 'b72ee508-2e7b-4ab7-8eb7-17fe1e7aac4a',
    });
  });

  it('should allow access if required role is __NONE__', async () => {
    const userRoleInAccount = [UserRoleInAccount.__NONE__];
    const context = mockExecutionContext({}, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should deny access if required role is __NONE__ and has any other role', async () => {
    const userRoleInAccount = [UserRoleInAccount.__NONE__, UserRoleInAccount.ADMIN];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, { roleInAccount: 'USER' })),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::invalid-user-role-in-account',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.INVALID_USER_ROLE_IN_ACCOUNT,
      code: '19edba3d-1ecd-49dc-9077-c470af7a3638',
    });
  });

  it('should deny access if user has no roleInAccount', async () => {
    const userRoleInAccount = [UserRoleInAccount.ADMIN];
    const context = mockExecutionContext({}, {}, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    await expect(guard.canActivate(mockExecutionContext(context))).rejects.toMatchObject({
      name: 'AuthzUserInAccountGuard::no-user-role-in-account',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.USER_HAS_NO_ROLE_IN_ACCOUNT,
      code: '35dd2ea7-8d6b-4cef-b201-d349b980af1e',
    });
  });

  it('should deny access if user role is not authorized', async () => {
    const userRoleInAccount = [UserRoleInAccount.ADMIN];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, { roleInAccount: 'user' })),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::invalid-user-role-in-account',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.INVALID_USER_ROLE_IN_ACCOUNT,
      code: '19edba3d-1ecd-49dc-9077-c470af7a3638',
    });
  });

  it('should allow access if user role is authorized', async () => {
    const userRoleInAccount = [UserRoleInAccount.ADMIN];
    const context = mockExecutionContext({}, {}, { roleInAccount: 'ADMIN' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should handle case insensitivity of user role', async () => {
    const userRoleInAccount = [UserRoleInAccount.ADMIN];
    const context = mockExecutionContext({}, {}, { roleInAccount: 'admin' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'userRoleInAccount') {
        return userRoleInAccount;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });
});
