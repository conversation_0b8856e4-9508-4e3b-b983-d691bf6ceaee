import { AuthnGuard } from '@common/auth/authn.guard';
import { JwtService } from '@nestjs/jwt';
import { Reflector } from '@nestjs/core';
import { TokenPort } from '@common/auth/db/ports/token.port';
import { ExecutionContext } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { TokenStatus } from '@common/enums';
import { AuthExceptionStatus } from '@common/exception/types/AuthException';

describe('AuthnGuard', () => {
  let guard: AuthnGuard;
  let jwtService: JwtService;
  let reflector: Reflector;
  let tokenAdapter: TokenPort;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthnGuard,
        {
          provide: JwtService,
          useValue: {
            verifyAsync: jest.fn(),
          },
        },
        {
          provide: Reflector,
          useValue: {
            getAllAndOverride: jest.fn(),
          },
        },
        {
          provide: 'TokenPort',
          useValue: {
            getFirst: jest.fn(),
          },
        },
      ],
    }).compile();

    guard = module.get<AuthnGuard>(AuthnGuard);
    jwtService = module.get<JwtService>(JwtService);
    reflector = module.get<Reflector>(Reflector);
    tokenAdapter = module.get<TokenPort>('TokenPort');
  });

  const mockExecutionContext = (req: Partial<Request>): ExecutionContext => {
    return {
      switchToHttp: () => ({
        getRequest: () => req,
      }),
      getHandler: jest.fn(),
      getClass: jest.fn(),
      // Otras propiedades y métodos de ExecutionContext se pueden mockear si es necesario
    } as any;
  };

  it('should allow access if route is public', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return true;
      return undefined;
    });

    const context = mockExecutionContext({});
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_PUBLIC_KEY, expect.any(Array));
  });

  it('should allow access if AuthnGuard is excluded', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return ['AuthnGuard'];
      return undefined;
    });

    const context = mockExecutionContext({});
    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(EXCLUDE_GUARDS_KEY, expect.any(Array));
  });

  it('should throw AuthException if no token is provided', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined; // No exclusion
      return undefined;
    });

    const request: Partial<Request> = {
      headers: {},
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);
    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'You must be authenticated to access this route',
      status: AuthExceptionStatus.NO_TOKEN_IN_PROTECTED_ROUTE,
    });
  });

  it('should throw AuthException if token signature is invalid', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined;
      return undefined;
    });

    const request: Partial<Request> = {
      headers: {
        authorization: 'Bearer invalidtoken',
      },
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);

    (jwtService.verifyAsync as jest.Mock).mockRejectedValue(new Error('Invalid signature'));

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'You must be authenticated to access this route',
      status: AuthExceptionStatus.INVALID_TOKEN_SIGNATURE,
    });
  });

  it('should throw AuthException if token is not found in the database', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined;
      return undefined;
    });

    const payload = { userId: 'user123' };
    const request: Partial<Request> = {
      headers: {
        authorization: 'Bearer validtoken',
      },
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);

    (jwtService.verifyAsync as jest.Mock).mockResolvedValue(payload);
    (tokenAdapter.getFirst as jest.Mock).mockResolvedValue(null);

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'You must be authenticated to access this route',
      status: AuthExceptionStatus.TOKEN_NOT_FOUND,
    });
  });

  it('should throw AuthException if token is revoked', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined;
      return undefined;
    });

    const payload = { userId: 'user123' };
    const tokenEntity = {
      id: 'token123',
      status: TokenStatus.REVOKED_BY_USER,
      expireAt: new Date(Date.now() + 10000),
    };
    const request: Partial<Request> = {
      headers: {
        authorization: 'Bearer validtoken',
      },
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);

    (jwtService.verifyAsync as jest.Mock).mockResolvedValue(payload);
    (tokenAdapter.getFirst as jest.Mock).mockResolvedValue(tokenEntity);

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'You must be authenticated to access this route',
      status: AuthExceptionStatus.TOKEN_IS_REVOKED,
    });
  });

  it('should throw AuthException if token is expired', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined;
      return undefined;
    });

    const payload = { userId: 'user123' };
    const tokenEntity = {
      id: 'token123',
      status: TokenStatus.ACTIVE,
      expireAt: new Date(Date.now() - 10000), // Token expirado
    };
    const request: Partial<Request> = {
      headers: {
        authorization: 'Bearer validtoken',
      },
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);

    (jwtService.verifyAsync as jest.Mock).mockResolvedValue(payload);
    (tokenAdapter.getFirst as jest.Mock).mockResolvedValue(tokenEntity);

    await expect(guard.canActivate(context)).rejects.toMatchObject({
      message: 'You must be authenticated to access this route',
      status: AuthExceptionStatus.TOKEN_EXPIRED,
    });
  });

  it('should assign payload to request and allow access if token is valid', async () => {
    (reflector.getAllAndOverride as jest.Mock).mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) return false;
      if (key === EXCLUDE_GUARDS_KEY) return undefined;
      return undefined;
    });

    const payload = { userId: 'user123' };
    const tokenEntity = {
      id: 'token123',
      status: TokenStatus.ACTIVE,
      expireAt: new Date(Date.now() + 10000),
    };
    const request: Partial<Request> = {
      headers: {
        authorization: 'Bearer validtoken',
      },
      url: '/protected-route',
    };
    const context = mockExecutionContext(request);

    (jwtService.verifyAsync as jest.Mock).mockResolvedValue(payload);
    (tokenAdapter.getFirst as jest.Mock).mockResolvedValue(tokenEntity);

    const canActivate = await guard.canActivate(context);
    expect(canActivate).toBe(true);
    expect(request['user']).toEqual(payload);
  });
});
