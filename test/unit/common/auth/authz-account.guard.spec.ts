import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { Reflector } from '@nestjs/core';
import { ExecutionContext } from '@nestjs/common';
import { AccountRole } from '@common/enums';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthExceptionStatus } from '@common/exception/types/AuthException';

describe('Common - AuthzAccountGuard', () => {
  let guard: AuthzAccountGuard;
  let reflector: Reflector;

  beforeEach(() => {
    reflector = new Reflector();
    guard = new AuthzAccountGuard(reflector);
  });

  const mockExecutionContext = (
    handlerMetadata: any = {},
    controllerMetadata: any = {},
    user: any = {},
    url: string = '',
  ) => {
    return {
      getHandler: jest.fn().mockReturnValue(handlerMetadata),
      getClass: jest.fn().mockReturnValue(controllerMetadata),
      switchToHttp: () => ({
        getRequest: () => ({
          user,
          url,
        }),
      }),
    } as unknown as ExecutionContext;
  };

  it('should allow access if the route is public (handler)', async () => {
    const handlerMetadata = { [IS_PUBLIC_KEY]: true };
    const context = mockExecutionContext(handlerMetadata, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return true;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_PUBLIC_KEY, [handlerMetadata, {}]);
  });

  it('should allow access if the controller is public', async () => {
    const controllerMetadata = { [IS_PUBLIC_KEY]: true };
    const context = mockExecutionContext({}, controllerMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return true;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(IS_PUBLIC_KEY, [
      {},
      controllerMetadata,
    ]);
  });

  it('should allow access if AuthzAccountGuard is excluded', async () => {
    const excludeGuardsMetadata = { [EXCLUDE_GUARDS_KEY]: ['AuthzAccountGuard'] };
    const context = mockExecutionContext({}, excludeGuardsMetadata);

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }
      if (key === EXCLUDE_GUARDS_KEY) {
        return ['AuthzAccountGuard'];
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith(EXCLUDE_GUARDS_KEY, [
      {},
      excludeGuardsMetadata,
    ]);
  });

  it('should throw AuthException if no required roles are defined', async () => {
    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }
      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return undefined;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, {}, '/no-required-roles')),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::no-account-roles',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_ROUTE,
      code: 'be0a08b8-56c0-4d2d-94af-752a85cff1e8',
      reason: 'No defined any account role in route /no-required-roles',
    });

    expect(reflector.getAllAndOverride).toHaveBeenCalledWith('accountRoles', [{}, {}]);
  });

  it('should allow access if required role is __NONE__', async () => {
    const accountRoles = [AccountRole.__NONE__];
    const context = mockExecutionContext({}, {});

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }

      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should deny access if required role is __NONE__ and has any other role', async () => {
    const accountRoles = [AccountRole.__NONE__, AccountRole.BASIC];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, { accountRole: 'USER' })),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::invalid-account-role',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.INVALID_ACCOUNT_ROLE,
      code: '0a9e1535-31b2-4ff4-a09c-0a7cce10513c',
    });
  });

  it('should throw AuthException if user has no account role', async () => {
    const accountRoles = [AccountRole.BASIC];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, {}, '/no-required-roles')),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::no-account-roles',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_USER,
      code: '789970cf-089b-4b74-9b3a-3c9060577d45',
      reason: 'The user undefined has no account role to access route /no-required-roles',
    });

    expect(reflector.getAllAndOverride).toHaveBeenCalledWith('accountRoles', [{}, {}]);
  });

  it('should deny access if user role is not authorized', async () => {
    const accountRoles = [AccountRole.BASIC];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, { accountRole: 'USER' })),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::invalid-account-role',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.INVALID_ACCOUNT_ROLE,
      code: '0a9e1535-31b2-4ff4-a09c-0a7cce10513c',
    });
  });

  it('should allow access if user role is authorized', async () => {
    const accountRoles = [AccountRole.BASIC];
    const context = mockExecutionContext({}, {}, { accountRole: 'BASIC' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should handle case insensitivity of user role', async () => {
    const accountRoles = [AccountRole.BASIC];
    const context = mockExecutionContext({}, {}, { accountRole: 'basic' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should allow access if user role is authorized and there are multiple required roles', async () => {
    const accountRoles = [AccountRole.PLUS, AccountRole.BASIC];
    const context = mockExecutionContext({}, {}, { accountRole: 'PLUS' });

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    const result = await guard.canActivate(context);
    expect(result).toBe(true);
  });

  it('should deny access if user role is authorized but not in the required roles', async () => {
    const accountRoles = [AccountRole.BASIC];

    jest.spyOn(reflector, 'getAllAndOverride').mockImplementation((key: string) => {
      if (key === IS_PUBLIC_KEY) {
        return false;
      }

      if (key === EXCLUDE_GUARDS_KEY) {
        return undefined;
      }
      if (key === 'accountRoles') {
        return accountRoles;
      }
      return undefined;
    });

    await expect(
      guard.canActivate(mockExecutionContext({}, {}, { accountRole: 'USER' })),
    ).rejects.toMatchObject({
      name: 'AuthzAccountGuard::invalid-account-role',
      message: 'You are not authorized to access this route',
      status: AuthExceptionStatus.INVALID_ACCOUNT_ROLE,
      code: '0a9e1535-31b2-4ff4-a09c-0a7cce10513c',
    });
  });
});
