services:
  postgres:
    container_name: transcendence_e2e_db
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password123
      POSTGRES_DB: transcendence_e2e_db
    ports:
      - '5444:5432'

  opensearch:
    container_name: opensearch_e2e
    image: opensearchproject/opensearch:2.11.1
    environment:
      - discovery.type=single-node
      - plugins.security.disabled=true
      - OPENSEARCH_JAVA_OPTS=-Xms512m -Xmx512m
    ports:
      - '9210:9200'

  localstack:
    container_name: localstack_e2e
    image: localstack/localstack
    ports:
      - '4567:4566'
      - '4572:4571'
      - '8057:8080'
    environment:
      - SERVICES=dynamodb,logs,s3,sqs
      - DEBUG=1
