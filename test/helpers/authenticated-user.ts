import request from 'supertest';
import { INestApplication } from '@nestjs/common';
import * as cookie from 'cookie';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';
import { PayloadTokenEntity } from '@auth/domain/entities/payload-token.entity';
import { JwtService } from '@nestjs/jwt';

export async function getAuthCredentials(
  app: INestApplication,
  email: string,
  password: string,
): Promise<{ accessToken: string; refreshToken: string; payload: PayloadTokenEntity }> {
  const jwtService = new JwtService({ secret: process.env.JWT_SECRET });
  const response = await request(app.getHttpServer()).post('/api/v1/auth/session/login').send({
    email,
    password,
  });

  const cookies = response.headers['set-cookie'];

  if (!Array.isArray(cookies)) {
    throw new Error("Expected 'set-cookie' to be an array.");
  }

  let accessToken = '';
  let refreshToken = '';

  cookies.forEach(cookieStr => {
    const parsedCookie = cookie.parse(cookieStr);
    if (parsedCookie['digai.accessToken']) {
      accessToken = parsedCookie['digai.accessToken'];
    }
    if (parsedCookie['digai.refreshToken']) {
      refreshToken = parsedCookie['digai.refreshToken'];
    }
  });

  if (!accessToken || !refreshToken) {
    throw new Error('Failed to extract accessToken or refreshToken from cookies.');
  }

  let payload: PayloadTokenEntity;
  try {
    payload = await jwtService.verifyAsync(accessToken, {
      secret: process.env.JWT_SECRET,
    });
  } catch {
    throw new AuthException(
      'AuthnGuard::invalid-signature-in-getAuthenticatedUser',
      '[E2E Test] You must be authenticated to access this route',
      AuthExceptionStatus.INVALID_TOKEN_SIGNATURE,
      'b0761d54-16d2-4f2a-8869-fc91ce814597',
    );
  }

  return { accessToken, refreshToken, payload };
}
