model knowledgeVector {
  id             String                 @id @default(cuid())
  originId       String                 @map("origin_id") @db.VarChar(36)
  businessUserId String                 @map("business_user_id") @db.VarChar(36)
  scopeId        String?                @default("invalid_item_id") @map("scope_id") @db.VarChar(36)
  itemId         String                 @map("item_id") @db.VarChar(36)
  relatedUserId  String?                @map("related_user_id") @db.VarChar(36)
  type           String                 @default("TEXT")
  embeddingModel String                 @default("text-embedding-3-large") @map("embedding_model")
  content        String
  vector         Unsupported("vector")?
  isBackfill     Boolean                @default(false) @map("is_backfill")
  status         String                 @default("ACTIVE")
  createdAt      DateTime               @default(now()) @map(name: "created_at")
  updatedAt      DateTime               @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "data_insights__knowledge_vector")
  @@schema("public")
}
