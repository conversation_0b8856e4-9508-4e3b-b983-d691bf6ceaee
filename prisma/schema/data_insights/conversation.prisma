model conversation {
  id             String   @id @default(uuid()) @db.Uuid
  sessionId      String   @map(name: "session_id") @db.Uuid
  originId       String   @map(name: "origin_id") @db.Uuid
  agentId        String   @map(name: "agent_id") @db.Uuid
  businessUserId String   @map(name: "business_user_id") @db.Uuid
  subject        String?  @map(name: "subject")
  type           String
  status         String   @default("ACTIVE")
  createdAt      DateTime @default(now()) @map(name: "created_at")
  updatedAt      DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "conversation")
  @@schema("data_insights")
}

