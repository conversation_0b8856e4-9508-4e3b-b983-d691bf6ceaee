model outgoingMessage {
  id             String    @id @default(uuid()) @map(name: "id") @db.Uuid
  customerId     String    @map(name: "customer_id") @db.Uuid
  from           String?
  to             String
  messageType    String    @default("TEXT") @map("message_type")
  message        String?
  channel        String    @default("WHATSAPPSELFHOSTED") @map("communication_channel")
  timeToGo       DateTime  @map(name: "time_to_go")
  sent           Boolean   @default(false)
  sentAt         DateTime? @map(name: "sent_at")
  status         String    @default("ACTIVE")
  apiUrl         String?   @default("whatsapp://send?phone=") @map("api_url")
  fileUrl        String?   @map("file_url")
  isfirstMessage Boolean?  @map(name: "is_first_message")
  createdAt      DateTime  @default(now()) @map(name: "created_at")
  updatedAt      DateTime  @updatedAt @map(name: "updated_at")

  @@unique([to, message, timeToGo], name: "outgoing_message_to_message_time_to_go_unique")
  @@index([customerId], name: "outgoing_message_customerid_index")
  @@map(name: "outgoing_message")
  @@schema("message_hub")
}
