model incomingMessage {
  id          String    @id @default(uuid()) @map(name: "id") @db.Uuid
  from        String
  to          String
  messageType String    @default("TEXT") @map("message_type")
  message     String?
  channel     String    @default("WHATSAPPSELFHOSTED") @map("communication_channel")
  fileUrl    String?   @map(name: "file_url")
  read        Boolean   @default(false)
  readAt      DateTime? @map(name: "read_at")
  status      String    @default("ACTIVE")
  createdAt   DateTime  @default(now()) @map(name: "created_at")
  updatedAt   DateTime  @updatedAt @map(name: "updated_at")

  @@index([from], name: "incoming_message_from_index")
  @@map(name: "incoming_message")
  @@schema("message_hub")
}
