model customerCommunicationChannel {
  id                String   @id @default(uuid()) @map(name: "id") @db.Uuid
  customerId        String   @map(name: "customer_id") @db.Uuid
  portfolioId       String?  @map(name: "portfolio_id") @db.Uuid
  channel           String   @default("WHATSAPPSELFHOSTED")
  integrationDataId String   @map(name: "integration_data_id") @db.Uuid
  status            String   @default("ACTIVE")
  createdAt         DateTime @default(now()) @map(name: "created_at")
  updatedAt         DateTime @updatedAt @map(name: "updated_at")

  @@unique([customerId, portfolioId, channel], name: "customer_communication_channel_customer_id_portfolio_id_channel_key")
  @@index([customerId], name: "customer_communication_channel_customerid_index")
  @@map(name: "customer_communication_channel")
  @@schema("message_hub")
}
