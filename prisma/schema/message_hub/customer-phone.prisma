model customerPhone {
  customerId           String   @map(name: "customer_id") @db.Uuid
  phoneNumber          String   @map(name: "phone_number")
  communicationChannel String   @default("WHATSAPPSELFHOSTED") @map(name: "communication_channel")
  apiUrl               String?  @map(name: "api_url")
  status               String   @default("ACTIVE")
  incomingCron         String   @default("*/2 * * * *") @map(name: "incoming_cron")
  outgoingCron         String   @default("* * * * * *") @map(name: "outgoing_cron")
  outgoingMaxDelay     BigInt   @default(50) @map(name: "outgoing_max_delay")
  dailyLimit           Int      @default(100) @map(name: "daily_limit")
  weight               BigInt   @default(100)
  createdAt            DateTime @default(now()) @map(name: "created_at")
  updatedAt            DateTime @updatedAt @map(name: "updated_at")

  @@id([customerId, phoneNumber, communicationChannel], name: "customer_phones_pkey")
  @@index([customerId], name: "customer_phones_customer_id_index")
  @@map(name: "customer_phone")
  @@schema("message_hub")
}
