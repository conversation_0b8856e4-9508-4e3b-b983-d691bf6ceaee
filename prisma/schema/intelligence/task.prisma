model task {
  id               String   @id @default(uuid()) @db.Uuid
  name             String?
  description      String
  agent            String
  responseTemplate String?  @map(name: "response_template")
  managerAgentId   String?  @map(name: "manager_agent_id") @db.VarChar(128)
  status           String   @default("ACTIVE")
  createdAt        DateTime @default(now()) @map(name: "created_at")
  updatedAt        DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "task")
  @@schema("intelligence")
}
