model agent {
  id                      String               @id @default(uuid()) @db.Uuid
  role                    String
  name                    String?              @db.<PERSON>ar<PERSON>har(128)
  backstory               String
  llmModel                String               @map(name: "llm_model")
  outputType              String               @default("TEXT") @map(name: "output_type") 
  lang                    String
  voice                   String?              @db.<PERSON>ar<PERSON>har(128)
  status                  String               @default("ACTIVE")
  createdAt               DateTime             @default(now()) @map(name: "created_at")
  updatedAt               DateTime             @updatedAt @map(name: "updated_at")

  @@index([id])
  @@unique([name, lang], name: "agent_name_lang_key")
  @@map(name: "agent")
  @@schema("intelligence")
}
