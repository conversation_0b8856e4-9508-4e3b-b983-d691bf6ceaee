model portfolioItemExecutionHistory {
    id                      String                @id @default(uuid()) @db.Uuid
    portfolioItemId         String                @db.Uuid @map(name: "portfolio_item_id")
    oldStatus               String                @map(name: "old_status")
    newStatus               String                @map(name: "new_status")
    reason                  String?               @map(name: "reason") 
    status                  String                @default("ACTIVE")
    createdAt               DateTime              @default(now()) @map(name: "created_at")
    updatedAt               DateTime              @updatedAt @map(name: "updated_at")
    
    @@index([id])
    @@map(name: "portfolio_item_execution_history")
    @@schema("business_base")
}