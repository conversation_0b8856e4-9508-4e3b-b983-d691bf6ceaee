model portfolioItemStatusMapping {
  workflowId                        String  @db.Uuid @map(name: "workflow_id")
  postExecutionResponse             String  @map(name: "post_execution_response")
  responseKey                       String  @map(name: "response_key")
  portfolioItemStatus               String  @map(name: "portfolio_item_status")
  status          String            @default("ACTIVE")
  createdAt       DateTime          @default(now()) @map(name: "created_at")
  updatedAt       DateTime          @updatedAt @map(name: "updated_at")

  @@id([workflowId, postExecutionResponse, portfolioItemStatus], name: "portfolio_item_status_mapping_pkey")
  @@index([workflowId], name: "portfolio_item_status_mapping_workflowid_index")
  @@map(name: "portfolio_item_status_mapping")
  @@schema("business_base")
}