model portfolioItemScheduledFollowUp {
  id              String    @id @default(uuid()) @db.Uuid
  portfolioItemId String    @map(name: "portfolio_item_id") @db.Uuid
  workflowId      String    @map(name: "workflow_id") @db.Uuid
  scheduledTo     DateTime  @map(name: "scheduled_to")
  doneAt          DateTime? @map(name: "done_at")
  isDone          Boolean   @map(name: "is_done") @default(false)
  type            String    @default("FOLLOW_UP")
  status          String    @default("ACTIVE")
  createdAt       DateTime  @default(now()) @map(name: "created_at")
  updatedAt       DateTime  @updatedAt @map(name: "updated_at")

  @@unique([portfolioItemId, scheduledTo])
  @@index([portfolioItemId, scheduledTo], name: "portfolio_item_scheduled_follow_up_index")
  @@map(name: "portfolio_item_scheduled_follow_up")
  @@schema("business_base")
}
