model portfolioItemImportError {
    id                      String               @id @default(uuid()) @db.Uuid
    portfolioId             String               @db.Uuid @map(name: "portfolio_id")
    lineNumber              BigInt               @map(name: "line_number")
    reason                  String
    status                  String               @default("ACTIVE")           
    createdAt               DateTime             @default(now()) @map(name: "created_at")
    updatedAt               DateTime             @updatedAt @map(name: "updated_at")    

    @@index([id])
    @@map(name: "portfolio_item_import_error")
    @@schema("business_base")
}