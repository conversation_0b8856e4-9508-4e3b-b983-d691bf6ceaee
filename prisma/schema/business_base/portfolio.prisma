model portfolio {
  id                   String    @id @default(uuid()) @db.Uuid
  name                 String
  customerId           String    @map(name: "customer_id") @db.Uuid
  workflowId           String    @map(name: "workflow_id") @db.Uuid
  executionStatus      String    @default("WAITING") @map(name: "execution_status")
  importStatus         String    @default("UPLOADED") @map(name: "import_status")
  originalFileName     String?   @map(name: "original_file_name")
  fileUrl              String?   @map(name: "file_url")
  workExpression       String    @map(name: "work_expression")
  followUpWorkflowId   String?   @map(name: "follow_up_workflow_id") @db.Uuid
  followUpExpression   String    @default("0 11-23/3 * * *") @map(name: "follow_up_expression")
  followUpAfter        Int       @default(175) @map(name: "follow_up_after")
  maxFollowUps         Int       @default(1) @map(name: "max_follow_ups")
  timezoneUTC          String    @default("-3") @map(name: "timezone_utc")
  communicationChannel String    @default("WHATSAPPSELFHOSTED") @map(name: "communication_channel")
  totalQuantity        BigInt    @default(0) @map(name: "total_quantity")
  processedQuantity    BigInt    @default(0) @map(name: "processed_quantity")
  totalSuccessQuantity BigInt    @default(0) @map(name: "total_success_quantity")
  totalFailedQuantity  BigInt    @default(0) @map(name: "total_failed_quantity")
  importFinishedAt     DateTime? @map(name: "import_finished_at")
  executeImmediately   Boolean   @default(false) @map(name: "execute_immediately")
  processingRateLimit  Int       @default(0) @map(name: "processing_rate_limit")
  isDefault            Boolean   @default(false) @map(name: "is_default")
  idleAfter            Int?      @map(name: "idle_after")
  status               String    @default("ACTIVE")
  createdAt            DateTime  @default(now()) @map(name: "created_at")
  updatedAt            DateTime  @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "portfolio")
  @@schema("business_base")
}
