model customerWorkflow {
  id         String   @id @default(uuid()) @db.Uuid
  customerId String   @map(name: "customer_id") @db.Uuid
  workflowId String   @map(name: "workflow_id") @db.Uuid
  status     String   @default("ACTIVE")
  createdAt  DateTime @default(now()) @map(name: "created_at")
  updatedAt  DateTime @updatedAt @map(name: "updated_at")

  @@unique([customerId, workflowId], name: "customer_workflow_customer_id_workflow_id_key")
  @@index([id])
  @@map(name: "customer_workflow")
  @@schema("business_base")
}
