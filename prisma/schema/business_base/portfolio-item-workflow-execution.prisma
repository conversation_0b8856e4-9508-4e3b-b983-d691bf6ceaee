model portfolioItemWorkflowExecution {
  id                    String   @id @default(uuid()) @db.Uuid
  portfolioItemId       String   @map(name: "portfolio_item_id") @db.Uuid
  workflowExecutionId   String   @map(name: "workflow_execution_id") @db.Uuid
  followFlowExecutionId String?  @map(name: "follow_flow_execution_id") @db.Uuid
  status                String   @default("ACTIVE")
  createdAt             DateTime @default(now()) @map(name: "created_at")
  updatedAt             DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "portfolio_item_workflow_execution")
  @@schema("business_base")
}
