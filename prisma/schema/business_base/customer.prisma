model customer {
  id            String   @id @default(uuid()) @db.Uuid
  name          String
  cnpj          String
  email         String   @unique
  phone         String
  whatsappPhone String   @map("whatsapp_phone") @unique
  status        String   @default("ACTIVE")
  segment       String
  createdAt     DateTime @default(now()) @map(name: "created_at")
  updatedAt     DateTime @updatedAt @map(name: "updated_at")

  @@index([id])
  @@map(name: "customer")
  @@schema("business_base")
}
