model User {
    id            String   @id @default(uuid()) @db.Uuid
    email         String   @unique @db.VarChar(64)
    firstname     String   @db.VarChar(64)
    lastname      String   @db.VarChar(64)
    password      String   @db.VarChar(64)
    salt          String   @db.VarChar(64)
    roleInAccount String   @map(name: "role_in_account")
    accountId     String   @map(name: "account_id") @db.Uuid
    status        String   @default("ACTIVE")
    createdAt     DateTime @default(now()) @map(name: "created_at")
    updatedAt     DateTime @default(now()) @map(name: "updated_at")

    @@map(name: "user")
    @@schema("auth")
}
