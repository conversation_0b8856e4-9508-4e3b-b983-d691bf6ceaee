model Account {
    id         String   @id @default(uuid()) @db.Uuid
    name       String   @map(name: "name") @db.VarChar(64) @unique
    role       String   @default("BASIC")
    status     String   @default("ACTIVE")
    customerId String   @map(name: "customer_id") @db.Uuid
    createdAt  DateTime @default(now()) @map(name: "created_at")
    updatedAt  DateTime @default(now()) @map(name: "updated_at")

    @@map(name: "account")
    @@schema("auth")
}
