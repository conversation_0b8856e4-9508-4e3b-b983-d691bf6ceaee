model Token {
    id        String   @id @default(uuid()) @db.Uuid
    userId    String   @map(name: "user_id") @db.VarChar(64)
    token     String
    reference String
    status    String   @default("ACTIVE")
    createdAt DateTime @default(now()) @map(name: "created_at")
    expireAt  DateTime @map(name: "expire_at")
    updatedAt DateTime @default(now()) @map(name: "updated_at")

    @@unique([token])
    @@index([token])
    @@map(name: "token")
    @@schema("auth")
}
