-- AlterTable
ALTER TABLE "business_base"."portfolio" ADD COLUMN     "follow_up_after" INTEGER NOT NULL DEFAULT 24,
ADD COLUMN     "follow_up_expression" TEXT NOT NULL DEFAULT '0 11-23 * * 1-5',
ADD COLUMN     "follow_up_workflow_id" UUID,
ADD COLUMN     "max_follow_ups" INTEGER NOT NULL DEFAULT 5;

-- AlterTable
ALTER TABLE "business_base"."portfolio_item" ADD COLUMN     "follow_up_count" INTEGER NOT NULL DEFAULT 0;

-- AlterTable
ALTER TABLE "business_base"."portfolio_item_workflow_execution" ADD COLUMN     "follow_flow_execution_id" UUID;
