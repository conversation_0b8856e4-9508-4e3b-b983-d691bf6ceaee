/*
  Warnings:

  - You are about to drop the `customer_whatsapp_phones` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `customer_whatsapp_phones_destination` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
DROP TABLE "message_hub"."customer_whatsapp_phones";

-- DropTable
DROP TABLE "message_hub"."customer_whatsapp_phones_destination";

-- CreateTable
CREATE TABLE "message_hub"."customer_phones_destination" (
    "customer_id" UUID NOT NULL,
    "phone_number" TEXT NOT NULL,
    "communication_channel" TEXT NOT NULL DEFAULT 'WHATSAPPSELFHOSTED',
    "destination" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_phones_destination_pkey" PRIMARY KEY ("customer_id","phone_number","destination")
);

-- CreateTable
CREATE TABLE "message_hub"."customer_phone" (
    "customer_id" UUID NOT NULL,
    "phone_number" TEXT NOT NULL,
    "communication_channel" TEXT NOT NULL DEFAULT 'WHATSAPPSELFHOSTED',
    "api_url" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_phone_pkey" PRIMARY KEY ("customer_id","phone_number")
);

-- CreateIndex
CREATE INDEX "customer_phone_destination_customer_id_index" ON "message_hub"."customer_phones_destination"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_phones_destination_customer_id_destination_communi_key" ON "message_hub"."customer_phones_destination"("customer_id", "destination", "communication_channel");

-- CreateIndex
CREATE INDEX "customer_phones_customer_id_index" ON "message_hub"."customer_phone"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_phone_phone_number_key" ON "message_hub"."customer_phone"("phone_number");

-- CreateIndex
CREATE UNIQUE INDEX "customer_phone_api_url_key" ON "message_hub"."customer_phone"("api_url");

-- CreateIndex
CREATE UNIQUE INDEX "customer_phone_phone_number_api_url_key" ON "message_hub"."customer_phone"("phone_number", "api_url");
