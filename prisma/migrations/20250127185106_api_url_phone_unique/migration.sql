/*
  Warnings:

  - The primary key for the `customer_phone` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropIndex
DROP INDEX "message_hub"."customer_phone_api_url_key";

-- DropIndex
DROP INDEX "message_hub"."customer_phone_phone_number_api_url_key";

-- DropIndex
DROP INDEX "message_hub"."customer_phone_phone_number_key";

-- AlterTable
ALTER TABLE "message_hub"."customer_phone" DROP CONSTRAINT "customer_phone_pkey",
ADD CONSTRAINT "customer_phone_pkey" PRIMARY KEY ("customer_id", "phone_number", "communication_channel");
