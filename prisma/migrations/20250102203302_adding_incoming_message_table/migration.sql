-- CreateTable
CREATE TABLE "message_hub"."incoming_message" (
    "id" UUID NOT NULL,
    "from" TEXT NOT NULL,
    "to" TEXT NOT NULL,
    "message_type" TEXT NOT NULL DEFAULT 'TEXT',
    "message" TEXT NOT NULL,
    "communication_channel" TEXT NOT NULL DEFAULT 'WHATSAPPSELFHOSTED',
    "read" BOOLEAN NOT NULL DEFAULT false,
    "read_at" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "incoming_message_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "incoming_message_from_index" ON "message_hub"."incoming_message"("from");
