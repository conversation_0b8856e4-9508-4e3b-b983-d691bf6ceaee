/*
  Warnings:

  - Added the required column `item_id` to the `data_insights__knowledge_vector` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "public"."data_insights__knowledge_vector" ADD COLUMN     "item_id" VARCHAR(36) NOT NULL,
ALTER COLUMN "origin_id" SET DATA TYPE VARCHAR(36),
ALTER COLUMN "business_user_id" SET DATA TYPE VARCHAR(36),
ALTER COLUMN "scope_id" DROP NOT NULL,
ALTER COLUMN "scope_id" SET DEFAULT 'invalid_item_id',
ALTER COLUMN "scope_id" SET DATA TYPE VARCHAR(36);
