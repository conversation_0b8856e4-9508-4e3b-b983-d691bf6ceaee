-- CreateTable
CREATE TABLE "message_hub"."customer_whatsapp_phones" (
    "customer_id" UUID NOT NULL,
    "phone_number" TEXT NOT NULL,
    "self_hosted_link" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_whatsapp_phones_pkey" PRIMARY KEY ("customer_id","phone_number")
);

-- CreateIndex
CREATE INDEX "customer_whatsapp_phones_customer_id_index" ON "message_hub"."customer_whatsapp_phones"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_whatsapp_phones_phone_number_key" ON "message_hub"."customer_whatsapp_phones"("phone_number");

-- CreateIndex
CREATE UNIQUE INDEX "customer_whatsapp_phones_self_hosted_link_key" ON "message_hub"."customer_whatsapp_phones"("self_hosted_link");

-- CreateIndex
CREATE UNIQUE INDEX "customer_whatsapp_phones_phone_number_self_hosted_link_stat_key" ON "message_hub"."customer_whatsapp_phones"("phone_number", "self_hosted_link", "status");
