-- Enabling the vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- CreateTable
CREATE TABLE "public"."data_insights__knowledge_vector" (
    "id" TEXT NOT NULL,
    "origin_id" UUID NOT NULL,
    "business_user_id" UUID NOT NULL,
    "scope_id" UUID NOT NULL,
    "type" TEXT NOT NULL DEFAULT 'TEXT',
    "content" TEXT NOT NULL,
    "vector" vector,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "data_insights__knowledge_vector_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "data_insights__knowledge_vector_id_idx" ON "public"."data_insights__knowledge_vector"("id");
