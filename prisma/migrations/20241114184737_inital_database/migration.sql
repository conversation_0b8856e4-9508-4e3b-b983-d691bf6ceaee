-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "business_base";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "intelligence";

-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "message_hub";

-- CreateTable
CREATE TABLE "business_base"."customer_workflow" (
    "id" UUID NOT NULL,
    "customer_id" UUID NOT NULL,
    "workflow_id" UUID NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_workflow_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."customer" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "cnpj" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "whatsapp_phone" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "segment" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio_item_execution_history" (
    "id" UUID NOT NULL,
    "portfolio_item_id" UUID NOT NULL,
    "old_status" TEXT NOT NULL,
    "new_status" TEXT NOT NULL,
    "reason" TEXT,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_execution_history_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio_item_import_error" (
    "id" UUID NOT NULL,
    "portfolio_id" UUID NOT NULL,
    "line_number" BIGINT NOT NULL,
    "reason" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_import_error_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio_item_status_mapping" (
    "workflow_id" UUID NOT NULL,
    "post_execution_response" TEXT NOT NULL,
    "response_key" TEXT NOT NULL,
    "portfolio_item_status" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_status_mapping_pkey" PRIMARY KEY ("workflow_id","post_execution_response","portfolio_item_status")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio_item_workflow_execution" (
    "id" UUID NOT NULL,
    "portfolio_item_id" UUID NOT NULL,
    "workflow_execution_id" UUID NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_workflow_execution_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio_item" (
    "id" UUID NOT NULL,
    "portfolio_id" UUID NOT NULL,
    "current_status" TEXT,
    "phone_number" TEXT NOT NULL,
    "custom_data_id" UUID NOT NULL,
    "line" BIGINT NOT NULL,
    "last_interaction" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "business_base"."portfolio" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "customer_id" UUID NOT NULL,
    "workflow_id" UUID NOT NULL,
    "execution_status" TEXT NOT NULL DEFAULT 'WAITING',
    "import_status" TEXT NOT NULL DEFAULT 'UPLOADED',
    "original_file_name" TEXT NOT NULL,
    "file_url" TEXT NOT NULL,
    "work_expression" TEXT NOT NULL,
    "total_quantity" BIGINT NOT NULL DEFAULT 0,
    "processed_quantity" BIGINT NOT NULL DEFAULT 0,
    "total_success_quantity" BIGINT NOT NULL DEFAULT 0,
    "total_failed_quantity" BIGINT NOT NULL DEFAULT 0,
    "import_finished_at" TIMESTAMP(3),
    "execute_immediately" BOOLEAN NOT NULL DEFAULT false,
    "processing_rate_limit" INTEGER NOT NULL DEFAULT 0,
    "idle_after" INTEGER,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "intelligence"."agent" (
    "id" UUID NOT NULL,
    "role" TEXT NOT NULL,
    "backstory" TEXT NOT NULL,
    "llm_model" TEXT NOT NULL,
    "output_type" TEXT NOT NULL DEFAULT 'TEXT',
    "lang" TEXT NOT NULL,
    "voice" VARCHAR(128),
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "agent_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "intelligence"."task" (
    "id" UUID NOT NULL,
    "description" TEXT NOT NULL,
    "agent" TEXT NOT NULL,
    "response_template" TEXT,
    "manager_agent_id" VARCHAR(128),
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "task_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "message_hub"."customer_communication_channel" (
    "id" UUID NOT NULL,
    "customer_id" UUID NOT NULL,
    "portfolio_id" UUID,
    "channel" TEXT NOT NULL DEFAULT 'WHATSAPPSELFHOSTED',
    "integration_data_id" UUID NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_communication_channel_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "customer_workflow_id_idx" ON "business_base"."customer_workflow"("id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_workflow_customer_id_workflow_id_key" ON "business_base"."customer_workflow"("customer_id", "workflow_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_email_key" ON "business_base"."customer"("email");

-- CreateIndex
CREATE INDEX "customer_id_idx" ON "business_base"."customer"("id");

-- CreateIndex
CREATE INDEX "portfolio_item_execution_history_id_idx" ON "business_base"."portfolio_item_execution_history"("id");

-- CreateIndex
CREATE INDEX "portfolio_item_import_error_id_idx" ON "business_base"."portfolio_item_import_error"("id");

-- CreateIndex
CREATE INDEX "portfolio_item_status_mapping_workflowid_index" ON "business_base"."portfolio_item_status_mapping"("workflow_id");

-- CreateIndex
CREATE INDEX "portfolio_item_workflow_execution_id_idx" ON "business_base"."portfolio_item_workflow_execution"("id");

-- CreateIndex
CREATE INDEX "portfolio_item_id_idx" ON "business_base"."portfolio_item"("id");

-- CreateIndex
CREATE INDEX "portfolio_id_idx" ON "business_base"."portfolio"("id");

-- CreateIndex
CREATE INDEX "agent_id_idx" ON "intelligence"."agent"("id");

-- CreateIndex
CREATE INDEX "task_id_idx" ON "intelligence"."task"("id");

-- CreateIndex
CREATE INDEX "customer_communication_channel_customerid_index" ON "message_hub"."customer_communication_channel"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_communication_channel_customer_id_portfolio_id_cha_key" ON "message_hub"."customer_communication_channel"("customer_id", "portfolio_id", "channel");
