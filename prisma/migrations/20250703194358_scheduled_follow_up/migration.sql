-- AlterTable
ALTER TABLE "business_base"."portfolio" ALTER COLUMN "follow_up_after" SET DEFAULT 175,
ALTER COLUMN "follow_up_expression" SET DEFAULT '0 11-23/3 * * *',
ALTER COLUMN "max_follow_ups" SET DEFAULT 1;

-- CreateTable
CREATE TABLE "business_base"."portfolio_item_scheduled_follow_up" (
    "id" UUID NOT NULL,
    "portfolio_item_id" UUID NOT NULL,
    "workflow_id" UUID NOT NULL,
    "scheduled_to" TIMESTAMP(3) NOT NULL,
    "done_at" TIMESTAMP(3),
    "is_done" BOOLEAN NOT NULL DEFAULT false,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "portfolio_item_scheduled_follow_up_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "portfolio_item_scheduled_follow_up_index" ON "business_base"."portfolio_item_scheduled_follow_up"("portfolio_item_id", "scheduled_to");

-- CreateIndex
CREATE UNIQUE INDEX "portfolio_item_scheduled_follow_up_portfolio_item_id_schedu_key" ON "business_base"."portfolio_item_scheduled_follow_up"("portfolio_item_id", "scheduled_to");
