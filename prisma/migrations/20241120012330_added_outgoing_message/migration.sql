-- CreateTable
CREATE TABLE "message_hub"."outgoing_message" (
    "id" UUID NOT NULL,
    "customer_id" UUID NOT NULL,
    "to" TEXT NOT NULL,
    "message_type" TEXT NOT NULL DEFAULT 'TEXT',
    "message" TEXT NOT NULL,
    "communication_channel" TEXT NOT NULL DEFAULT 'WHATSAPPSELFHOSTED',
    "time_to_go" TIMESTAMP(3) NOT NULL,
    "sent" BOOLEAN NOT NULL DEFAULT false,
    "sent_at" TIMESTAMP(3),
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "outgoing_message_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "outgoing_message_customerid_index" ON "message_hub"."outgoing_message"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "outgoing_message_to_message_time_to_go_key" ON "message_hub"."outgoing_message"("to", "message", "time_to_go");
