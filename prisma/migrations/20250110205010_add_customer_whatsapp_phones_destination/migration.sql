/*
  Warnings:

  - A unique constraint covering the columns `[phone_number,self_hosted_link]` on the table `customer_whatsapp_phones` will be added. If there are existing duplicate values, this will fail.

*/
-- DropIndex
DROP INDEX "message_hub"."customer_whatsapp_phones_phone_number_self_hosted_link_stat_key";

-- CreateTable
CREATE TABLE "message_hub"."customer_whatsapp_phones_destination" (
    "customer_id" UUID NOT NULL,
    "phone_number" TEXT NOT NULL,
    "destination" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "customer_whatsapp_phones_destination_pkey" PRIMARY KEY ("customer_id","phone_number","destination")
);

-- CreateIndex
CREATE INDEX "customer_whatsapp_phone_destination_customer_id_index" ON "message_hub"."customer_whatsapp_phones_destination"("customer_id");

-- CreateIndex
CREATE UNIQUE INDEX "customer_whatsapp_phones_phone_number_self_hosted_link_key" ON "message_hub"."customer_whatsapp_phones"("phone_number", "self_hosted_link");
