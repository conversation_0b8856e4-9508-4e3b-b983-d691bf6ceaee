-- CreateSchema
CREATE SCHEMA IF NOT EXISTS "data_insights";

-- CreateTable
CREATE TABLE "data_insights"."conversation" (
    "id" UUID NOT NULL,
    "session_id" UUID NOT NULL,
    "origin_id" UUID NOT NULL,
    "agent_id" UUID NOT NULL,
    "business_user_id" UUID NOT NULL,
    "subject" TEXT,
    "type" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "conversation_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "conversation_id_idx" ON "data_insights"."conversation"("id");
