#AWS
/dynamo

# Docker
docker.env
.dockerignore
Dockerfile
DockerfileProd
DockerfileStaging
docker-compose-local.yml

# Github
.git
.github
.gitignore

## compiled output
/dist
/node_modules
.env*

# DB
/.docker/dbdata
/prisma/.env
!/prisma/migrations/
!/prisma/migrations/migration_lock.toml

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output
tests/
__tests__/

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
/load-test

# misc
*.md
.eslint*
.prettier*
