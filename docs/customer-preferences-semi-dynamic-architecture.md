# Customer Preferences Semi-Dynamic Architecture

## Overview

The customer preferences CRUD system implements a **semi-dynamic architecture** that provides strict DTO validation at the API boundary while maintaining dynamic behavior in service and repository layers. This approach prevents data corruption while enabling flexible property handling for future enhancements.

## Architecture Components

### 1. API Layer - Strict Validation
- **Validation**: Uses `ValidationPipe` with `whitelist: true` and `forbidNonWhitelisted: true`
- **Purpose**: Rejects any properties not explicitly defined in DTOs
- **Implementation**: Applied to POST and PUT endpoints via `@UsePipes` decorator

### 2. Service Layer - Dynamic Handling
- **Behavior**: Automatically processes any properties defined in DTOs through dynamic object operations
- **Implementation**: Uses object spreading (`...dynamicProperties`) instead of hardcoded property names
- **Benefit**: No code changes needed when new properties are added to DTOs

### 3. Repository Layer - Dynamic Persistence
- **Behavior**: Automatically persists any properties from entities using dynamic object assignment
- **Implementation**: Uses `Object.assign()` and object spreading for data persistence
- **Benefit**: No code changes needed when new properties are added to entities

## Key Features

### Strict DTO Validation
```typescript
@UsePipes(new ValidationPipe({ 
  whitelist: true, 
  forbidNonWhitelisted: true, 
  transform: true,
  transformOptions: { enableImplicitConversion: true }
}))
```

### Dynamic Property Handling
```typescript
// Extract dynamic properties (excluding portfolio to avoid conflicts)
const { portfolio, ...dynamicProperties } = createCustomerPreferencesDto;

const entity = new CustomerPreferencesEntity({
  customerId,
  portfolio: portfolioPreferences,
  ...dynamicProperties, // Automatically handles any DTO properties
});
```

### Prevented Data Corruption Example
The following invalid data is now **rejected** at the API boundary:
```json
{
  "portfolio": {
    "exportColumns": [],
    "followUpWorkflowId": "7f413811-4aa8-43f4-8c48-d00143dd226e",
    "prop": "value" // ❌ Not defined in DTO - REJECTED
  },
  "teste": {}, // ❌ Not defined in DTO - REJECTED
  "unknownProperty": "value" // ❌ Not defined in DTO - REJECTED
}
```

## Adding New Properties

To add new properties to the system:

1. **Define in DTO** with proper validation:
```typescript
@ApiProperty({
  description: 'New property description',
  example: 'example-value',
  required: false,
})
@IsString({ message: 'newProperty must be a string' })
@IsOptional()
readonly newProperty?: string;
```

2. **Define in Update DTO** with the same validation:
```typescript
@ApiProperty({
  description: 'New property description',
  example: 'example-value',
  required: false,
})
@IsString({ message: 'newProperty must be a string' })
@IsOptional()
readonly newProperty?: string;
```

3. **Define in Entity** with validation:
```typescript
@IsString()
@IsOptional()
newProperty?: string;
```

4. **Service and Repository layers automatically handle the new property** - no code changes needed!

### Example: Adding the `test` Property

Here's how the `test` property was successfully added:

**CustomerPreferencesDto:**
```typescript
@ApiProperty({
  description: 'A test property to demonstrate semi-dynamic architecture',
  example: 'test-value',
  required: false,
})
@IsString({ message: 'test must be a string' })
@IsOptional()
readonly test?: string;
```

**UpdateCustomerPreferencesDto:**
```typescript
@ApiProperty({
  description: 'A test property to demonstrate semi-dynamic architecture',
  example: 'test-value',
  required: false,
})
@IsString({ message: 'test must be a string' })
@IsOptional()
readonly test?: string;
```

**CustomerPreferencesEntity:**
```typescript
@IsString()
@IsOptional()
test?: string;
```

**Result:** The `test` property now flows automatically through all layers without requiring any changes to service or repository code.

## Benefits

- **Data Integrity**: Prevents corruption by rejecting undefined properties at the API boundary
- **Type Safety**: Explicit DTO definitions with validation decorators ensure proper validation
- **Maintainability**: Service/repository layers require no changes for new properties - they handle them automatically through dynamic object operations
- **Backward Compatibility**: Existing properties continue to work unchanged when new properties are added
- **Developer Experience**: Clear validation errors guide proper API usage and prevent accidental data corruption
- **Flexibility**: Easy to add new properties by simply defining them in DTOs and entities
- **Consistency**: All properties flow through the same validation and processing pipeline

## Testing

Comprehensive test coverage includes:
- ✅ Rejection of unknown root-level properties (`unknownProperty`, `anotherUnknownProp`)
- ✅ Rejection of unknown nested properties (`portfolio.unknownNestedProp`, `portfolio.prop`)
- ✅ Validation of the exact corruption example (`teste`, `portfolio.prop`)
- ✅ Proper handling of valid defined properties (`test`, `portfolio.timezoneUTC`)
- ✅ End-to-end CRUD operations with strict validation
- ✅ Semi-dynamic architecture demonstration with new property acceptance

### Test Results
All 29 tests pass, including:
- **"should accept new test property - demonstrates semi-dynamic architecture"** (POST)
- **"should accept new test property in PUT requests - demonstrates semi-dynamic architecture"** (PUT)
- **"should reject unknown properties at root level - strict DTO validation"**
- **"should reject unknown properties in nested portfolio object - strict DTO validation"**
- **"should reject the exact invalid data from the example - prevents data corruption"**

## API Documentation

The Swagger documentation has been updated to reflect:
- Strict validation behavior in operation descriptions
- Enhanced error response examples showing unknown property rejection
- Clear guidance on acceptable properties

## Common Pitfalls to Avoid

When adding new properties, avoid these common mistakes:

1. **❌ Wrong Type Declaration:**
```typescript
// Don't use @Type() for simple types
@Type(() => TestDto)
readonly test?: TestDto; // This expects an object, not a string
```

2. **✅ Correct Type Declaration:**
```typescript
// Use appropriate validators for simple types
@IsString({ message: 'test must be a string' })
readonly test?: string; // This expects a string value
```

3. **❌ Forgetting Update DTO:**
```typescript
// Don't forget to add the property to UpdateCustomerPreferencesDto
// Otherwise PUT requests won't accept the new property
```

4. **❌ Missing Entity Definition:**
```typescript
// Don't forget to add the property to the entity
// Otherwise the property won't be persisted
```

## Conclusion

This semi-dynamic architecture successfully balances **strict validation** with **flexible property handling**. It prevents data corruption by rejecting unknown properties at the API boundary while enabling easy addition of new properties through explicit DTO definitions. The service and repository layers automatically handle any properties defined in DTOs, eliminating the need for code changes across multiple layers when adding new features.

The architecture ensures data integrity while maintaining the flexibility needed for future feature development.
