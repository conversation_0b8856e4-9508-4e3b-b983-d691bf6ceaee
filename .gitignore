# compiled output
/dist
/node_modules
.env*

# DB
/.docker/dbdata
/prisma/.env
!/prisma/migrations/
!/prisma/migrations/migration_lock.toml

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output
src/repl.ts


# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
/load-test

TODOs.txt


