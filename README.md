![Logo <PERSON>](https://media.licdn.com/dms/image/v2/D4D3DAQHb-DIsCgUezQ/image-scale_191_1128/image-scale_191_1128/0/1725561282496/digai_ai_cover?e=2147483647&v=beta&t=O16-_0xoMONaD5zaI4q2zFEDfgC_-NXjCTVo0AE7hWs)

# **Transcendence**

## **About the Project**

This repository contains the **back-end** of the **CollectCash** product. To view and run the **front-end**, you will also need to clone the `transcendence-admin` repository.  
The project uses the **LocalStack** tool, which allows simulating AWS services locally, enabling the entire architecture to run on your machine without the need to access the AWS cloud directly.

---

## **Prerequisites**

Before getting started, make sure the environment is properly configured:

- **Operating System**: It is recommended to use Linux distributions. Windows users may encounter conflicts, even when using WSL.
- **Dependencies**:
  - <PERSON><PERSON> or <PERSON><PERSON> configured
  - Docker installed ([installation instructions for Docker](https://docs.docker.com/get-docker/))
  - Git configured with your GitHub user
  - Node.js and NPM installed

---

## **Installation Steps**

Here are the steps to set up the project on your machine:

1. **Edit the shell configuration file**:  
   Open the terminal and edit the shell configuration file to add your **GitHub Token**. Depending on your shell, use one of the following commands:

   - For **bash**:
     ```bash
     vi ~/.bashrc
     ```
   - For **zsh**:
     ```bash
     vi ~/.zshrc
     ```

2. **Enable edit mode**:  
   Go to the end of the file and press the **`A`** key to enter edit mode.

3. **Add the GitHub Token**:  
   Paste the following code, replacing `your-accesstoken-here` with your GitHub token:

   ```bash
   export GITHUB_TOKEN=your-accesstoken-here
   ```

   **Note**: To generate the token on GitHub, follow these steps:

   - Go to GitHub and click on the profile icon at the top right.
   - Select **Settings**.
   - In the left sidebar, click **Developer settings**.
   - Then click **Personal access tokens**.
   - Click **Generate new token classic**, select the required permissions, and copy the generated token.
   - **Replace** `your-accesstoken-here` in the above code with the generated token.

4. **Abort the edit (if needed)**:  
   If you made an error during editing, you can abort by typing:

   ```bash
   :qa!
   ```

5. **Save changes and exit `vi` (or `vim`)**:  
   To save and exit the editor, type:

   ```bash
   :wq
   ```

6. **Apply the changes**:  
   To load the changes made to the configuration file, run:

   ```bash
   source ~/.bashrc  # Or ~/.zshrc if you're using Zsh
   ```

7. **Clone the repository**:  
   Clone the project's back-end repository and install the dependencies:

   ```bash
   git clone https://github.com/edutalent/transcendence-admin.git
   cd transcendence
   npm install
   ```

8. **Install Docker**:  
   If you don't have Docker installed yet, follow [the official Docker installation instructions](https://docs.docker.com/get-docker/) for your operating system.

9. **Install AWS CLI**:  
   To install the AWS CLI, run the following command:

   ```bash
    # Install AWS CLI on macOS (Using Homebrew)
    brew install awscli
    aws --version

    # Install AWS CLI on Linux (Using apt-get)
    sudo apt-get update
    sudo apt-get install awscli
   ```

10. **Get the `.env` files**:  
    Request the `.env` (for the back-end) and `.env.local` (for the front-end) files from your team member.

11. **Run the local infrastructure**:  
    In the project folder, run the command to start the local infrastructure with LocalStack:

```bash
npm run local-infra:start
```

Wait until the infrastructure is created locally.

12. **Start the server**:  
    After the infrastructure is set up, run the command to start the back-end:

```bash
npm run build
npm run start
```

---

Now the infrastructure will be running locally on your machine. Next, follow the steps for the front-end!

Clone the repository:

```bash
git clone https://github.com/edutalent/transcendence-admin.git
cd transcendence-admin
npm install
npm install -g next
```

Don't forget to request the environment variables (envs) from your team member.

Run the following to start the server:

```bash
npm run start
```

Once the server loads, you can access the application at:

[http://localhost:3390](http://localhost:3390)

---

Now, the infrastructure and the front-end are set up and ready to be used locally on your machine!
