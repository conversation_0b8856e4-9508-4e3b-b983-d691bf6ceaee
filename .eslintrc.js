module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: 'tsconfig.json',
    tsconfigRootDir: __dirname,
    sourceType: 'module',
  },
  plugins: ['@typescript-eslint', 'import'],
  extends: [
    'plugin:@typescript-eslint/recommended',
    'plugin:prettier/recommended',
  ],
  root: true,
  env: {
    node: true,
    jest: true,
  },
  ignorePatterns: ['.eslintrc.js'],
  rules: {
    "prettier/prettier": 'warn',
    "no-console": "error",
    "prefer-const": "error",
    '@typescript-eslint/interface-name-prefix': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    'import/no-restricted-paths': [
      'error',
      {
        basePath: __dirname,
        zones: [
          // Disallow imports from restricted-module in user module
          {
            target: './src/!(orquestrator)/**',
            from: './src/orquestrator',
            message: 'Importing between modules is not allowed',
          },
          {
            target: './src/!(intelligence)/**',
            from: './src/intelligence',
            message: 'Importing between modules is not allowed',
          },
          {
            target: './src/!(business-base)/**',
            from: './src/business-base',
            message: 'Importing between modules is not allowed',
          },
          {
            target: './src/!(data-insights)/**',
            from: './src/data-insights',
            message: 'Importing between modules is not allowed',
          },
          {
            target: './src/!(auth)/**',
            from: './src/auth',
            message: 'Importing between modules is not allowed',
          },
        ],
      },
    ],
    'no-restricted-imports': [
      'error',
      {
        patterns: [
          {
            group: ['../*', 'src/*', './'],
            message: 'only @alias import is allowed. Example: instead of src/module/something or ../module/something better use "@module/something"',
          },
        ],
      },
    ],
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "vars": "all",
        "varsIgnorePattern": "^_",
        "args": "after-used",
        "argsIgnorePattern": "^_"
      }
    ],
  },
  "settings": {
    "import/parsers": {
      "@typescript-eslint/parser": [".ts", ".tsx"]
    },
    "import/resolver": {
      "typescript": {
        "alwaysTryTypes": true,
      }
    },
    "import/internal-regex": "^@"
  }
};
