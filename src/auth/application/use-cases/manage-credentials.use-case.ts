import { UserPort } from '@auth/infrastructure/ports/db/user.port';
import { Inject, Injectable } from '@nestjs/common';
import { PasswordService } from '@auth/application/services/password.service';
import { ChangePasswordByAdminDto } from '@auth/application/dto/in/change-password.dto';
import { TokenService } from '@auth/domain/services/token.service';
import { PayloadTokenEntity } from '@auth/domain/entities/payload-token.entity';
import { TokenPort } from '@auth/infrastructure/ports/db/token.port';
import { getCookieDomainByRequest } from '@common/utils/domain-selector';
import { Request } from 'express';
import { JWTCookieOptionEntity } from '@auth/domain/entities/jwt-cookie.entity';

@Injectable()
export class ManageCredentialsUseCase {
  constructor(
    @Inject('UserPort')
    private readonly userAdapter: UserPort,
    private readonly passwordService: PasswordService,
    private readonly tokenService: TokenService,
    @Inject('TokenPort')
    private readonly tokenAdapter: TokenPort,
  ) {}

  async updatePassword(passwordDto: ChangePasswordByAdminDto): Promise<void> {
    const { hash, salt } = this.passwordService.hashPassword(passwordDto.password);
    const credentials = { password: hash, salt };

    await this.userAdapter.updateCredentials(passwordDto.email, credentials);
  }

  async refreshAuthTokens(
    request: Request,
  ): Promise<{ accessToken: string; setupCookieJWTAccessToken: JWTCookieOptionEntity }> {
    const domain = getCookieDomainByRequest(request);
    const payload: PayloadTokenEntity = request['user'];
    const { accessTokenEntity } = await this.tokenService.genAuthTokens(payload);

    await this.tokenAdapter.create(accessTokenEntity);

    const { setupCookieJWTAccessToken } = this.tokenService.genSetupJWTCookies(domain);

    return {
      accessToken: accessTokenEntity.token,
      setupCookieJWTAccessToken,
    };
  }
}
