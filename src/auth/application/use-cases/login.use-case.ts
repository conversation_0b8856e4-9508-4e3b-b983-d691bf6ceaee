import { Inject, Injectable } from '@nestjs/common';
import { LoginDto } from '@auth/application/dto/in/login.dto';
import { UserPort } from '@auth/infrastructure/ports/db/user.port';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';
import { PasswordService } from '@auth/application/services/password.service';
import { AccountStatus, LoginMethod, TokenStatus, UserStatus } from '@common/enums';
import { AccountPort } from '@auth/infrastructure/ports/db/account.port';
import { PayloadTokenEntity } from '@auth/domain/entities/payload-token.entity';
import { AccountEntity } from '@auth/domain/entities/account.entity';
import { TokenPort } from '@auth/infrastructure/ports/db/token.port';
import { JWTCookieOptionEntity } from '@auth/domain/entities/jwt-cookie.entity';
import { Request } from 'express';
import { getCookieDomainByRequest } from '@common/utils/domain-selector';
import { UserDataDto } from '@auth/application/dto/out/user-data.dto';
import { UserEntity } from '@auth/domain/entities/user.entity';
import { InfraCustomerPort } from '@auth/infrastructure/ports/http/customer.port';
import { TokenService } from '@auth/domain/services/token.service';
import { CustomerApiDto } from '@auth/application/dto/in/user-account.dto';

@Injectable()
export class LoginUSeCase {
  constructor(
    @Inject('UserPort')
    private readonly userAdapter: UserPort,
    @Inject('AccountPort')
    private readonly accountAdapter: AccountPort,
    private readonly passwordService: PasswordService,
    @Inject('TokenPort')
    private readonly tokenAdapter: TokenPort,
    @Inject('InfraCustomerPort')
    private readonly customerAdapter: InfraCustomerPort,
    private readonly tokenService: TokenService,
  ) {}

  private validateUserStatus(userId: string, status: UserStatus): void {
    if (status !== UserStatus.ACTIVE) {
      if (status === UserStatus.SUSPENDED) {
        throw new AuthException(
          'user::suspended-access',
          'Your account has been suspended. Please contact support.',
          AuthExceptionStatus.SUSPENDED_ACCESS,
          `Suspended status for user ${userId}`,
          '5d4794f8-20c3-4c58-9649-10f9a8c41db5',
        );
      } else if (status === UserStatus.PENDING_CONFIRMATION) {
        throw new AuthException(
          'user::pending-confirmation',
          'Your account is pending confirmation. Please check your email.',
          AuthExceptionStatus.PENDING_CONFIRMATION,
          `Pending confirmation status for user ${userId}`,
          'da9afc84-54d1-4daf-a77e-c06fdfbc6ee3',
        );
      } else if (status === UserStatus.DELETED) {
        throw new AuthException(
          'user::deleted',
          'We could not determine the status of your account. Please contact support.',
          AuthExceptionStatus.DELETED_ACCOUNT,
          `Deleted status for user ${userId}`,
          'cfa9c3c9-84c1-4d2c-b355-be66e4b1933d',
        );
      } else {
        throw new AuthException(
          'user::unknown-status',
          'We could not determine the status of your account. Please contact support.',
          AuthExceptionStatus.UNKNOWN_STATUS,
          `Unknown status ${status} for user ${userId}`,
          '3c0a767e-57c7-4c7b-a840-fa088aad32b7',
        );
      }
    }
  }

  private validateAccountStatus(account: AccountEntity, userId: string): void {
    if (!account) {
      throw new AuthException(
        'user::invalid-account',
        'Business account not found. Please contact support.',
        AuthExceptionStatus.ACCOUNT_NOT_FOUND,
        `Account not found for user ${userId}`,
        'bbb8a475-fd7d-40cc-be9d-5c5a31c68366',
      );
    }

    if (account.status !== AccountStatus.ACTIVE) {
      throw new AuthException(
        'user::inactive-account',
        'Inactive account. Please contact support.',
        AuthExceptionStatus.ACCOUNT_INACTIVE,
        `Account ${account.id} is not active for user ${userId}`,
        '5ffaafff-83b9-4da8-8f9c-47bfb6a5064f',
      );
    }
  }

  async getSessionData(
    loginDto: LoginDto,
    loginMethod: LoginMethod = LoginMethod.PASSWORD,
  ): Promise<{ payload: PayloadTokenEntity; user: UserEntity; customerInfo: CustomerApiDto }> {
    const lowerEmail = loginDto.email.toLowerCase();

    const userData = await this.userAdapter.getAll({
      email: lowerEmail,
      status: '*',
    });

    if (userData.length === 0) {
      throw new AuthException(
        'user::invalid-credentials',
        'Invalid credentials. Check that your email and password are correct.',
        AuthExceptionStatus.USER_NOT_FOUND,
        'Login email not found',
        '0fc9cc4c-eb4c-44fc-bffa-a57c719320da',
      );
    }

    const user = userData[0];
    const userId = user.id;
    const credentials = await this.userAdapter.getCredentials(userId);

    if (!credentials) {
      throw new AuthException(
        'user::invalid-credentials',
        'Invalid credentials. Check that your email and password are correct.',
        AuthExceptionStatus.CREDENTIALS_NOT_FOUND,
        `Credentials were undefined for user ${userId}`,
        '06f36889-963a-45f1-b388-db8c0e65cc89',
      );
    }

    if (loginMethod === LoginMethod.PASSWORD) {
      const isPasswordValid = this.passwordService.validatePassword(
        loginDto.password,
        credentials.password,
      );

      if (!isPasswordValid) {
        throw new AuthException(
          'user::invalid-credentials',
          'Invalid credentials. Check that your email and password are correct.',
          AuthExceptionStatus.EMAIL_AND_PASSWORD_DO_NOT_MATCH,
          `Password is invalid for user ${userId}`,
          '578dd50b-9e41-41bc-9933-ba95ea9a565a',
        );
      }
    }

    const status = user.status;
    this.validateUserStatus(userId, status);

    const accountId = user.accountId;
    const userAccount = await this.accountAdapter.get(accountId);
    this.validateAccountStatus(userAccount, userId);

    //Get customer data
    const customer = await this.customerAdapter.getCustomerById(userAccount.customerId);

    const payload = {
      userId,
      accountId: user.accountId,
      customerId: userAccount.customerId,
      email: user.email,
      roleInAccount: user.roleInAccount,
      accountRole: userAccount.role,
      segment: customer.segment,
    };

    return { payload, user, customerInfo: customer };
  }

  async login(
    request: Request,
    loginDto: LoginDto,
    loginMethod: LoginMethod = LoginMethod.PASSWORD,
  ): Promise<{
    userData: UserDataDto;
    accessToken: string;
    refreshToken: string;
    setupCookieJWTAccessToken: JWTCookieOptionEntity;
    setupCookieJWTRefreshToken: JWTCookieOptionEntity;
  }> {
    const domain = getCookieDomainByRequest(request);
    const { payload, user, customerInfo } = await this.getSessionData(loginDto, loginMethod);
    const { accessTokenEntity, refreshTokenEntity } = await this.tokenService.genAuthTokens(
      payload,
    );

    await this.tokenAdapter.create(refreshTokenEntity);
    await this.tokenAdapter.create(accessTokenEntity);

    const { setupCookieJWTAccessToken, setupCookieJWTRefreshToken } =
      this.tokenService.genSetupJWTCookies(domain);

    const userData: UserDataDto = {
      userId: payload.userId,
      email: payload.email,
      firstname: user.firstname,
      lastname: user.lastname,
      accountId: payload.accountId,
      customerId: payload.customerId,
      cnpj: customerInfo.cnpj,
      segment: payload.segment,
      phone: customerInfo.phone,
      whatsappPhone: customerInfo.whatsappPhone,
    };

    return {
      userData,
      accessToken: accessTokenEntity.token,
      refreshToken: refreshTokenEntity.token,
      setupCookieJWTAccessToken,
      setupCookieJWTRefreshToken,
    };
  }

  async logout(userId: string): Promise<void> {
    await this.tokenAdapter.revokeAllTokens(userId, TokenStatus.REVOKED_BY_USER);
  }
}
