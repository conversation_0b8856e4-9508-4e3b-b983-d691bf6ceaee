import { UserPort } from '@auth/infrastructure/ports/db/user.port';
import { Inject, Injectable } from '@nestjs/common';
import { UserDto } from '@auth/application/dto/in/user.dto';
import { UserEntity } from '@auth/domain/entities/user.entity';
import { UserRoleInAccount, UserStatus } from '@common/enums';
import { PasswordService } from '@auth/application/services/password.service';
import { randomUUID as uuidv4 } from 'crypto';

@Injectable()
export class ManageUserUseCase {
  constructor(
    @Inject('UserPort')
    private readonly userAdapter: UserPort,
    private readonly passwordService: PasswordService,
  ) {}

  async create(user: UserDto, accountId: string): Promise<UserEntity> {
    const userId = uuidv4();
    const roleInAccount = UserRoleInAccount.ADMIN;
    const status = UserStatus.ACTIVE;
    const { hash, salt } = this.passwordService.hashPassword(user.password);
    const credentials = { password: hash, salt };

    const userEntity = new UserEntity(
      userId,
      user.email.toLowerCase(),
      user.firstname,
      user.lastname,
      roleInAccount,
      accountId,
      status,
    );

    return this.userAdapter.create(userEntity, credentials);
  }
}
