import { Inject, Injectable } from '@nestjs/common';
import { AccountRole, AccountStatus } from '@common/enums';
import { AccountPort } from '@auth/infrastructure/ports/db/account.port';
import { AccountDto } from '@auth/application/dto/in/account.dto';
import { AccountEntity } from '@auth/domain/entities/account.entity';
import { CustomerApiDto, CustomerDto } from '@auth/application/dto/in/user-account.dto';
import { InfraCustomerPort } from '@auth/infrastructure/ports/http/customer.port';

@Injectable()
export class ManageAccountUseCase {
  constructor(
    @Inject('AccountPort')
    private readonly accountAdapter: AccountPort,
    @Inject('InfraCustomerPort')
    private readonly customerAdapter: InfraCustomerPort,
  ) {}

  async create(
    accountId: string,
    accountDto: AccountDto,
    customerData: CustomerDto,
  ): Promise<{ account: AccountEntity; customer: CustomerApiDto }> {
    const customer = await this.customerAdapter.createCustomer(customerData);
    const role = AccountRole.BASIC;
    const status = AccountStatus.ACTIVE;

    const accountEntity = new AccountEntity(
      accountId,
      accountDto.nickname,
      role,
      status,
      customer.id,
    );
    const account = await this.accountAdapter.create(accountEntity);

    return { account, customer };
  }

  async getAccountByNickname(nickname: string): Promise<AccountEntity | null> {
    return this.accountAdapter.findByNickname(nickname);
  }
}
