import { IsNotEmpty, <PERSON><PERSON>ptional, IsString, IsUUID } from 'class-validator';

export class UserDataDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly userId: string;

  @IsString()
  @IsNotEmpty()
  readonly email: string;

  @IsString()
  @IsNotEmpty()
  readonly firstname: string;

  @IsString()
  @IsNotEmpty()
  readonly lastname: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly accountId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsOptional()
  @IsString()
  readonly cnpj: string;

  @IsNotEmpty()
  readonly segment: string;

  @IsOptional()
  @IsString()
  readonly phone: string;

  @IsOptional()
  @IsString()
  readonly whatsappPhone: string;
}
