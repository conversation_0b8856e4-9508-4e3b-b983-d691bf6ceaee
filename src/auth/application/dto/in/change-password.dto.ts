import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Matches,
  Validate,
  IsE<PERSON>,
} from 'class-validator';
import { MatchPasswords } from '@auth/application/dto/in/user.dto';
import { params as p } from '@auth/config';

export class ChangePasswordDto {
  @IsString()
  @IsNotEmpty()
  @MinLength(p.MIN_PASSWORD_LENGTH)
  @MaxLength(p.MAX_PASSWORD_LENGTH)
  @Matches(p.PASSWORD_REGEX, {
    message: 'The password must contain at least one letter, one number and one special character',
  })
  readonly password: string;

  @IsNotEmpty()
  @IsString()
  @Validate(MatchPasswords, ['password'])
  readonly passwordConfirmation: string;
}

export class ChangePasswordByAdminDto extends ChangePasswordDto {
  @IsEmail()
  @IsNotEmpty()
  readonly email: string;
}
