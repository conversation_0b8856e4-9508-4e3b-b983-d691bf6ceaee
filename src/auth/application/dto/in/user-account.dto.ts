import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  IsString,
  ValidateNested,
} from 'class-validator';
import { AccountDto } from '@auth/application/dto/in/account.dto';
import { UserDto } from '@auth/application/dto/in/user.dto';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CustomerDto {
  @IsNotEmpty({ message: 'name is required' })
  @IsString({ message: 'name must be a string' })
  readonly name: string;

  @IsNotEmpty({ message: 'cnpj is required' })
  @IsString({ message: 'cnpj must be a string' })
  readonly cnpj: string;

  @IsOptional()
  @IsEmail({}, { message: 'email must be a valid email' })
  readonly email?: string;

  @IsNotEmpty({ message: 'phone is required' })
  @IsPhoneNumber('BR', { message: 'phone must be a valid phone number' })
  readonly phone: string;

  @IsNotEmpty({ message: 'whatsappPhone is required' })
  @IsPhoneNumber('BR', { message: 'whatsappPhone must be a valid phone number' })
  readonly whatsappPhone: string;

  @IsNotEmpty({ message: 'segment is required' })
  @IsString({ message: 'segment must be a string' })
  readonly segment: string;
}

export class CustomerApiDto extends CustomerDto {
  @ApiProperty({
    description: 'Customer identifier',
    example: 'abc123',
  })
  readonly id: string;

  @ApiProperty({
    description: 'Customer business segment',
    example: 'Retail',
  })
  readonly segment: string;
}

export class UserAccountDto {
  @ApiProperty({
    type: () => CustomerDto,
    example: {
      name: 'John Doe',
      cnpj: '**************',
      email: '<EMAIL>',
      phone: '+*************',
      whatsappPhone: '+*************',
      segment: 'Retail',
    },
  })
  @ValidateNested()
  @Type(() => CustomerDto)
  readonly customerData: CustomerDto;

  @ApiProperty({ type: () => AccountDto })
  @ValidateNested()
  @Type(() => AccountDto)
  readonly accountData: AccountDto;

  @ApiProperty({ type: () => UserDto })
  @ValidateNested()
  @Type(() => UserDto)
  readonly userData: UserDto;
}
