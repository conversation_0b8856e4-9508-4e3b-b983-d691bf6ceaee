import {
  IsNotEmpty,
  IsString,
  Matches,
  MaxLength,
  Min<PERSON>ength,
  Validate,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';
import { params as p } from '@auth/config';
import { ApiProperty } from '@nestjs/swagger';

@ValidatorConstraint({ name: 'MatchPasswords', async: false })
export class MatchPasswords implements ValidatorConstraintInterface {
  validate(passwordConfirmation: string, args: ValidationArguments) {
    const password = (args.object as any)[args.constraints[0]];
    return passwordConfirmation === password;
  }

  defaultMessage() {
    return 'The password confirmation does not match the password';
  }
}

export class UserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'The email of the user',
  })
  @IsString()
  @IsNotEmpty()
  readonly email: string;

  @ApiProperty({
    example: '<PERSON>',
    description: 'The first name of the user',
  })
  @IsString()
  @IsNotEmpty()
  readonly firstname: string;

  @ApiProperty({
    example: 'Doe',
    description: 'The last name of the user',
  })
  @IsString()
  @IsNotEmpty()
  readonly lastname: string;

  @ApiProperty({
    example: '1234567890@A',
    description: 'The phone number of the user',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(p.MIN_PASSWORD_LENGTH)
  @MaxLength(p.MAX_PASSWORD_LENGTH)
  @Matches(p.PASSWORD_REGEX, {
    message: 'The password must contain at least one letter, one number and one special character',
  })
  readonly password: string;

  @ApiProperty({
    example: '1234567890@A',
    description: 'The password confirmation of the user',
  })
  @IsNotEmpty()
  @IsString()
  @Validate(MatchPasswords, ['password'])
  readonly passwordConfirmation: string;
}
