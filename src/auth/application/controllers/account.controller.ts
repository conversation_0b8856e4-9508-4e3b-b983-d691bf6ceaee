import { Controller, Get, Param, Version } from '@nestjs/common';
import { ManageAccountUseCase } from '@auth/application/use-cases/manage-account.use-case';
import { BaseResponse } from '@common/http/responses/types/base-response.type';
import { AccountEntity } from '@auth/domain/entities/account.entity';
import { Public } from '@common/auth/decorators/public.decorator';

@Controller('auth/accounts')
export class AccountController {
  constructor(private readonly manageAccountUseCase: ManageAccountUseCase) {}

  @Public()
  @Get('/nickname/:nickname')
  @Version('1')
  async getAccountDataByNickname(
    @Param('nickname') nickname: string,
  ): Promise<BaseResponse<AccountEntity>> {
    const account = await this.manageAccountUseCase.getAccountByNickname(nickname);
    if (!account) {
      throw new Error('Account not found');
    }

    return {
      statusCode: 200,
      data: account,
    };
  }
}
