import { Body, Controller, Post, Version } from '@nestjs/common';
import { ManageAccountUseCase } from '@auth/application/use-cases/manage-account.use-case';
import { ManageUserUseCase } from '@auth/application/use-cases/manage-user.use-case';
import { CustomerApiDto, UserAccountDto } from '@auth/application/dto/in/user-account.dto';
import { randomUUID as uuidv4 } from 'crypto';
import { BaseResponse } from '@common/http/responses/types/base-response.type';
import { AccountEntity } from '@auth/domain/entities/account.entity';
import { UserEntity } from '@auth/domain/entities/user.entity';
import { Public } from '@common/auth/decorators/public.decorator';
import { ApiBody } from '@nestjs/swagger';

@Controller('auth/users')
export class UserController {
  constructor(
    private readonly manageAccountUSeCase: ManageAccountUseCase,
    private readonly manageUserUSeCase: ManageUserUseCase,
  ) {}

  @Public()
  @ApiBody({ type: UserAccountDto })
  @Post('/signup')
  @Version('1')
  async signup(
    @Body() userAccountDto: UserAccountDto,
  ): Promise<BaseResponse<{ account: AccountEntity; customer: CustomerApiDto; user: UserEntity }>> {
    const accountId = uuidv4();
    const userData = userAccountDto.userData;
    const customerData = { ...userAccountDto.customerData, email: userData.email.toLowerCase() };
    const accountData = userAccountDto.accountData;

    const user = await this.manageUserUSeCase.create(userData, accountId);
    const { account, customer } = await this.manageAccountUSeCase.create(
      accountId,
      accountData,
      customerData,
    );

    return {
      statusCode: 201,
      data: { account, customer, user },
    };
  }
}
