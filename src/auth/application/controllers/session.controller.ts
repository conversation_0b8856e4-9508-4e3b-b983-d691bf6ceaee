import { Body, Controller, Get, Post, Req, Res, UseGuards, Version } from '@nestjs/common';
import { Request, Response } from 'express';
import { LoginDto } from '@auth/application/dto/in/login.dto';
import { LoginUSeCase } from '@auth/application/use-cases/login.use-case';
import { BaseResponse } from '@common/http/responses/types/base-response.type';
import { UserDataDto } from '@auth/application/dto/out/user-data.dto';
import { Public } from '@common/auth/decorators/public.decorator';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { AccountRole, LoginMethod, UserRoleInAccount } from '@common/enums';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthnRefreshGuard } from '@common/auth/authn-refresh.guard';
import { ManageCredentialsUseCase } from '@auth/application/use-cases/manage-credentials.use-case';
import { ApiBearerAuth, ApiBody, ApiResponse } from '@nestjs/swagger';

@Controller('auth/session')
export class SessionController {
  constructor(
    private readonly loginUseCase: LoginUSeCase,
    private readonly manageCredentialsUseCase: ManageCredentialsUseCase,
  ) {}

  @Public()
  @ApiBody({ type: LoginDto })
  @Post('/login')
  @Version('1')
  async login(
    @Req() request: Request,
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<BaseResponse<UserDataDto>> {
    const {
      userData,
      accessToken,
      refreshToken,
      setupCookieJWTAccessToken,
      setupCookieJWTRefreshToken,
    } = await this.loginUseCase.login(request, loginDto);

    response.cookie('digai.accessToken', accessToken, setupCookieJWTAccessToken);
    response.cookie('digai.refreshToken', refreshToken, setupCookieJWTRefreshToken);
    return {
      statusCode: 201,
      data: userData,
    };
  }

  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @AccountRoles(AccountRole.__SYSTEM__)
  @Post('/login/email-only')
  @Version('1')
  async loginEmailOnly(
    @Req() request: Request,
    @Body('email') email: string,
    @Res({ passthrough: true }) response: Response,
  ): Promise<BaseResponse<UserDataDto>> {
    const {
      userData,
      accessToken,
      refreshToken,
      setupCookieJWTAccessToken,
      setupCookieJWTRefreshToken,
    } = await this.loginUseCase.login(
      request,
      { email, password: 'mock' },
      LoginMethod.EMAIL_SERVER_TOKEN,
    );

    response.cookie('digai.accessToken', accessToken, setupCookieJWTAccessToken);
    response.cookie('digai.refreshToken', refreshToken, setupCookieJWTRefreshToken);
    return {
      statusCode: 201,
      data: userData,
    };
  }

  @AccountRoles(AccountRole.__NONE__)
  @UserRolesInAccount(UserRoleInAccount.__NONE__)
  @Post('/logout')
  @Version('1')
  async logout(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ): Promise<BaseResponse<string>> {
    const { userId } = request['user'];
    await this.loginUseCase.logout(userId);

    response.clearCookie('digai.refreshToken');
    response.clearCookie('digai.accessToken');
    return {
      statusCode: 201,
      data: 'User successfully logged out',
    };
  }

  @ExcludeGuards('AuthnGuard')
  @UseGuards(AuthnRefreshGuard)
  @AccountRoles(AccountRole.__NONE__)
  @UserRolesInAccount(UserRoleInAccount.__NONE__)
  @Post('/refresh-access-token')
  @Version('1')
  async refreshAccessToken(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ): Promise<BaseResponse<string>> {
    const { accessToken, setupCookieJWTAccessToken } =
      await this.manageCredentialsUseCase.refreshAuthTokens(request);

    response.cookie('digai.accessToken', accessToken, setupCookieJWTAccessToken);
    return {
      statusCode: 201,
      data: 'Access token successfully refreshed',
    };
  }

  @ExcludeGuards('AuthnGuard')
  @UseGuards(AuthnRefreshGuard)
  @AccountRoles(AccountRole.__NONE__)
  @UserRolesInAccount(UserRoleInAccount.__NONE__)
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'Token is valid',
    type: BaseResponse<{ valid: boolean }>,
  })
  @Get('/token-is-valid')
  @Version('1')
  async tokenIsValid(): Promise<BaseResponse<{ valid: boolean }>> {
    return {
      statusCode: 200,
      data: { valid: true },
    };
  }
}
