import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';

@Injectable()
export class PasswordService {
  hashPassword(password: string): { hash: string; salt: string } {
    const salt = bcrypt.genSaltSync(10);
    const hash = bcrypt.hashSync(password, salt);

    return { hash: hash, salt: salt };
  }

  validatePassword(passwordToValidate: string, password: string): boolean {
    return bcrypt.compareSync(passwordToValidate, password);
  }
}
