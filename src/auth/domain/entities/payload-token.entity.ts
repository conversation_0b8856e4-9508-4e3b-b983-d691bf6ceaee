import { AccountRole, UserRoleInAccount } from '@common/enums';
import { IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class PayloadTokenEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly userId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly accountId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly email: string;

  @IsEnum(UserRoleInAccount)
  @IsNotEmpty()
  readonly roleInAccount?: UserRoleInAccount;

  @IsEnum(AccountRole)
  @IsNotEmpty()
  readonly accountRole: AccountRole;

  @IsString()
  @IsNotEmpty()
  readonly segment: string;

  constructor(
    userId: string,
    accountId: string,
    customerId: string,
    email: string,
    roleInAccount: UserRoleInAccount,
    accountRole: AccountRole,
    segment: string,
  ) {
    this.userId = userId;
    this.accountId = accountId;
    this.customerId = customerId;
    this.email = email;
    this.roleInAccount = roleInAccount;
    this.accountRole = accountRole;
    this.segment = segment;
  }
}
