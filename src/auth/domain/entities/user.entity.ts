import { UserRoleInAccount, UserStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class UserEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly email: string;

  @IsString()
  @IsNotEmpty()
  readonly firstname: string;

  @IsString()
  @IsNotEmpty()
  readonly lastname: string;

  @IsEnum(UserRoleInAccount)
  @IsNotEmpty()
  readonly roleInAccount?: UserRoleInAccount;

  @IsUUID('4')
  @IsNotEmpty()
  readonly accountId: string;

  @IsEnum(UserStatus)
  @IsNotEmpty()
  readonly status: UserStatus;

  @IsDate()
  @IsOptional()
  readonly createdAt?: Date;

  @IsDate()
  @IsOptional()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    email: string,
    firstname: string,
    lastname: string,
    roleInAccount: UserRoleInAccount,
    accountId: string,
    status: UserStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.email = email;
    this.firstname = firstname;
    this.lastname = lastname;
    this.roleInAccount = roleInAccount;
    this.accountId = accountId;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
