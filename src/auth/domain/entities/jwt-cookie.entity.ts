import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class JWTCookieOptionEntity {
  @IsBoolean()
  @IsNotEmpty()
  readonly httpOnly: boolean;

  @IsNumber()
  @IsInt()
  @Min(0)
  @IsNotEmpty()
  readonly maxAge: number;

  @IsBoolean()
  @IsNotEmpty()
  readonly secure: boolean;

  @IsString()
  @IsNotEmpty()
  readonly path: string;

  @IsString()
  @IsOptional()
  domain?: string;

  @IsEnum(['strict', 'lax', 'none'])
  @IsOptional()
  sameSite?: 'strict' | 'lax' | 'none';
}
