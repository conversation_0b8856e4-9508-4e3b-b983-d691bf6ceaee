import { AccountRole, AccountStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class AccountEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly name: string;

  @IsEnum(AccountRole)
  @IsNotEmpty()
  readonly role: AccountRole;

  @IsEnum(AccountStatus)
  @IsNotEmpty()
  readonly status: AccountStatus;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsDate()
  @IsOptional()
  readonly createdAt?: Date;

  @IsDate()
  @IsOptional()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    name: string,
    role: AccountRole,
    status: AccountStatus,
    customerId: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.name = name;
    this.role = role;
    this.status = status;
    this.customerId = customerId;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
