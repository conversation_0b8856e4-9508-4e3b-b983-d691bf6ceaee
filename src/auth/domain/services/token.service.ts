import { parseDuration } from '@common/utils/parse-duration';
import { Injectable } from '@nestjs/common';
import { JWTCookieOptionEntity } from '@auth/domain/entities/jwt-cookie.entity';
import { params as p } from '@auth/config';
import { TokenReference, TokenStatus } from '@common/enums';
import { PayloadTokenEntity } from '@auth/domain/entities/payload-token.entity';
import { TokenEntity } from '@auth/domain/entities/token.entity';
import { randomUUID as uuidv4 } from 'crypto';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class TokenService {
  constructor(private readonly jwtService: JwtService) {}

  genSetupJWTCookies(domain: string): {
    setupCookieJWTAccessToken: JWTCookieOptionEntity;
    setupCookieJWTRefreshToken: JWTCookieOptionEntity;
  } {
    const baseConfig = {
      httpOnly: true,
      secure: process.env.NODE_ENV !== 'development' && process.env.NODE_ENV !== 'test-local',
      path: '/',
    };

    const configAccessToken: JWTCookieOptionEntity = {
      ...baseConfig,
      maxAge: parseDuration(p.ACCESS_TOKEN_EXPIRES_IN),
    };

    const configRefreshToken: JWTCookieOptionEntity = {
      ...baseConfig,
      maxAge: parseDuration(p.REFRESH_TOKEN_EXPIRES_IN),
    };

    if (process.env.NODE_ENV === 'production' || process.env.NODE_ENV === 'staging') {
      configAccessToken.domain = domain;
      configRefreshToken.domain = domain;
      configAccessToken.sameSite = 'none';
      configRefreshToken.sameSite = 'none';
    }

    return {
      setupCookieJWTAccessToken: configAccessToken,
      setupCookieJWTRefreshToken: configRefreshToken,
    };
  }

  async genAuthTokens(
    payloadRaw: PayloadTokenEntity,
  ): Promise<{ accessTokenEntity: TokenEntity; refreshTokenEntity: TokenEntity }> {
    const payload = {
      userId: payloadRaw.userId,
      accountId: payloadRaw.accountId,
      customerId: payloadRaw.customerId,
      email: payloadRaw.email,
      roleInAccount: payloadRaw.roleInAccount,
      accountRole: payloadRaw.accountRole,
      segment: payloadRaw.segment,
    };

    const accessToken = await this.jwtService.signAsync(
      { ...payload, jti: uuidv4() },
      {
        expiresIn: p.ACCESS_TOKEN_EXPIRES_IN,
      },
    );

    const refreshToken = await this.jwtService.signAsync(
      { ...payload, jti: uuidv4() },
      {
        expiresIn: p.REFRESH_TOKEN_EXPIRES_IN,
      },
    );

    const accessTokenEntity = new TokenEntity(
      uuidv4(),
      payload.userId,
      accessToken,
      TokenReference.PRIVATE_API__ACCESS_TOKEN,
      TokenStatus.ACTIVE,
      new Date(Date.now() + parseDuration(p.ACCESS_TOKEN_EXPIRES_IN)),
    );

    const refreshTokenEntity = new TokenEntity(
      uuidv4(),
      payload.userId,
      refreshToken,
      TokenReference.PRIVATE_API__REFRESH_TOKEN,
      TokenStatus.ACTIVE,
      new Date(Date.now() + parseDuration(p.REFRESH_TOKEN_EXPIRES_IN)),
    );

    return { accessTokenEntity, refreshTokenEntity };
  }
}
