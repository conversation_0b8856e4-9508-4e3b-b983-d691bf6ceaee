import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { UserController } from '@auth/application/controllers/user.controller';
import { AccountController } from '@auth/application/controllers/account.controller';
import { SessionController } from '@auth/application/controllers/session.controller';
import { AccountAdapter } from '@auth/infrastructure/adpaters/db/account.adapter';
import { InfraCustomerAdapter } from '@auth/infrastructure/adpaters/http/customer.adapter';
import { UserAdapter } from '@auth/infrastructure/adpaters/db/user.adapter';
import { ManageAccountUseCase } from '@auth/application/use-cases/manage-account.use-case';
import { ManageUserUseCase } from '@auth/application/use-cases/manage-user.use-case';
import { PasswordService } from '@auth/application/services/password.service';
import { JwtModule } from '@nestjs/jwt';
import { TokenAdapter } from '@auth/infrastructure/adpaters/db/token.adapter';
import { LoginUSeCase } from '@auth/application/use-cases/login.use-case';
import { ManageCredentialsUseCase } from '@auth/application/use-cases/manage-credentials.use-case';
import { TokenService } from '@auth/domain/services/token.service';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 50000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [httpModule, JwtModule.register({ secret: process.env.JWT_SECRET })],
  controllers: [UserController, SessionController, AccountController],
  providers: [
    {
      provide: 'UserPort',
      useClass: UserAdapter,
    },
    {
      provide: 'InfraCustomerPort',
      useClass: InfraCustomerAdapter,
    },
    {
      provide: 'AccountPort',
      useClass: AccountAdapter,
    },
    {
      provide: 'TokenPort',
      useClass: TokenAdapter,
    },
    {
      provide: 'AccountPort',
      useClass: AccountAdapter,
    },
    LoginUSeCase,
    ManageAccountUseCase,
    ManageUserUseCase,
    PasswordService,
    ManageCredentialsUseCase,
    TokenService,
  ],
})
export class AuthModule {}
