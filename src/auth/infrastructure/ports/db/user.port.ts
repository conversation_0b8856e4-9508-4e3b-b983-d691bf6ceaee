import { UserCredentials } from '@auth/domain/entities/user-credentials';
import { UserEntity } from '@auth/domain/entities/user.entity';
import { DbCommonPort } from '@common/db/ports/common.port';

export interface UserPort extends DbCommonPort<UserEntity> {
  create(entity: UserEntity, credentials?: { password: string; salt: string }): Promise<UserEntity>;
  getCredentials(userId: string): Promise<UserCredentials>;
  updateCredentials(email: string, credentials: { password: string; salt: string }): Promise<void>;
}
