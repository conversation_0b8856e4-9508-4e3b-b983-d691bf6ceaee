import { TokenEntity } from '@common/auth/entities/token.entity';
import { DbCommonPort } from '@common/db/ports/common.port';
import { TokenStatus } from '@common/enums';
import { Prisma } from '@prisma/client';

export interface TokenPort extends DbCommonPort<TokenEntity> {
  revokeAllTokens(userId: string, revokeStatus: TokenStatus): Promise<void>;
  getFirst(where: Prisma.TokenWhereInput): Promise<TokenEntity>;
}
