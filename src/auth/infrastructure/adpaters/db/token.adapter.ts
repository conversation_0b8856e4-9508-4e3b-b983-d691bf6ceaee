import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { Injectable } from '@nestjs/common';
import { TokenEntity } from '@common/auth/entities/token.entity';
import { TokenPort } from '@auth/infrastructure/ports/db/token.port';
import { TokenStatus } from '@common/enums';
import { Prisma } from '@prisma/client';

@Injectable()
export class TokenAdapter extends PrismaCommonAdapter<TokenEntity> implements TokenPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'token');
  }

  async revokeAllTokens(userId: string, revokeStatus: TokenStatus): Promise<void> {
    await this.prismaClient.token.updateMany({
      where: { userId },
      data: { status: revokeStatus },
    });
  }

  async getFirst(where: Prisma.TokenWhereInput): Promise<TokenEntity> {
    return this.prismaClient.token.findFirst({ where }).then(token => {
      return token ? { ...token, status: token.status as TokenStatus } : null;
    });
  }
}
