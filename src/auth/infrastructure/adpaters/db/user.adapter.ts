import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { Injectable } from '@nestjs/common';
import { UserEntity } from '@auth/domain/entities/user.entity';
import { UserPort } from '@auth/infrastructure/ports/db/user.port';
import { UserRoleInAccount, UserStatus } from '@common/enums';
import { UserCredentials } from '@auth/domain/entities/user-credentials';

@Injectable()
export class UserAdapter extends PrismaCommonAdapter<UserEntity> implements UserPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'user');
  }

  async create(
    entity: UserEntity,
    credentials?: { password: string; salt: string },
  ): Promise<UserEntity> {
    const data = entity;
    const user = {
      id: data.id,
      email: data.email,
      firstname: data.firstname,
      lastname: data.lastname,
      roleInAccount: data.roleInAccount,
      accountId: data.accountId,
      status: data.status,
      password: credentials.password,
      salt: credentials.salt,
      createdAt: data.createdAt || new Date(),
      updatedAt: data.updatedAt || new Date(),
    };
    const savedUser = await this.prismaClient.user.create({ data: user });

    return new UserEntity(
      savedUser.id,
      savedUser.email,
      savedUser.firstname,
      savedUser.lastname,
      savedUser.roleInAccount as UserRoleInAccount,
      savedUser.accountId,
      savedUser.status as UserStatus,
      savedUser.createdAt,
      savedUser.updatedAt,
    );
  }

  async getCredentials(userId: string): Promise<UserCredentials> {
    const credentials = await this.prismaClient.user.findFirst({
      where: {
        id: userId,
      },
      select: {
        password: true,
        salt: true,
      },
    });

    return {
      userId: userId,
      ...credentials,
    };
  }

  async updateCredentials(
    email: string,
    credentials: { password: string; salt: string },
  ): Promise<void> {
    await this.prismaClient.user.update({
      where: {
        email: email,
      },
      data: {
        password: credentials.password,
        salt: credentials.salt,
        updatedAt: new Date(),
      },
    });
  }
}
