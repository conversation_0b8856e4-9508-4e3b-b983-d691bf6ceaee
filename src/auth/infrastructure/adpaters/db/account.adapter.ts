import { Injectable } from '@nestjs/common';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PrismaService } from '@common/prisma/prisma.service';
import { AccountPort } from '@auth/infrastructure/ports/db/account.port';
import { AccountEntity } from '@auth/domain/entities/account.entity';
import { AccountRole, AccountStatus } from '@common/enums';

@Injectable()
export class AccountAdapter extends PrismaCommonAdapter<AccountEntity> implements AccountPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'account');
  }

  async findByNickname(nickname: string): Promise<AccountEntity | null> {
    const accountData = await this.prismaClient.account.findFirst({
      where: { name: nickname },
    });

    if (!accountData) {
      return null;
    }

    return new AccountEntity(
      accountData.id,
      accountData.name,
      accountData.role as AccountRole,
      accountData.status as AccountStatus,
      accountData.customerId,
      accountData.createdAt,
      accountData.updatedAt,
    );
  }
}
