import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { CustomerApiDto, CustomerDto } from '@auth/application/dto/in/user-account.dto';
import { InfraCustomerPort } from '@auth/infrastructure/ports/http/customer.port';

@Injectable()
export class InfraCustomerAdapter implements InfraCustomerPort {
  private readonly businessBaseServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.businessBaseServiceUrl = String(process.env.BUSINESS_BASE_SERVICE_URL);
  }

  async createCustomer(customerDto: CustomerDto): Promise<CustomerApiDto> {
    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/customers`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.post(url, customerDto, { headers }));
      const customer = data?.data;

      return customer as CustomerApiDto;
    } catch (error) {
      handleHttpError(error, 'Infra-Customer-adapter::createCustomer');
    }
  }

  async getCustomerById(customerId: string): Promise<CustomerApiDto> {
    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/customers/${customerId}`;
      logger.info(`Getting data from ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));
      const customer = data?.data;

      return customer as CustomerApiDto;
    } catch (error) {
      handleHttpError(error, 'Infra-Customer-adapter::getCustomer');
    }
  }
}
