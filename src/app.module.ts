import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { OrchestratorModule } from '@orchestrator/orchestrator.module';
import { IntelligenceModule } from '@intelligence/intelligence.module';
import { PrismaExceptionFilter } from '@common/exception/filters/prisma-exceptions.filter';
import { DynamoExceptionFilter } from '@common/exception/filters/dynamo-exceptions.filter';
import { BusinessExceptionFilter } from '@common/exception/filters/business-exceptions.filter';
import { OpenAIExceptionFilter } from '@common/exception/filters/openai-exceptions.filter';
import { BusinessBaseModule } from '@business-base/business-base.module';
import { SQSService } from '@common/sqs/sqs.service';
import { MessageHubModule } from '@message-hub/message-hub.module';
import { DataInsightsModule } from '@data-insights/data-insights.module';
import { AuthModule } from '@auth/auth.module';
import { AuthExceptionFilter } from '@common/exception/filters/auth-exceptions.filter';
import { APP_GUARD } from '@nestjs/core';
import { BlockAccessGuard } from '@common/auth/block-access.guard';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { JwtModule } from '@nestjs/jwt';
import { TokenAdapter } from '@common/auth/db/adapters/token.adapter';
import { PrismaModule } from '@common/prisma/prisma.module';
import { WorkflowExceptionFilter } from '@common/exception/filters/workflow-exceptions.filter';
import { NewRelicMiddleware } from '@common/middlewares/newrelic.middleware';
import { CorrelationIdMiddleware } from '@common/middlewares/correlation-id.middleware';

@Module({
  imports: [
    PrismaModule, // Global Prisma module - must be first
    JwtModule.register({ secret: process.env.JWT_SECRET }),
    OrchestratorModule,
    IntelligenceModule,
    BusinessBaseModule,
    MessageHubModule,
    AuthModule,
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
      serveRoot: '/audios',
    }),
    DataInsightsModule,
  ],
  controllers: [],
  providers: [
    {
      provide: 'TokenPort',
      useClass: TokenAdapter,
    },
    PrismaExceptionFilter,
    DynamoExceptionFilter,
    BusinessExceptionFilter,
    OpenAIExceptionFilter,
    AuthExceptionFilter,
    WorkflowExceptionFilter,
    SQSService,
    {
      provide: APP_GUARD,
      useClass: BlockAccessGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthnGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthzAccountGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AuthzUserInAccountGuard,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply correlation ID middleware first for all routes
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');

    if (process.env.NODE_ENV === 'production') {
      consumer.apply(NewRelicMiddleware).forRoutes('*');
    }
  }
}
