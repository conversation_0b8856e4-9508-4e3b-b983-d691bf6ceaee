export function parseDuration(duration: string): number {
  const match = RegExp(/^(\d+)(ms|s|m|h|d|w)$/).exec(duration); // Captura el número y el sufijo
  if (!match) throw new Error(`Formato inválido: ${duration}`);

  const value = parseInt(match[1], 10);
  const unit = match[2];

  switch (unit) {
    case 'ms':
      return value; // Milisegundos
    case 's':
      return value * 1000; // Segundos
    case 'm':
      return value * 60 * 1000; // Minutos
    case 'h':
      return value * 60 * 60 * 1000; // Horas
    case 'd':
      return value * 24 * 60 * 60 * 1000; // Días
    case 'w':
      return value * 7 * 24 * 60 * 60 * 1000; // Semanas
    default:
      throw new Error(`Unknow time unit: ${unit}`);
  }
}
