import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

export const validateKeys = (taskDescription: string, params: Record<string, any>): void => {
  const regex = /\{\{(\w+)\}\}/g;
  const missingKeys = [];
  let match: any[];

  while ((match = regex.exec(taskDescription)) !== null) {
    const key = match[1];

    if (!(key in params) || params[key] === undefined || params[key] === null) {
      missingKeys.push(key);
    }
  }

  if (missingKeys.length > 0) {
    logger.error(`Invalid values for keys to compile task description: ${missingKeys.join(', ')}`);

    throw new BusinessException(
      'Compile task Description Error',
      `Invalid values for keys: ${missingKeys.join(', ')}`,
      BusinessExceptionStatus.INVALID_INPUT,
      'CompileTaskDescriptionError',
    );
  } else {
    logger.debug('All keys are covered with values for task description.');
  }
};
