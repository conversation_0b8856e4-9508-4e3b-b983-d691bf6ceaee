export function extractNestedKeys(obj: any, prefix: string = ''): string[] {
  const keys: string[] = [];
  if (!obj || typeof obj !== 'object') return keys;

  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    keys.push(fullKey);
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...extractNestedKeys(value, fullKey));
    }
  }
  return keys;
}
