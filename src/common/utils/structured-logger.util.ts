import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';

// Simple helper function for background jobs
export const logWithContext = (
  level: 'info' | 'debug' | 'warn' | 'error',
  message: string,
  additionalData: any = {},
) => {
  const traceId = CorrelationContextService.getTraceId();
  const context = CorrelationContextService.getContext();

  const logData = {
    traceId,
    layer: context?.layer,
    operation: context?.operation,
    timestamp: new Date().toISOString(),
    ...additionalData,
  };

  logger[level](message, logData);
};
