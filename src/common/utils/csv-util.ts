export function getNestedProperty(obj: any, path: string): any {
  return path.split('.').reduce((current, key) => current?.[key] ?? '', obj);
}

export function sanitizeCsvValue(value: any): string {
  if (value === null || value === undefined) return '';
  const str = String(value);
  if (str.startsWith('=') || str.startsWith('+') || str.startsWith('-') || str.startsWith('@')) {
    return `'${str}`;
  }
  return str.replace(/"/g, '""'); // Escape double quotes
}
