export type SelectFields<T> = {
  [K in keyof T]?: boolean;
};

export interface DbCommonPort<T> {
  getAll(where?: any, select?: SelectFields<T>): Promise<T[]>;
  create(entity: T): Promise<T>;
  createMany(entities: T[]): Promise<T[]>;
  get(id: string): Promise<T>;
  update(entity: Partial<T>): Promise<T>;
  delete(id: string): Promise<T>;
  deleteMany(where: any): Promise<void>;
  count(where?: any): Promise<number>;
}
