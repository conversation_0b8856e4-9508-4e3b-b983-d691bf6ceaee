import { FindManyPaginated, makeFindManyPaginated } from '@common/pagination/find-many.proxy';
import { ProxyFunctions } from '@common/pagination/types';

type ProxyPrismaModel<F extends ProxyFunctions> = F & FindManyPaginated<F>;

export function ProxyPrismaModel<F extends ProxyFunctions>(model: F): ProxyPrismaModel<F> {
  Reflect.set(model, 'findManyPaginated', makeFindManyPaginated(model));
  return model as ProxyPrismaModel<F>;
}
