import { PaginationData } from '@common/pagination/types';

export type Paginated<T> = {
  data: T[];
  total: number;
  limit: number;
  page: number;
  totalPages: number;
};

export type PaginatedDto<T> = {
  items: T[];
  total: number;
  limit: number;
  page: number;
  totalPages: number;
};

export function paginate<T>(items: T[], page: number = 1, limit: number = 10): Paginated<T> {
  const offset = (page - 1) * limit;
  const paginatedData = items.slice(offset, offset + limit);
  const total = items.length;
  const totalPages = Math.ceil(total / limit);

  return {
    data: paginatedData,
    total,
    limit,
    page,
    totalPages,
  };
}

export function createPaginationData(pagination?: PaginationData): PaginationData {
  return {
    page: pagination?.page || 1,
    limit: pagination?.limit || 10,
  };
}
