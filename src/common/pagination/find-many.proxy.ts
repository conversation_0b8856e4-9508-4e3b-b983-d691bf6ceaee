import { PaginationData, ProxyFunctions } from '@common/pagination/types';
import { Paginated } from '@common/pagination/paginated';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

export type FindManyPaginated<F extends ProxyFunctions> = {
  findManyPaginated: (
    data?: Omit<Parameters<F['findMany']>[0], 'take' | 'skip'>,
    pagination?: PaginationData,
  ) => Promise<Paginated<Awaited<ReturnType<F['findMany']>>[0]>>;
};

export function makeFindManyPaginated(model: ProxyFunctions) {
  return new Proxy(model.findMany, {
    apply: async (target, thisArg, [data, paginationInfo]) => {
      const page = paginationInfo?.page || 1;
      const limit = paginationInfo?.limit ?? 10;

      // Extract and validate sort parameter
      const sort = paginationInfo?.sort || {};
      if (typeof sort !== 'object') {
        throw new BusinessException(
          'Pagination',
          "Invalid sort format. Expected an object like { fieldName: 'asc' | 'desc' }.",
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }

      // Ensure the query format is compatible with Prisma
      const query = data || {};
      query.take = limit === 0 ? undefined : limit;
      query.skip = (page - 1) * limit;
      query.orderBy = sort;

      const [total, docs] = await Promise.all([
        model.count({
          where: query.where,
        }),
        target.apply(thisArg, [query]),
      ]);

      const totalPages = Math.ceil(total / limit);

      return {
        data: docs,
        total,
        limit,
        page,
        totalPages: totalPages === Infinity ? 1 : totalPages,
      };
    },
  });
}
