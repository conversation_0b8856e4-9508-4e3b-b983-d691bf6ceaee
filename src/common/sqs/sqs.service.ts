import {
  SendMessageBatchCommand,
  SendMessageBatchCommandOutput,
  SendMessageBatchRequestEntry,
  SendMessageCommand,
  SendMessageCommandOutput,
  SQSClient,
} from '@aws-sdk/client-sqs';
import { CommunicationChannel } from '@common/enums';
import { logger } from '@edutalent/commons-sdk';
import { Injectable } from '@nestjs/common';
import { Consumer } from 'sqs-consumer';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import {
  sanitizeMessageBody,
  sanitizeBatchMessages,
} from '@common/utils/message-sanitization.util';

@Injectable()
export class SQSService {
  private readonly sqsClient: SQSClient;
  private readonly importItemQueuePrefix: string;
  private readonly sqsQueueBaseUrl: string;
  private readonly importItemQueueSufix: string;

  constructor() {
    this.sqsClient = new SQSClient({
      region: process.env.AWS_REGION,
    });
    this.importItemQueuePrefix = process.env.IMPORT_ITEM_QUEUE_PREFIX;
    this.sqsQueueBaseUrl = process.env.SQS_QUEUE_BASE_URL;
    this.importItemQueueSufix = process.env.IMPORT_ITEM_QUEUE_SUFIX;
  }

  public async produce(
    queueUrl: string,
    messageBody: any,
  ): Promise<SendMessageCommandOutput | void> {
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const startTime = Date.now();

    logger.info('SQS message production initiated', {
      traceId,
      queueUrl: this.sanitizeQueueUrl(queueUrl),
      messageSize: JSON.stringify(messageBody).length,
      messageBody: sanitizeMessageBody(messageBody),
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'SQS_PRODUCER',
      operation: 'produce_message',
    });

    const params = { QueueUrl: queueUrl, MessageBody: JSON.stringify(messageBody) };

    try {
      const response = await this.sqsClient.send(new SendMessageCommand(params));
      const duration = Date.now() - startTime;

      logger.info('SQS message produced successfully', {
        traceId,
        queueUrl: this.sanitizeQueueUrl(queueUrl),
        messageId: response.MessageId,
        messageBody: sanitizeMessageBody(messageBody),
        duration: `${duration}ms`,
        businessContext: context?.operation,
        timestamp: new Date().toISOString(),
        layer: 'SQS_PRODUCER',
        operation: 'produce_message_success',
      });

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('SQS message production failed', {
        traceId,
        queueUrl: this.sanitizeQueueUrl(queueUrl),
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        errorType: this.getSQSErrorType(error),
        severity: this.getSQSErrorSeverity(error),
        retryable: this.isSQSRetryableError(error),
        businessContext: context?.operation,
        messageBody: sanitizeMessageBody(messageBody),
        requestId: error.$metadata?.requestId,
        httpStatusCode: error.$metadata?.httpStatusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SQS_PRODUCER',
        operation: 'produce_message_error',
      });

      throw error;
    }
  }

  public async produceBatch(
    queueUrl: string,
    messages: SendMessageBatchRequestEntry[],
  ): Promise<SendMessageBatchCommandOutput | void> {
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const startTime = Date.now();

    logger.info('SQS batch message production initiated', {
      traceId,
      queueUrl: this.sanitizeQueueUrl(queueUrl),
      batchSize: messages.length,
      totalSize: JSON.stringify(messages).length,
      messageBody: sanitizeBatchMessages(messages),
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'SQS_PRODUCER',
      operation: 'produce_batch',
    });

    const params = {
      QueueUrl: queueUrl,
      Entries: messages,
    };

    try {
      const response = await this.sqsClient.send(new SendMessageBatchCommand(params));
      const duration = Date.now() - startTime;

      logger.info('SQS batch messages produced successfully', {
        traceId,
        queueUrl: this.sanitizeQueueUrl(queueUrl),
        batchSize: messages.length,
        successfulCount: response.Successful?.length || 0,
        failedCount: response.Failed?.length || 0,
        messageBody: sanitizeBatchMessages(messages),
        duration: `${duration}ms`,
        businessContext: context?.operation,
        timestamp: new Date().toISOString(),
        layer: 'SQS_PRODUCER',
        operation: 'produce_batch_success',
      });

      // Log individual failures if any
      if (response.Failed && response.Failed.length > 0) {
        logger.warn('SQS batch production had partial failures', {
          traceId,
          queueUrl: this.sanitizeQueueUrl(queueUrl),
          failedMessages: response.Failed,
          businessContext: context?.operation,
          timestamp: new Date().toISOString(),
          layer: 'SQS_PRODUCER',
          operation: 'produce_batch_partial_failure',
        });
      }

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('SQS batch message production failed', {
        traceId,
        queueUrl: this.sanitizeQueueUrl(queueUrl),
        batchSize: messages.length,
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        errorType: this.getSQSErrorType(error),
        severity: this.getSQSErrorSeverity(error),
        retryable: this.isSQSRetryableError(error),
        businessContext: context?.operation,
        requestId: error.$metadata?.requestId,
        httpStatusCode: error.$metadata?.httpStatusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SQS_PRODUCER',
        operation: 'produce_batch_error',
      });

      throw error;
    }
  }

  public createConsumer(queueUrl: string, batchSize: number, handler): void {
    logger.info('Creating consumer for queue: ', queueUrl);

    try {
      // Add random polling wait time to prevent multiple consumers from polling simultaneously
      const randomPollingWaitTime = Math.floor(Math.random() * 3000) + 1000; // 1-4 seconds

      const consumer = Consumer.create({
        queueUrl: queueUrl,
        batchSize: batchSize,
        handleMessageBatch: handler,
        pollingWaitTimeMs: randomPollingWaitTime, // Random delay between polls
        waitTimeSeconds: 20, // Long polling to reduce API calls
      });

      consumer.start();
      logger.info(
        `Consumer started for queue: ${queueUrl} with ${randomPollingWaitTime}ms polling interval`,
      );
    } catch (error) {
      const traceId = CorrelationContextService.getTraceId();
      const context = CorrelationContextService.getContext();

      logger.error('SQS consumer creation failed', {
        traceId,
        queueUrl: this.sanitizeQueueUrl(queueUrl),
        batchSize,
        error: error.message,
        errorCode: error.name,
        errorType: this.getSQSErrorType(error),
        severity: 'CRITICAL',
        retryable: false,
        businessContext: context?.operation,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SQS_CONSUMER_CREATION',
        operation: 'create_consumer_error',
      });
    }
  }

  public getQueueByTypeAndSegment(type: string, segment: string): string {
    return `PORTFOLIO_${type}_${segment}_QUEUE_URL`.toUpperCase();
  }

  public getImportItemQueueByCustomer(customerId: string) {
    const traceId = CorrelationContextService.getTraceId();
    logger.info('Getting import item queue for customer', {
      traceId,
      customerId,
      operation: 'getImportItemQueueByCustomer',
      layer: 'SQS_PRODUCER',
    });

    return `${this.sqsQueueBaseUrl}${this.importItemQueuePrefix}${customerId}${this.importItemQueueSufix}`;
  }

  public getOutgoingQueueNameByChannel(_communicationChannel: CommunicationChannel): string {
    return `OUTGOING_MESSAGE_QUEUE_URL`.toUpperCase();
  }

  private sanitizeQueueUrl(queueUrl: string): string {
    if (!queueUrl) return 'unknown';

    try {
      // Remove sensitive information from queue URL
      const url = new URL(queueUrl);
      return `${url.protocol}//${url.host}${url.pathname}`;
    } catch {
      return queueUrl.replace(/\/\/.*@/, '//[CREDENTIALS_REDACTED]@');
    }
  }

  private getSQSErrorType(error: any): string {
    if (!error.name) return 'UNKNOWN_ERROR';

    switch (error.name) {
      case 'QueueDoesNotExist':
        return 'QUEUE_NOT_FOUND';
      case 'AccessDenied':
        return 'ACCESS_DENIED';
      case 'InvalidParameterValue':
        return 'INVALID_PARAMETER';
      case 'MissingParameter':
        return 'MISSING_PARAMETER';
      case 'InvalidAttributeName':
        return 'INVALID_ATTRIBUTE';
      case 'MessageNotInflight':
        return 'MESSAGE_NOT_INFLIGHT';
      case 'PurgeQueueInProgress':
        return 'PURGE_IN_PROGRESS';
      case 'QueueDeletedRecently':
        return 'QUEUE_DELETED_RECENTLY';
      case 'RequestThrottled':
        return 'REQUEST_THROTTLED';
      case 'ServiceUnavailable':
        return 'SERVICE_UNAVAILABLE';
      case 'InternalError':
        return 'INTERNAL_ERROR';
      case 'NetworkingError':
        return 'NETWORK_ERROR';
      case 'TimeoutError':
        return 'TIMEOUT';
      default:
        return `SQS_ERROR_${error.name}`;
    }
  }

  private getSQSErrorSeverity(error: any): string {
    if (!error.name) return 'HIGH';

    // Critical infrastructure errors
    const criticalErrors = [
      'ServiceUnavailable',
      'InternalError',
      'NetworkingError',
      'TimeoutError',
    ];
    if (criticalErrors.includes(error.name)) return 'CRITICAL';

    // High priority errors
    const highPriorityErrors = ['QueueDoesNotExist', 'AccessDenied'];
    if (highPriorityErrors.includes(error.name)) return 'HIGH';

    // Medium priority errors
    const mediumPriorityErrors = ['RequestThrottled', 'PurgeQueueInProgress'];
    if (mediumPriorityErrors.includes(error.name)) return 'MEDIUM';

    return 'LOW';
  }

  private isSQSRetryableError(error: any): boolean {
    if (!error.name) return false;

    const retryableErrors = [
      'RequestThrottled',
      'ServiceUnavailable',
      'InternalError',
      'NetworkingError',
      'TimeoutError',
    ];

    return retryableErrors.includes(error.name);
  }
}
