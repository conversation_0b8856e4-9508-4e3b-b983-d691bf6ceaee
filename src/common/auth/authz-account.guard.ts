import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AccountRole } from '@common/enums';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';

@Injectable()
export class AuthzAccountGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const controller = context.getClass();
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      handler,
      controller,
    ]);

    if (isPublic) {
      return true;
    }

    const excludedGuards = this.reflector.getAllAndOverride<string[]>(EXCLUDE_GUARDS_KEY, [
      handler,
      controller,
    ]);

    if (excludedGuards?.includes('AuthzAccountGuard')) {
      return true;
    }

    const requiredRoles = this.reflector.getAllAndOverride<AccountRole[]>('accountRoles', [
      handler,
      controller,
    ]);

    const request = context.switchToHttp().getRequest();

    if (!requiredRoles) {
      throw new AuthException(
        'AuthzAccountGuard::no-account-roles',
        'You are not authorized to access this route',
        AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_ROUTE,
        'be0a08b8-56c0-4d2d-94af-752a85cff1e8',
        `No defined any account role in route ${request.url}`,
      );
    }

    if (requiredRoles.length === 1 && requiredRoles[0] === AccountRole.__NONE__) {
      return true;
    }

    const accountRole = request.user?.accountRole?.toUpperCase();

    if (!accountRole) {
      throw new AuthException(
        'AuthzAccountGuard::no-account-roles',
        'You are not authorized to access this route',
        AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_USER,
        '789970cf-089b-4b74-9b3a-3c9060577d45',
        `The user ${request.user?.id} has no account role to access route ${request.url}`,
      );
    }

    const hasValidRole = requiredRoles?.some(role => accountRole === role);

    if (!hasValidRole) {
      throw new AuthException(
        'AuthzAccountGuard::invalid-account-role',
        'You are not authorized to access this route',
        AuthExceptionStatus.INVALID_ACCOUNT_ROLE,
        '0a9e1535-31b2-4ff4-a09c-0a7cce10513c',
        `The user ${request.user?.id} has no valid account role to access route ${request.url}`,
      );
    }

    return hasValidRole;
  }
}
