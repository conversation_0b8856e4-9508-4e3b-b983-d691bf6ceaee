import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UserRoleInAccount } from '@common/enums';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';

@Injectable()
export class AuthzUserInAccountGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const controller = context.getClass();
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      handler,
      controller,
    ]);

    if (isPublic) {
      return true;
    }

    const excludedGuards = this.reflector.getAllAndOverride<string[]>(EXCLUDE_GUARDS_KEY, [
      handler,
      controller,
    ]);

    if (excludedGuards?.includes('AuthzUserInAccountGuard')) {
      return true;
    }

    const requiredRoles = this.reflector.getAllAndOverride<UserRoleInAccount[]>(
      'userRoleInAccount',
      [handler, controller],
    );

    const request = context.switchToHttp().getRequest();

    if (!requiredRoles) {
      throw new AuthException(
        'AuthzUserInAccountGuard::no-user-role-in-route',
        'You are not authorized to access this route',
        AuthExceptionStatus.UNDEFINED_USER_ROLE_IN_ROUTE,
        'b72ee508-2e7b-4ab7-8eb7-17fe1e7aac4a',
        `No defined any user role in account in route ${request.url}`,
      );
    }

    if (requiredRoles.length === 1 && requiredRoles[0] === UserRoleInAccount.__NONE__) {
      return true;
    }

    const userRole = request.user?.roleInAccount?.toUpperCase();

    if (!userRole) {
      throw new AuthException(
        'AuthzUserInAccountGuard::no-user-role-in-account',
        'You are not authorized to access this route',
        AuthExceptionStatus.USER_HAS_NO_ROLE_IN_ACCOUNT,
        '35dd2ea7-8d6b-4cef-b201-d349b980af1e',
        `The user ${request.user?.id} has no role in account to perform this action in route ${request.url}`,
      );
    }

    const hasValidRole = requiredRoles?.some(role => userRole === role);

    if (!hasValidRole) {
      throw new AuthException(
        'AuthzAccountGuard::invalid-user-role-in-account',
        'You are not authorized to access this route',
        AuthExceptionStatus.INVALID_USER_ROLE_IN_ACCOUNT,
        '19edba3d-1ecd-49dc-9077-c470af7a3638',
        `The user ${request.user?.id} has no valid role in account to perform this action in route ${request.url}`,
      );
    }

    return hasValidRole;
  }
}
