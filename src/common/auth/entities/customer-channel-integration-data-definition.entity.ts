import { IsDate, IsNotEmpty } from 'class-validator';

export class CustomerChannelIntegrationDataDefinitionEntity {
  @IsNotEmpty()
  readonly id: string;

  @IsNotEmpty()
  readonly data: any;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(id: string, data: any, createdAt?: Date, updatedAt?: Date) {
    this.id = id;
    this.data = data;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
