import { TokenStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class TokenEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly userId: string;

  @IsString()
  @IsNotEmpty()
  readonly token: string;

  @IsString()
  @IsNotEmpty()
  readonly reference: string;

  @IsEnum(TokenStatus)
  @IsNotEmpty()
  readonly status: TokenStatus;

  @IsDate()
  @IsNotEmpty()
  readonly expireAt: Date;

  @IsDate()
  @IsOptional()
  readonly createdAt?: Date;

  @IsDate()
  @IsOptional()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    userId: string,
    token: string,
    reference: string,
    status: TokenStatus,
    expireAt: Date,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.userId = userId;
    this.token = token;
    this.reference = reference;
    this.status = status;
    this.createdAt = createdAt;
    this.expireAt = expireAt;
    this.updatedAt = updatedAt;
  }
}
