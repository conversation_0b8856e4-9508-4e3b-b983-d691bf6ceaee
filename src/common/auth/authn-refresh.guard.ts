import { CanActivate, ExecutionContext, Inject, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { IS_PUBLIC_KEY } from '@common/auth/decorators/public.decorator';
import { Reflector } from '@nestjs/core';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { TokenPort } from '@common/auth/db/ports/token.port';
import { TokenReference, TokenStatus } from '@common/enums';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';

@Injectable()
export class AuthnRefreshGuard implements CanActivate {
  constructor(
    private readonly jwtService: JwtService,
    private readonly reflector: Reflector,
    @Inject('TokenPort')
    private readonly tokenAdapter: TokenPort,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const controller = context.getClass();
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      handler,
      controller,
    ]);
    if (isPublic) {
      return true;
    }

    const excludedGuards = this.reflector.getAllAndOverride<string[]>(EXCLUDE_GUARDS_KEY, [
      handler,
      controller,
    ]);

    if (excludedGuards?.includes('AuthnRefreshGuard')) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    const token = await this.extractTokenFromHeader(request);

    if (!token) {
      throw new AuthException(
        'AuthnRefreshGuard::no-token',
        'You must be authenticated to access this route',
        AuthExceptionStatus.NO_TOKEN_IN_PROTECTED_ROUTE,
        '92f4ad36-6eb5-45c8-820c-52006b2a0e01',
        `No ${TokenReference.PRIVATE_API__REFRESH_TOKEN} token in protected route ${request.url}`,
      );
    }

    let payload: any;
    try {
      payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.JWT_SECRET,
      });
    } catch {
      throw new AuthException(
        'AuthnRefreshGuard::invalid-signature',
        'You must be authenticated to access this route',
        AuthExceptionStatus.INVALID_TOKEN_SIGNATURE,
        '86c26fd9-0e22-41f5-888a-8ccbe2c20b41',
        `Invalid ${TokenReference.PRIVATE_API__REFRESH_TOKEN} token signature in protected route ${request.url}`,
      );
    }

    const tokenEntity = await this.tokenAdapter.getFirst({
      token,
      userId: payload.userId,
      reference: TokenReference.PRIVATE_API__REFRESH_TOKEN,
    });

    if (!tokenEntity) {
      throw new AuthException(
        'AuthnRefreshGuard::token-not-found',
        'You must be authenticated to access this route',
        AuthExceptionStatus.TOKEN_NOT_FOUND,
        '44624dbf-d5a3-4c88-9dd8-048c162ad838',
        `Not found ${TokenReference.PRIVATE_API__REFRESH_TOKEN} token for protected route ${request.url}`,
      );
    }

    const tokenId = tokenEntity.id;

    if (tokenEntity.status !== TokenStatus.ACTIVE) {
      throw new AuthException(
        'AuthnRefreshGuard::revoked-token',
        'You must be authenticated to access this route',
        AuthExceptionStatus.TOKEN_IS_REVOKED,
        'aa8a3471-d5e0-4acc-b23c-8896874649d1',
        `${TokenReference.PRIVATE_API__REFRESH_TOKEN} Token ${tokenId} is ${tokenEntity.status} . Protected route ${request.url}`,
      );
    }

    if (tokenEntity.expireAt < new Date()) {
      throw new AuthException(
        'AuthnRefreshGuard::expired-token',
        'You must be authenticated to access this route',
        AuthExceptionStatus.TOKEN_EXPIRED,
        '5e233505-4595-4488-a4f9-3fda7218c8db',
        ` ${TokenReference.PRIVATE_API__ACCESS_TOKEN} Token ${tokenId} is expired . Protected route ${request.url}`,
      );
    }

    // We're assigning the payload to the request object here
    // so that we can access it in our route handlers
    request['user'] = payload;
    return true;
  }

  private async extractTokenFromHeader(request: Request): Promise<string | undefined> {
    const [type, headerToken] = request.headers.authorization?.split(' ') ?? [];
    const bearerToken = type === 'Bearer' ? headerToken : undefined;

    const token = bearerToken ?? undefined;
    return token;
  }
}
