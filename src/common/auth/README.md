# Authentication System Overview

This document provides a detailed explanation of how the authentication system works, including the structure, logic, and components involved. Our system uses JWT (JSON Web Tokens) for user authentication, ensuring secure and efficient communication between the server and client.

## How Does the Authentication System Work?

The authentication system is based on JWTs to authenticate users securely. JWTs are a compact and secure way of transmitting information between two parties. In this context, the server generates a JWT that contains the authenticated user's information and sends it to the client. The client must include this token in each request it sends to the server, allowing the server to verify that the user is authenticated.

### Authentication Flow

1. **User Login:** The client sends a POST request to the server with the user's credentials (email and password).
2. **Token Generation:** The server verifies the credentials and generates two JWTs with user information:
   - **Access Token:** Used to authenticate the user on each request made to the server. This token has a short lifespan (e.g., 15 minutes).
   - **Refresh Token:** Used to renew the access token when it expires. This token has a longer lifespan (e.g., 7 days).
3. **Token Distribution:** The server sends the generated JWTs back to the client. The client must store these tokens securely and include the access token in the Authorization header of every request to the server.

#### JWT Payload Example

```json
{
  "userId": "123456", // User ID
  "accountId": "654321", // User's business account ID
  "customerId": "789012", // Business customer ID
  "roleInAccount": "ADMIN", // User's role within the business account
  "accountRole": "BASIC" // Role of the business account in the platform
}
```

The server validates the JWT on each request and, if valid, allows the user to access protected resources. JWTs have an expiration date, so the client must renew the token before it expires to maintain the session.

### Roles and Permissions

All resources created by a user are associated with the business account, not the user itself. Therefore, users can only access resources that belong to the business account to which they are associated.

The platform defines two levels of roles:

- **User Role in Business Account:** Defines the actions a user can perform within a business account (e.g., `ADMIN`, `MAINTAIN`, `WRITE`, `READ`).
- **Business Account Role:** Defines the capabilities of the business account on the platform (e.g., `BASIC`, `PLUS`).

The server verifies that the user has the necessary permissions to access the requested endpoint. If the required permissions are not present, the server returns an authorization error.

## Guard System for Endpoint Protection

The system uses four guards to manage access to endpoints:

1. **AuthnGuard:** Verifies if the user is authenticated by validating the access token.
2. **AuthzAccountGuard:** Verifies if the business account has the necessary permissions to access the requested endpoint.
3. **AuthzUserInAccountGuard:** Verifies if the user has the required permissions to access the requested endpoint for the business account they are logged into.
4. **BlockAccessGuard:** Blocks access to endpoints regardless of authentication or permissions of the user/account.

### Guard Hierarchy

The guards are evaluated in the following order:

1. **BlockAccessGuard**
2. **AuthnGuard**
3. **AuthzAccountGuard**
4. **AuthzUserInAccountGuard**

## The Auth Module

The `AuthModule` is responsible for managing the authentication process. It includes the services and controllers necessary for user authentication. The guards for permission checks are implemented in the `common` module.

## Defining User and Account Roles in Endpoints

The platform includes specific decorators for defining user and account roles on endpoints. These decorators can be applied globally, to controllers, or to individual methods to specify the roles required for accessing an endpoint.

### Available Decorators

- **@Public()**: Marks an endpoint as public, meaning no authentication is required. This can be used on controllers or methods.

- **@AccountRoles(...accountRoles: AccountRole[])**: Specifies the roles of the business account that can access an endpoint. This can be used on controllers or methods.

- **@UserRolesInAccount(...userRolesInAccount: UserRoleInAccount[])**: Specifies the roles of the user within the business account that are allowed to access an endpoint. This can be used on controllers or methods.

- **@BlockAccess()**: Blocks access to an endpoint, regardless of user or account permissions.

- **@ExcludeGuards(...guards: string[])**: Excludes specific guards from being applied to an endpoint.

### Security Principles

Following zero trust security principles, all endpoints are private by default. This means that it is necessary to explicitly define which endpoints are public and which roles are required for accessing protected endpoints.

#### Example Usage

- **Public Endpoint**:

  ```typescript
  @Public()
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  ```

- **Endpoint Requiring Specific Roles**:

  ```typescript
  @AccountRoles(AccountRole.BASIC, AccountRole.PLUS)
  @UserRolesInAccount(UserRoleInAccount.ADMIN, UserRoleInAccount.MAINTAIN)
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  ```

- **Blocked Endpoint**:

  ```typescript
  @BlockAccess()
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  ```

- **Endpoint with Guard Exclusion**:

  ```typescript
  @ExcludeGuards('AuthzAccountGuard')
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  ```

- **Endpoint without Specific Role Requirements**:

  ```typescript
  @AccountRoles(AccountRole.__NONE__)
  @UserRolesInAccount(UserRoleInAccount.__NONE__)
  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }
  ```

If `__NONE__` is combined with other roles in `@AccountRoles()` or `@UserRolesInAccount()`, it will be ignored. For example:

```typescript
@AccountRoles(AccountRole.__NONE__, AccountRole.BASIC)
@UserRolesInAccount(UserRoleInAccount.__NONE__, UserRoleInAccount.ADMIN)
@Get()
async findAll(): Promise<User[]> {
  return this.usersService.findAll();
}
```

In this case, the endpoint will only be accessible by users with the `ADMIN` role in a business account with the `BASIC` role.

### How to Use Logged Users in Tests

To test endpoints that require authentication, you can use the helper function `getAuthCredentials` to log in a user and retrieve their access token. This token can then be included in the headers of subsequent requests to simulate authenticated actions. Below is an example of how to structure your tests to handle logged users effectively:

```typescript
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('ProtectedResourceController (e2e)', () => {
  let app: INestApplication;
  let accessJWTToken: string;

  beforeAll(async () => {
    // Initialize the application
    app = global.__NEST_APP__;

    // Retrieve authentication credentials
    const { accessToken } = await getAuthCredentials(app, '<EMAIL>', 'securePassword');
    accessJWTToken = accessToken;
  });

  it('/v1/protected/resource (GET) - should return protected resource for authenticated user', async () => {
    const response = await request(app.getHttpServer())
      .get('/api/v1/protected/resource')
      .set('Authorization', `Bearer ${accessJWTToken}`)
      .expect(200);

    expect(response.body).toBeDefined();
    expect(response.body.data).toEqual('Expected protected resource data');
  });
});
```

#### Key Points:
1. **Helper Function:** Use a helper function like `getAuthCredentials` to encapsulate the login logic and fetch the access token for a test user.
2. **Token Injection:** Include the `Authorization` header with the retrieved `accessJWTToken` in requests to endpoints that require authentication.
3. **Additional Information:**: Besides the access token, the `getAuthCredentials` function can return other useful information, such as the refreshToken and the user information from the payload of the token. 

This approach ensures the authentication flow is properly tested, while also verifying that only logged-in users can access protected resources.


## Summary

The authentication system relies on JWT for secure user authentication and includes a robust guard mechanism to control access to endpoints based on both user and account roles. By leveraging decorators, the platform offers flexibility in defining access control requirements at both the controller and method levels. Following zero trust security principles, all endpoints are private by default, requiring explicit role definitions for access.

The `AuthModule` ensures that only authorized users can access protected resources, maintaining the integrity and confidentiality of business data.

