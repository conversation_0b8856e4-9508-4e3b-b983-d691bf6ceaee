import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { Injectable } from '@nestjs/common';
import { TokenEntity } from '@common/auth/entities/token.entity';
import { TokenPort } from '@common/auth/db/ports/token.port';
import { Prisma } from '@prisma/client';
import { TokenStatus } from '@common/enums';

@Injectable()
export class TokenAdapter extends PrismaCommonAdapter<TokenEntity> implements TokenPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'token');
  }

  async getFirst(where: Prisma.TokenWhereInput): Promise<TokenEntity> {
    return this.prismaClient.token.findFirst({ where }).then(token => {
      return token ? { ...token, status: token.status as TokenStatus } : null;
    });
  }
}
