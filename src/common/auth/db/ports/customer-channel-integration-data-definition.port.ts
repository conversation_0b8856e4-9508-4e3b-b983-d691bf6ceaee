import { CustomerChannelIntegrationDataDefinitionEntity } from '@common/auth/entities/customer-channel-integration-data-definition.entity';

export interface CustomerChannelIntegrationDataDefinitionPort {
  create(
    entity: CustomerChannelIntegrationDataDefinitionEntity,
  ): Promise<CustomerChannelIntegrationDataDefinitionEntity>;

  get(key: string): Promise<CustomerChannelIntegrationDataDefinitionEntity>;
}
