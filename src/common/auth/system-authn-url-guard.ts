import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';
import { CommunicationDirection } from '@common/enums';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';
import { Injectable, CanActivate, ExecutionContext, Inject } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class SystemAuthnURLGuard implements CanActivate {
  constructor(
    @Inject('CustomerChannelIntegrationDataDefinitionPort')
    private readonly customerChannelIntegrationDataDefinitionAdapter: CustomerChannelIntegrationDataDefinitionPort,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    const token = await this.extractTokenFromUrl(request);

    const customerChannelIntegrationDataDefinition =
      await this.customerChannelIntegrationDataDefinitionAdapter.get(token);

    if (
      !customerChannelIntegrationDataDefinition ||
      !customerChannelIntegrationDataDefinition.data.active ||
      customerChannelIntegrationDataDefinition.data['direction'] !== CommunicationDirection.INBOUND
    ) {
      throw new AuthException(
        'SystemAuthnURLGuard::invalid-token',
        'Invalid token',
        AuthExceptionStatus.INVALID_TOKEN,
        'fb2862cf-02e3-4021-a739-14d2ffe870d9',
        `System authentication failed`,
      );
    }

    request['user'] = customerChannelIntegrationDataDefinition.data;

    return true;
  }

  private async extractTokenFromUrl(request: Request): Promise<string | undefined> {
    const token = request.query.token as string | undefined;
    return token;
  }
}
