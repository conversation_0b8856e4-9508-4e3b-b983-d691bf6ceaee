import { CustomerChannelIntegrationDataDefinitionPort } from '@common/auth/db/ports/customer-channel-integration-data-definition.port';
import { CommunicationDirection } from '@common/enums';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';
import { Injectable, CanActivate, ExecutionContext, Inject } from '@nestjs/common';
import { Request } from 'express';

@Injectable()
export class SystemAuthnGuard implements CanActivate {
  constructor(
    @Inject('CustomerChannelIntegrationDataDefinitionPort')
    private readonly customerChannelIntegrationDataDefinitionAdapter: CustomerChannelIntegrationDataDefinitionPort,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();

    const token = await this.extractTokenFromHeader(request);

    const customerChannelIntegrationDataDefinition =
      await this.customerChannelIntegrationDataDefinitionAdapter.get(token);

    if (
      !customerChannelIntegrationDataDefinition ||
      !customerChannelIntegrationDataDefinition.data.active ||
      customerChannelIntegrationDataDefinition.data['direction'] !== CommunicationDirection.INBOUND
    ) {
      throw new AuthException(
        'SystemAuthnGuard::invalid-token',
        'Invalid token',
        AuthExceptionStatus.INVALID_TOKEN,
        '0a9e1535-31b2-4ff4-a09c-0a7cce10513c',
        `System authentication failed`,
      );
    }

    request['user'] = customerChannelIntegrationDataDefinition.data;

    return true;
  }

  private async extractTokenFromHeader(request: Request): Promise<string | undefined> {
    const [type, headerToken] = request.headers.authorization?.split(' ') ?? [];
    const bearerToken = type === 'Bearer' ? headerToken : undefined;

    const token = bearerToken ?? undefined;
    return token;
  }
}
