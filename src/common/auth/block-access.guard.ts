import { IS_BLOCKED } from '@common/auth/decorators/block-access.decorator';
import { EXCLUDE_GUARDS_KEY } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class BlockAccessGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const handler = context.getHandler();
    const controller = context.getClass();
    const excludedGuards = this.reflector.getAllAndOverride<string[]>(EXCLUDE_GUARDS_KEY, [
      handler,
      controller,
    ]);

    if (excludedGuards?.includes('BlockAccessGuard')) {
      return true;
    }

    const request = context.switchToHttp().getRequest();

    const isBlocked = this.reflector.getAllAndOverride<boolean>(IS_BLOCKED, [handler, controller]);
    if (isBlocked) {
      throw new AuthException(
        'BlockAccessGuard::blocked',
        'This functionality is blocked',
        AuthExceptionStatus.BLOCKED_ENDPOINT,
        '6d2a317c-5b0b-41cf-a005-0d251cf3075b',
        `User trying to access blocked endpoint. Route ${request.url}`,
      );
    }

    return true;
  }
}
