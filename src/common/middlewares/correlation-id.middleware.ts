import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { randomUUID as uuidv4 } from 'crypto';
import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class CorrelationIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    // Extract portfolioItemId from URL parameters for business correlation
    const portfolioItemIdMatch = req.originalUrl.match(/\/portfolio-items\/([a-f0-9-]{36})/i);
    const portfolioItemId = portfolioItemIdMatch ? portfolioItemIdMatch[1] : null;

    // Use portfolioItemId as primary trace identifier for portfolio-item related requests
    // Fall back to header-based traceId or generate new UUID
    const traceId =
      portfolioItemId ||
      (req.headers['x-correlation-id'] as string) ||
      (req.headers['correlation-id'] as string) ||
      (req.headers['x-trace-id'] as string) ||
      uuidv4();

    // Store trace ID in request and response locals for access throughout the request lifecycle
    // This is what the exception filters and logger library expect
    req['correlationId'] = traceId; // Keep for backward compatibility
    req['traceId'] = traceId;
    res.locals.trackRequest = traceId; // This is what exception filters use
    res.locals.correlationId = traceId; // Keep for backward compatibility
    res.locals.traceId = traceId;

    // Set response header for client tracking
    res.setHeader('X-Correlation-ID', traceId);
    res.setHeader('X-Trace-ID', traceId);

    // Run the rest of the request in correlation context
    CorrelationContextService.run({ traceId, layer: 'HTTP_REQUEST' }, () => {
      // Log incoming request with trace ID and portfolio item context
      logger.info('Incoming HTTP request', {
        traceId,
        correlationId: traceId, // Keep for backward compatibility
        portfolioItemId: portfolioItemId || undefined,
        method: req.method,
        url: req.originalUrl,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        timestamp: new Date().toISOString(),
        layer: 'HTTP_REQUEST',
      });

      // Track request start time for performance logging
      const startTime = Date.now();
      res.locals.startTime = startTime;

      // Override res.end to log response details
      const originalEnd = res.end.bind(res);
      res.end = function (chunk?: any, encoding?: any, cb?: () => void) {
        const duration = Date.now() - startTime;

        logger.info('HTTP request completed', {
          traceId,
          correlationId: traceId, // Keep for backward compatibility
          method: req.method,
          url: req.originalUrl,
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          timestamp: new Date().toISOString(),
          layer: 'HTTP_RESPONSE',
        });

        return originalEnd(chunk, encoding, cb);
      };

      next();
    });
  }
}
