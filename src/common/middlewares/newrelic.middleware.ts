// newrelic.middleware.ts
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import newrelic from 'newrelic';

@Injectable()
export class NewRelicMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    const route = `${req.method} ${req.originalUrl}`;
    newrelic.setTransactionName(route);
    next();
  }
}
