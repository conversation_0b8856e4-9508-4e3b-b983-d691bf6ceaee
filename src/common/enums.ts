export enum MessageType {
  TEXT = 'TEXT',
  AUDIO = 'AUDIO',
  PDF = 'application/pdf',
  MP3 = 'audio/mpeg',
  OPUS = 'audio/ogg',
  PNG = 'image/png',
  JPG = 'image/jpeg',
  GIF = 'image/gif',
  MP4 = 'video/mp4',
  MPEG = 'video/mpeg',
  PPTX = 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  PPT = 'application/vnd.ms-powerpoint',
  XLSX = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  XLS = 'application/vnd.ms-excel',
  DOCX = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  DOC = 'application/msword',
}

export enum RoleType {
  SYSTEM = 'system',
  ASSISTANT = 'assistant',
  USER = 'user',
  ATTENDANT = 'attendant',
}

export enum StepExecutionStatus {
  STARTED = 'started',
  RUNNING = 'running',
  SKIPPED = 'skipped',
  FINISHED = 'finished',
  CANCELLED = 'cancelled',
}

export enum PortfolioItemStatus {
  PENDING = 'PENDING', // aguardando envio da primeira mensagem
  IN_PROGRESS = 'IN_PROGRESS', //envio da primeira mensagem com sucesso
  FOLLOWED_UP = 'FOLLOWED_UP', //foi feito um envio de follow-up e está aguardando resposta
  SCHEDULED_FOLLOW_UP = 'SCHEDULED_FOLLOW_UP', //foi agendado um follow-up para o futuro
  OPTED_OUT = 'OPTED_OUT', // cliente optou por não continuar a conversa
  PAUSED = 'PAUSED', // parado pelo customer e pode voltar a in progress
  SUCCEED = 'SUCCEED', // negociação finalizou com sucesso
  FAILED = 'FAILED', // negociação finalizou sem sucesso
  CANCELLED = 'CANCELLED', // item cancelado pelo customer por resolução externa
  UNLINKED = 'UNLINKED', // O customer desvinculou a fala da IA e passa a digitar as mensagens por conta própria
  IDLE = 'IDLE', // This status will be used when last_interaction is greater than portfolio.idle_after
  FINISHED = 'FINISHED', // negociação finalizou com sucesso
}

export enum FollowUpType {
  FOLLOW_UP = 'FOLLOW_UP',
  PAYMENT_REMINDER = 'PAYMENT_REMINDER',
}

export enum PortfolioImportStatus {
  UPLOADED = 'UPLOADED',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
}

export enum PortfolioExecutionStatus {
  EXECUTING = 'EXECUTING',
  INBOUND = 'INBOUND_EXECUTING',
  QUEUED = 'QUEUED',
  PAUSED = 'PAUSED',
  WAITING = 'WAITING',
  CANCELLED = 'CANCELLED',
  FINISHED = 'FINISHED',
}

export enum MiddlewareType {
  POST_EXECUTION = 'POST_EXECUTION',
  POST_START = 'POST_START',
}

export enum MiddlewareToolCategory {
  TASK = 'TASK',
  INTEGRATION = 'INTEGRATION',
  FUNCTION = 'FUNCTION',
}

export enum RecordStatus {
  ACTIVE = 'ACTIVE',
  DELETED = 'DELETED',
}

export enum CommunicationChannel {
  WHATSAPPSELFHOSTED = 'WHATSAPPSELFHOSTED',
  WHATSAPPAPI = 'WHATSAPPAPI',
  MOCK = 'MOCK',
  SMS_VONAGE = 'SMS_VONAGE',
  BLIP_COLINA = 'BLIP_COLINA',
  LOVELACE = 'LOVELACE',
}

export enum ConversationAgentName {
  HIRING_DATA_INSIGHTS = 'HIRING_DATA_INSIGHTS',
  TEST_DATA = 'TEST_DATA',
}

export enum LangType {
  EN_US = 'en_US',
  PT_BR = 'pt_BR',
}

export enum ConversationType {
  HIRING = 'HIRING',
}

export enum ConversationMessageType {
  HUMAN = 'human',
  AI = 'ai',
}

export enum VectorType {
  TEXT = 'TEXT',
}

export enum UserRoleInAccount {
  __NONE__ = '__NONE__',
  ADMIN = 'ADMIN',
  MANTAIN = 'MANTAIN',
  WRITE = 'WRITE',
  READ = 'READ',
}

export enum AccountRole {
  __NONE__ = '__NONE__',
  __SYSTEM__ = '__SYSTEM__',
  __EMPLOYEE__ = '__EMPLOYEE__',
  BASIC = 'BASIC',
  PLUS = 'PLUS',
}

export enum AccountStatus {
  ACTIVE = 'ACTIVE',
  PENDING_CONFIRMATION = 'PENDING_CONFIRMATION',
  DELETED = 'DELETED',
}

export enum UserStatus {
  ACTIVE = 'ACTIVE',
  PENDING_CONFIRMATION = 'PENDING_CONFIRMATION',
  SUSPENDED = 'SUSPENDED',
  DELETED = 'DELETED',
}

export enum TokenStatus {
  ACTIVE = 'ACTIVE',
  REVOKED_BY_SYSTEM = 'REVOKED_BY_SYSTEM',
  REVOKED_BY_USER = 'REVOKED_BY_USER',
  REVOKED_BY_ACCOUNT_ADMIN = 'REVOKED_BY_ACCOUNT_ADMIN',
  DELETED = 'DELETED',
  EXPIRED = 'EXPIRED',
}

export enum TokenReference {
  PRIVATE_API__ACCESS_TOKEN = 'private-api::access-token',
  PRIVATE_API__REFRESH_TOKEN = 'private-api::refresh-token',
}

export enum LoginMethod {
  PASSWORD = 'password',
  EMAIL_SERVER_TOKEN = 'email_server_token',
}

export enum GroupByDate {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
}

export enum CommunicationDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}
