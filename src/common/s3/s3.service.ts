import {
  CompleteMultipartUploadCommand,
  CreateMultipartUploadCommand,
  GetObjectCommand,
  S3Client,
  UploadPartCommand,
} from '@aws-sdk/client-s3';
import 'amazon-s3-uri';
import { Upload } from '@aws-sdk/lib-storage';
import { Readable } from 'node:stream';
import StreamConcat from 'stream-concat';
import { logger } from '@edutalent/commons-sdk';
import { Injectable } from '@nestjs/common';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import AmazonS3URI from 'amazon-s3-uri';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class S3Service {
  private readonly s3Client: S3Client;

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION,
      forcePathStyle: true,
    });
  }

  public async getFileStream(bucket: string, fileKey: string): Promise<Readable | void> {
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const startTime = Date.now();

    logger.info('S3 file stream retrieval initiated', {
      traceId,
      bucket: this.sanitizeBucketName(bucket),
      fileKey: this.sanitizeFileKey(fileKey),
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'S3_CLIENT',
      operation: 'get_file_stream',
    });

    const params = { Bucket: bucket, Key: fileKey };

    try {
      const response = await this.s3Client.send(new GetObjectCommand(params));
      const duration = Date.now() - startTime;
      const s3Stream = response.Body as Readable;

      if (!s3Stream) {
        logger.error('S3 file stream retrieval failed - empty response body', {
          traceId,
          bucket: this.sanitizeBucketName(bucket),
          fileKey: this.sanitizeFileKey(fileKey),
          duration: `${duration}ms`,
          errorType: 'EMPTY_RESPONSE_BODY',
          severity: 'HIGH',
          businessContext: context?.operation,
          timestamp: new Date().toISOString(),
          layer: 'S3_CLIENT',
          operation: 'get_file_stream_error',
        });
        return;
      }

      logger.info('S3 file stream retrieved successfully', {
        traceId,
        bucket: this.sanitizeBucketName(bucket),
        fileKey: this.sanitizeFileKey(fileKey),
        duration: `${duration}ms`,
        contentLength: response.ContentLength,
        contentType: response.ContentType,
        businessContext: context?.operation,
        timestamp: new Date().toISOString(),
        layer: 'S3_CLIENT',
        operation: 'get_file_stream_success',
      });

      return s3Stream;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('S3 file stream retrieval failed', {
        traceId,
        bucket: this.sanitizeBucketName(bucket),
        fileKey: this.sanitizeFileKey(fileKey),
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        errorType: this.getS3ErrorType(error),
        severity: this.getS3ErrorSeverity(error),
        retryable: this.isS3RetryableError(error),
        businessContext: context?.operation,
        requestId: error.$metadata?.requestId,
        httpStatusCode: error.$metadata?.httpStatusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'S3_CLIENT',
        operation: 'get_file_stream_error',
      });

      throw error;
    }
  }

  public async getFileStreamFromPath(s3Uri: string): Promise<Readable | void> {
    const { bucket, fileKey } = await this.parseS3Uri(s3Uri);

    if (!bucket || !fileKey) {
      logger.error(`S3 service: Invalid S3 URI: ${s3Uri}`);
      return;
    }

    logger.info(`Fetching file stream from S3 bucket: ${bucket} file key: ${fileKey}`);
    const params = { Bucket: bucket, Key: fileKey };

    try {
      const response = await this.s3Client.send(new GetObjectCommand(params));
      const s3Stream = response.Body as Readable;
      if (!s3Stream) {
        logger.error(
          `Unable to get file stream from S3 bucket: ${bucket} file key: ${fileKey}. Response: ${JSON.stringify(
            response,
          )}`,
        );
        return;
      }
      return s3Stream;
    } catch (error) {
      logger.error(
        `Unable to get file stream from S3 bucket: ${bucket} file key: ${fileKey}. Error: ${JSON.stringify(
          error,
        )}`,
        error,
      );
    }
  }

  public async uploadFileStream(
    bucket: string,
    file: string,
    body: Readable,
  ): Promise<string | void> {
    logger.info(`Starting file: ${file} upload to S3 bucket: ${bucket}`);
    const upload = new Upload({
      client: this.s3Client,
      params: {
        Bucket: bucket,
        Key: file,
        Body: body,
      },
    });

    return upload
      .done()
      .then(result => decodeURIComponent(result.Location))
      .catch(error => {
        logger.error(
          `Unable to upload file: ${file} to S3 bucket: ${bucket}. Error: ${JSON.stringify(error)}`,
          error,
        );
      });
  }

  public async concatAndUploadFilesStream(
    fileUrls: string[],
    targetBucket: string,
    targetFileKey: string,
  ): Promise<string | void> {
    const streams: Readable[] = [];
    for (const fileUrl of fileUrls.filter(Boolean)) {
      const stream = await this.getFileStreamFromPath(fileUrl);
      if (stream && stream instanceof Readable) {
        streams.push(stream);
      } else {
        logger.warn(`Skipping file key ${fileUrl} due to missing or invalid stream`);
      }
    }

    if (streams.length === 0) {
      logger.error('No valid streams to concatenate');
      throw new Error('No valid streams to concatenate');
    }

    try {
      const combinedStream = new StreamConcat(streams, { highWaterMark: 1024 * 1024 });
      return await this.uploadFileStream(targetBucket, targetFileKey, combinedStream);
    } catch (error) {
      logger.error(`Failed to concatenate streams: ${JSON.stringify(error)}`, error);
      throw error;
    }
  }

  public async uploadFile(bucket: string, file: string, body: Buffer): Promise<string | void> {
    logger.info(`Starting file: ${file} upload to s3 bucket: ${bucket} `, file);
    const upload = new Upload({
      client: this.s3Client,
      params: {
        Bucket: bucket,
        Key: file,
        Body: body,
      },
    });

    return upload
      .done()
      .then(result => decodeURIComponent(result.Location))
      .catch(error => {
        logger.error(
          `Unable to upload file: ${file} to S3 bucket: ${bucket} . Error: ${JSON.stringify(
            error,
          )}`,
          error,
        );
      });
  }

  public async uploadFileMultPart(file: Express.Multer.File, bucketName: string, filename: string) {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Starting multipart upload to S3 bucket', {
      traceId,
      bucket: this.sanitizeBucketName(bucketName),
      fileKey: this.sanitizeFileKey(filename),
      operation: 'upload_file_mult_part',
      layer: 'S3_CLIENT',
    });

    try {
      const chunkSizeLimit = 10 * 1024 * 1024;
      const fileBuffer = file.buffer;
      const totalParts = Math.ceil(fileBuffer.length / chunkSizeLimit);
      const parts = [];
      const uploadId = await this.createMultipartUpload(bucketName, filename);
      logger.info('Multipart upload created', {
        traceId,
        bucket: this.sanitizeBucketName(bucketName),
        fileKey: this.sanitizeFileKey(filename),
        uploadId,
        operation: 'create_multipart_upload',
        layer: 'S3_CLIENT',
      });

      for (let partNumber = 0; partNumber < totalParts; partNumber++) {
        logger.info('Uploading part', {
          traceId,
          bucket: this.sanitizeBucketName(bucketName),
          fileKey: this.sanitizeFileKey(filename),
          partNumber: partNumber + 1,
          totalParts,
          operation: 'upload_part',
          layer: 'S3_CLIENT',
        });
        const start = partNumber * chunkSizeLimit;
        const end = Math.min(start + chunkSizeLimit, fileBuffer.length);
        const partBuffer = fileBuffer.subarray(start, end);

        const { ETag } = await this.uploadPart(
          bucketName,
          filename,
          uploadId,
          partBuffer,
          partNumber + 1,
        );
        logger.info(`Multipart uploaded: ${ETag}`);
        parts.push({ ETag, PartNumber: partNumber + 1 });
      }

      const result = await this.completeMultipartUpload(bucketName, filename, uploadId, parts);
      const finalLocationDecoded = decodeURIComponent(result.Location);
      logger.info('Multipart upload completed successfully', {
        traceId,
        bucket: this.sanitizeBucketName(bucketName),
        fileKey: this.sanitizeFileKey(filename),
        location: finalLocationDecoded,
        operation: 'complete_multipart_upload',
        layer: 'S3_CLIENT',
      });
      return { filename, location: finalLocationDecoded };
    } catch (err) {
      logger.error(`Failed to upload multipart file: ${filename} to S3 bucket: ${bucketName}`, {
        traceId,
        bucket: this.sanitizeBucketName(bucketName),
        fileKey: this.sanitizeFileKey(filename),
        error: err.message,
        errorType: this.getS3ErrorType(err),
        severity: this.getS3ErrorSeverity(err),
        retryable: this.isS3RetryableError(err),
        stack: process.env.NODE_ENV !== 'production' ? err.stack : undefined,
        operation: 'upload_file_mult_part_error',
        layer: 'S3_CLIENT',
      });
      throw new BusinessException(
        'S3Service',
        'Failed to upload multipart file',
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }
  }

  private async createMultipartUpload(bucketName: string, filename: string) {
    const createMultipartUploadCommand = new CreateMultipartUploadCommand({
      Bucket: bucketName,
      Key: filename,
    });

    const { UploadId } = await this.s3Client.send(createMultipartUploadCommand);
    return UploadId;
  }

  private async uploadPart(
    bucketName: string,
    filename: string,
    uploadId: string,
    partBuffer: Buffer,
    partNumber: number,
  ) {
    const uploadPartCommand = new UploadPartCommand({
      Bucket: bucketName,
      Key: filename,
      PartNumber: partNumber,
      UploadId: uploadId,
      Body: partBuffer,
    });

    const { ETag } = await this.s3Client.send(uploadPartCommand);
    return { ETag };
  }

  private async completeMultipartUpload(
    bucketName: string,
    filename: string,
    uploadId: string,
    parts: Array<{ ETag: string; PartNumber: number }>,
  ) {
    const completeMultipartUploadCommand = new CompleteMultipartUploadCommand({
      Bucket: bucketName,
      Key: filename,
      MultipartUpload: { Parts: parts },
      UploadId: uploadId,
    });

    return await this.s3Client.send(completeMultipartUploadCommand);
  }

  private async parseS3Uri(s3Uri: string): Promise<{ bucket: string; fileKey: string }> {
    try {
      const uri = s3Uri;
      if (!(process.env.NODE_ENV === 'development')) {
        const { bucket, key } = AmazonS3URI(uri);
        return { bucket, fileKey: key };
      } else {
        const { bucket, key } = this.extractS3Details(uri);
        return { bucket, fileKey: key };
      }
    } catch (err) {
      logger.error(`S3 service: ${s3Uri} is not a valid S3 uri`);
      return { bucket: null, fileKey: null };
    }
  }

  private extractS3Details(s3Path: string) {
    try {
      const url = new URL(s3Path);
      const pathParts = url.pathname.split('/').filter(part => part);
      const bucket = pathParts[0];
      const key = pathParts.slice(1).join('/');

      if (!bucket || !key) {
        throw new Error('Invalid S3 path format');
      }

      return { bucket, key };
    } catch (error) {
      throw new Error(`Failed to parse S3 path: ${error.message}`);
    }
  }

  private sanitizeBucketName(bucket: string): string {
    if (!bucket) return 'unknown';
    // Remove any sensitive information from bucket name
    return bucket.replace(/[0-9]{12}/, '[ACCOUNT_ID]');
  }

  private sanitizeFileKey(fileKey: string): string {
    if (!fileKey) return 'unknown';

    // Redact sensitive patterns in file keys
    return fileKey
      .replace(/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/gi, '[UUID]')
      .replace(/\d{11,}/g, '[PHONE_NUMBER]')
      .replace(/\d{3}\.\d{3}\.\d{3}-\d{2}/g, '[CPF]');
  }

  private getS3ErrorType(error: any): string {
    if (!error.name) return 'UNKNOWN_ERROR';

    switch (error.name) {
      case 'NoSuchBucket':
        return 'BUCKET_NOT_FOUND';
      case 'NoSuchKey':
        return 'OBJECT_NOT_FOUND';
      case 'AccessDenied':
        return 'ACCESS_DENIED';
      case 'InvalidBucketName':
        return 'INVALID_BUCKET_NAME';
      case 'BucketAlreadyExists':
        return 'BUCKET_ALREADY_EXISTS';
      case 'BucketNotEmpty':
        return 'BUCKET_NOT_EMPTY';
      case 'InvalidObjectName':
        return 'INVALID_OBJECT_NAME';
      case 'EntityTooLarge':
        return 'OBJECT_TOO_LARGE';
      case 'InvalidPart':
        return 'INVALID_MULTIPART';
      case 'InvalidPartOrder':
        return 'INVALID_PART_ORDER';
      case 'NoSuchUpload':
        return 'UPLOAD_NOT_FOUND';
      case 'ServiceUnavailable':
        return 'SERVICE_UNAVAILABLE';
      case 'InternalError':
        return 'INTERNAL_ERROR';
      case 'SlowDown':
        return 'RATE_LIMITED';
      case 'RequestTimeout':
        return 'REQUEST_TIMEOUT';
      case 'NetworkingError':
        return 'NETWORK_ERROR';
      case 'TimeoutError':
        return 'TIMEOUT';
      default:
        return `S3_ERROR_${error.name}`;
    }
  }

  private getS3ErrorSeverity(error: any): string {
    if (!error.name) return 'HIGH';

    // Critical infrastructure errors
    const criticalErrors = [
      'ServiceUnavailable',
      'InternalError',
      'NetworkingError',
      'TimeoutError',
    ];
    if (criticalErrors.includes(error.name)) return 'CRITICAL';

    // High priority errors
    const highPriorityErrors = ['NoSuchBucket', 'AccessDenied', 'InvalidBucketName'];
    if (highPriorityErrors.includes(error.name)) return 'HIGH';

    // Medium priority errors
    const mediumPriorityErrors = ['NoSuchKey', 'EntityTooLarge', 'SlowDown', 'RequestTimeout'];
    if (mediumPriorityErrors.includes(error.name)) return 'MEDIUM';

    return 'LOW';
  }

  private isS3RetryableError(error: any): boolean {
    if (!error.name) return false;

    const retryableErrors = [
      'ServiceUnavailable',
      'InternalError',
      'SlowDown',
      'RequestTimeout',
      'NetworkingError',
      'TimeoutError',
    ];

    return retryableErrors.includes(error.name);
  }
}
