import {
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ name: 'IsNotEmptyObject', async: false })
export class IsNotEmptyObject implements ValidatorConstraintInterface {
  validate(value: any, _args: ValidationArguments) {
    return value && typeof value === 'object' && Object.keys(value).length > 0;
  }

  defaultMessage(_args: ValidationArguments) {
    return 'filters is required';
  }
}
