import { Injectable } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';
import { randomUUID as uuidv4 } from 'crypto';

interface CorrelationContext {
  traceId: string; // Primary trace ID for logger library
  layer?: string;
  operation?: string;
}

@Injectable()
export class CorrelationContextService {
  private static asyncLocalStorage = new AsyncLocalStorage<CorrelationContext>();

  static run<T>(context: CorrelationContext, callback: () => T): T {
    return this.asyncLocalStorage.run(context, callback);
  }

  static getTraceId(): string | undefined {
    const store = this.asyncLocalStorage.getStore();
    return store?.traceId;
  }

  static getContext(): CorrelationContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  /**
   * Creates a new correlation context for background jobs/processes
   * This ensures background processes have proper trace IDs for logging
   */
  static createBackgroundContext(layer: string, operation?: string): CorrelationContext {
    const traceId = uuidv4();
    return {
      traceId,
      layer,
      operation,
    };
  }

  /**
   * Runs a background job/process with proper correlation context
   * This eliminates "not-trace-id-in-logger-wrapper" for background processes
   */
  static runBackgroundJob<T>(
    layer: string,
    operation: string,
    callback: () => T | Promise<T>,
  ): T | Promise<T> {
    const context = this.createBackgroundContext(layer, operation);
    return this.run(context, callback);
  }
}
