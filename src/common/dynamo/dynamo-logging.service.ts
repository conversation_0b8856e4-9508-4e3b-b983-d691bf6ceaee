import { Injectable } from '@nestjs/common';
import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  UpdateCommand,
  DeleteCommand,
  QueryCommand,
  ScanCommand,
} from '@aws-sdk/lib-dynamodb';
import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class DynamoLoggingService {
  constructor(private readonly dynamoClient: DynamoDBDocumentClient) {}

  async get(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB GetItem operation initiated', {
      traceId,
      operation: 'GetItem',
      tableName: params.TableName,
      key: this.sanitizeKey(params.Key),
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new GetCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB GetItem operation completed', {
        traceId,
        operation: 'GetItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        itemFound: !!result.Item,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB GetItem operation failed', {
        traceId,
        operation: 'GetItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      throw error;
    }
  }

  async put(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB PutItem operation initiated', {
      traceId,
      operation: 'PutItem',
      tableName: params.TableName,
      itemSize: JSON.stringify(params.Item).length,
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new PutCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB PutItem operation completed', {
        traceId,
        operation: 'PutItem',
        tableName: params.TableName,
        itemSize: JSON.stringify(params.Item).length,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB PutItem operation failed', {
        traceId,
        operation: 'PutItem',
        tableName: params.TableName,
        itemSize: JSON.stringify(params.Item).length,
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        errorType: this.getDynamoErrorType(error),
        severity: this.getDynamoErrorSeverity(error),
        retryable: this.isDynamoRetryableError(error),
        businessContext: this.getBusinessContext(),
        requestId: error.$metadata?.requestId,
        httpStatusCode: error.$metadata?.httpStatusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
        operationType: 'put_item_error',
      });

      throw error;
    }
  }

  async update(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB UpdateItem operation initiated', {
      traceId,
      operation: 'UpdateItem',
      tableName: params.TableName,
      key: this.sanitizeKey(params.Key),
      updateExpression: params.UpdateExpression,
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new UpdateCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB UpdateItem operation completed', {
        traceId,
        operation: 'UpdateItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB UpdateItem operation failed', {
        traceId,
        operation: 'UpdateItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      throw error;
    }
  }

  async delete(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB DeleteItem operation initiated', {
      traceId,
      operation: 'DeleteItem',
      tableName: params.TableName,
      key: this.sanitizeKey(params.Key),
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new DeleteCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB DeleteItem operation completed', {
        traceId,
        operation: 'DeleteItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB DeleteItem operation failed', {
        traceId,
        operation: 'DeleteItem',
        tableName: params.TableName,
        key: this.sanitizeKey(params.Key),
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      throw error;
    }
  }

  async query(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB Query operation initiated', {
      traceId,
      operation: 'Query',
      tableName: params.TableName,
      indexName: params.IndexName,
      keyConditionExpression: params.KeyConditionExpression,
      limit: params.Limit,
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new QueryCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB Query operation completed', {
        traceId,
        operation: 'Query',
        tableName: params.TableName,
        indexName: params.IndexName,
        itemsReturned: result.Items?.length || 0,
        scannedCount: result.ScannedCount,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB Query operation failed', {
        traceId,
        operation: 'Query',
        tableName: params.TableName,
        indexName: params.IndexName,
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      throw error;
    }
  }

  async scan(params: any): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('DynamoDB Scan operation initiated', {
      traceId,
      operation: 'Scan',
      tableName: params.TableName,
      indexName: params.IndexName,
      filterExpression: params.FilterExpression,
      limit: params.Limit,
      timestamp: new Date().toISOString(),
      layer: 'DYNAMODB',
    });

    try {
      const result = await this.dynamoClient.send(new ScanCommand(params));
      const duration = Date.now() - startTime;

      logger.info('DynamoDB Scan operation completed', {
        traceId,
        operation: 'Scan',
        tableName: params.TableName,
        indexName: params.IndexName,
        itemsReturned: result.Items?.length || 0,
        scannedCount: result.ScannedCount,
        duration: `${duration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      logger.error('DynamoDB Scan operation failed', {
        traceId,
        operation: 'Scan',
        tableName: params.TableName,
        indexName: params.IndexName,
        duration: `${duration}ms`,
        error: error.message,
        errorCode: error.name,
        timestamp: new Date().toISOString(),
        layer: 'DYNAMODB',
      });

      throw error;
    }
  }

  private getDynamoErrorType(error: any): string {
    if (!error.name) return 'UNKNOWN_ERROR';

    switch (error.name) {
      case 'ValidationException':
        return 'VALIDATION_ERROR';
      case 'ResourceNotFoundException':
        return 'RESOURCE_NOT_FOUND';
      case 'ConditionalCheckFailedException':
        return 'CONDITIONAL_CHECK_FAILED';
      case 'ProvisionedThroughputExceededException':
        return 'THROUGHPUT_EXCEEDED';
      case 'ThrottlingException':
        return 'THROTTLING';
      case 'ItemCollectionSizeLimitExceededException':
        return 'ITEM_SIZE_LIMIT_EXCEEDED';
      case 'LimitExceededException':
        return 'LIMIT_EXCEEDED';
      case 'InternalServerError':
        return 'INTERNAL_SERVER_ERROR';
      case 'ServiceUnavailableException':
        return 'SERVICE_UNAVAILABLE';
      case 'UnrecognizedClientException':
        return 'UNRECOGNIZED_CLIENT';
      case 'AccessDeniedException':
        return 'ACCESS_DENIED';
      case 'IncompleteSignatureException':
        return 'INCOMPLETE_SIGNATURE';
      case 'InvalidSignatureException':
        return 'INVALID_SIGNATURE';
      case 'TokenRefreshRequiredException':
        return 'TOKEN_REFRESH_REQUIRED';
      case 'NetworkingError':
        return 'NETWORK_ERROR';
      case 'TimeoutError':
        return 'TIMEOUT';
      default:
        return `DYNAMO_ERROR_${error.name}`;
    }
  }

  private getDynamoErrorSeverity(error: any): string {
    if (!error.name) return 'HIGH';

    // Critical infrastructure errors
    const criticalErrors = [
      'InternalServerError',
      'ServiceUnavailableException',
      'NetworkingError',
      'TimeoutError',
    ];
    if (criticalErrors.includes(error.name)) return 'CRITICAL';

    // High priority errors that affect functionality
    const highPriorityErrors = [
      'AccessDeniedException',
      'UnrecognizedClientException',
      'InvalidSignatureException',
      'IncompleteSignatureException',
    ];
    if (highPriorityErrors.includes(error.name)) return 'HIGH';

    // Medium priority errors (throttling, limits)
    const mediumPriorityErrors = [
      'ProvisionedThroughputExceededException',
      'ThrottlingException',
      'LimitExceededException',
      'ItemCollectionSizeLimitExceededException',
    ];
    if (mediumPriorityErrors.includes(error.name)) return 'MEDIUM';

    return 'LOW';
  }

  private isDynamoRetryableError(error: any): boolean {
    if (!error.name) return false;

    const retryableErrors = [
      'ProvisionedThroughputExceededException',
      'ThrottlingException',
      'InternalServerError',
      'ServiceUnavailableException',
      'NetworkingError',
      'TimeoutError',
    ];

    return retryableErrors.includes(error.name);
  }

  private getBusinessContext(): string {
    const context = CorrelationContextService.getContext();
    return context?.operation || 'unknown';
  }

  private sanitizeKey(key: any): any {
    if (!key) return key;

    // Create a copy and redact sensitive fields
    const sanitized = { ...key };
    const sensitiveFields = ['password', 'token', 'secret', 'key'];

    for (const field in sanitized) {
      if (sensitiveFields.some(sensitive => field.toLowerCase().includes(sensitive))) {
        sanitized[field] = '[REDACTED]';
      }
    }

    return sanitized;
  }
}
