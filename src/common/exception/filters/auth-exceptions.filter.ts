import { Catch, ExceptionFilter, ArgumentsHost, HttpStatus } from '@nestjs/common';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { AuthException, AuthExceptionStatus } from '@common/exception/types/AuthException';

@Catch(AuthException)
export class AuthExceptionFilter implements ExceptionFilter {
  catch(exception: AuthException, host: ArgumentsHost) {
    const mappedCode = (() => {
      switch (exception.status) {
        case AuthExceptionStatus.USER_NOT_FOUND:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.CREDENTIALS_NOT_FOUND:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.EMAIL_AND_PASSWORD_DO_NOT_MATCH:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.SUSPENDED_ACCESS:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.PENDING_CONFIRMATION:
          return HttpStatus.BAD_REQUEST;
        case AuthExceptionStatus.UNKNOWN_STATUS:
          return HttpStatus.INTERNAL_SERVER_ERROR;
        case AuthExceptionStatus.DELETED_ACCOUNT:
          return HttpStatus.BAD_REQUEST;
        case AuthExceptionStatus.ACCOUNT_NOT_FOUND:
          return HttpStatus.NOT_FOUND;
        case AuthExceptionStatus.ACCOUNT_INACTIVE:
          return HttpStatus.BAD_REQUEST;
        case AuthExceptionStatus.NO_TOKEN_IN_PROTECTED_ROUTE:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.INVALID_TOKEN_SIGNATURE:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.TOKEN_NOT_FOUND:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.TOKEN_IS_REVOKED:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.TOKEN_EXPIRED:
          return HttpStatus.UNAUTHORIZED;
        case AuthExceptionStatus.BLOCKED_ENDPOINT:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_ROUTE:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.UNDEFINED_ACCOUNT_ROLE_IN_USER:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.INVALID_ACCOUNT_ROLE:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.UNDEFINED_USER_ROLE_IN_ROUTE:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.USER_HAS_NO_ROLE_IN_ACCOUNT:
          return HttpStatus.FORBIDDEN;
        case AuthExceptionStatus.INVALID_USER_ROLE_IN_ACCOUNT:
          return HttpStatus.FORBIDDEN;
        default:
          return HttpStatus.INTERNAL_SERVER_ERROR;
      }
    })();

    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const status = mappedCode;
    const errorResponse = {
      message: exception.message,
      error: exception.stack,
    };

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || exception.message;

    logger.error('Authentication exception occurred', {
      traceId,
      message,
      origin: 'AuthException',
      path: request.url,
      method: request.method,
      statusCode: status,
      code: exception.code,
      error: exception.message,
      reason: exception.reason,
      errorType: 'AUTHENTICATION_ERROR',
      severity: 'HIGH',
      authExceptionStatus: exception.status,
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'AUTH_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: {
        path: request.url,
        traceId,
        ...(process.env.NODE_ENV !== 'production' && { errorStatus: exception.status }),
      },
    };

    return response.status(status).json(payload);
  }
}
