import { Catch, ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { APIError } from 'openai/error';
import { handleOpenAIErrors } from '@common/exception/handlers/handle-openai-errors.util';

@Catch(APIError)
export class OpenAIExceptionFilter implements ExceptionFilter {
  catch(exception: APIError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const openAIError = handleOpenAIErrors(exception);
    const status = openAIError.getStatus();
    const errorResponse = openAIError.getResponse();

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || openAIError.message;

    logger.error('OpenAI API exception occurred', {
      traceId,
      message,
      origin: 'OpenAIException',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: openAIError.message,
      errorType: 'OPENAI_API_ERROR',
      severity: 'HIGH',
      openAIErrorType: exception.type,
      openAIErrorCode: exception.code,
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'OPENAI_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: { path: request.url, traceId },
    };

    return response.status(status).json(payload);
  }
}
