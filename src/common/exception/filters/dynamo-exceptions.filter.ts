// dynamo.exception.filter.ts
import { Catch, ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { DynamoException } from '@common/exception/types/DynamoException';

@Catch(DynamoException)
export class DynamoExceptionFilter implements ExceptionFilter {
  catch(exception: DynamoException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || exception.message;

    logger.error('DynamoDB exception occurred', {
      traceId,
      message,
      origin: 'DynamoException',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: exception.message,
      errorType: 'DYNAMODB_ERROR',
      severity: 'HIGH',
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'DYNAMO_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: { path: request.url, traceId },
    };

    return response.status(status).json(payload);
  }
}
