import { Catch, ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { PrismaException } from '@common/exception/types/PrismaException';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';

@Catch(PrismaException)
export class PrismaExceptionFilter implements ExceptionFilter {
  catch(exception: PrismaException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || exception.message;

    logger.error('Prisma database exception occurred', {
      traceId,
      message,
      origin: 'PrismaException',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: exception.message,
      errorType: 'DATABASE_ERROR',
      severity: 'HIGH',
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'PRISMA_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: { path: request.url, traceId },
    };

    return response.status(status).json(payload);
  }
}
