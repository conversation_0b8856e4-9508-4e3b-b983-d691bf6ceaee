import { Catch, ExceptionFilter, ArgumentsHost } from '@nestjs/common';
import { BaseErrorResponse } from '@common/exception/types/BaseErrorResponse';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { WorkflowExecutionException } from '@common/exception/types/WorkflowExecutionException';

@Catch(WorkflowExecutionException)
export class WorkflowExceptionFilter implements ExceptionFilter {
  catch(exception: WorkflowExecutionException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();
    const traceId = CorrelationContextService.getTraceId();
    const context = CorrelationContextService.getContext();
    const status = exception.getStatus();
    const errorResponse = exception.getResponse();

    const message =
      typeof errorResponse === 'string'
        ? errorResponse
        : (errorResponse as any).message || exception.message;

    logger.error('Workflow execution exception occurred', {
      traceId,
      message,
      origin: 'WorkflowException',
      path: request.url,
      method: request.method,
      statusCode: status,
      error: exception.message,
      errorType: 'WORKFLOW_EXECUTION_ERROR',
      severity: 'HIGH',
      businessContext: context?.operation,
      timestamp: new Date().toISOString(),
      layer: 'WORKFLOW_EXCEPTION_FILTER',
    });

    const messages = Array.isArray(message) ? message : [message];

    const payload: BaseErrorResponse = {
      statusCode: status,
      timestamp: new Date().toISOString(),
      message: messages,
      data: { path: request.url, traceId },
    };

    return response.status(status).json(payload);
  }
}
