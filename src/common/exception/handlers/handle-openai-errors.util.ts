import { HttpStatus } from '@nestjs/common';
import { APIError } from 'openai/error';
import { OpenAIException } from '@common/exception/types/OpenAIException';

export function handleOpenAIErrors(error: APIError): OpenAIException {
  switch (error.type) {
    case 'RateLimitError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - O limite de requisições foi atingido.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );

    case 'BadRequestError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Requisição inválida.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'AuthenticationError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Falha na autenticação.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.UNAUTHORIZED,
      );

    case 'PermissionDeniedError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Permissão negada.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.FORBIDDEN,
      );

    case 'NotFoundError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Não encontrado.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'UnprocessableEntityError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Entidade não processável.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.UNPROCESSABLE_ENTITY,
      );

    case 'InternalServerError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Erro interno do servidor.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    case 'APIConnectionError':
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Erro na conexão com a API.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    default:
      return new OpenAIException(
        {
          message: `OpenAIException-${error.code} - Erro desconhecido.`,
          error: `Original error: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
  }
}
