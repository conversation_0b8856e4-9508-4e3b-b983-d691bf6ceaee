import { HttpStatus } from '@nestjs/common';
import { Prisma } from '@prisma/client';
import { PrismaException } from '@common/exception/types/PrismaException';

export function handlePrismaErrors(error: Prisma.PrismaClientKnownRequestError): PrismaException {
  switch (error.code) {
    case 'P2000':
      return new PrismaException(
        {
          message:
            'PrismaException' +
            error.code +
            ' - O valor fornecido para a coluna é muito longo para o tipo da coluna.',
          error: `Column: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2001':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - O registro procurado não existe.',
          error: `Original error: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2002':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Esse registro já existe',
          error: `Unique constraint failed on: ${error.meta.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2003':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha no cadastro das informações.',
          error: `Field: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2004':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Uma restrição não foi cumprida.',
          error: `Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2005':
      return new PrismaException(
        {
          message:
            'PrismaException' +
            error.code +
            ' - O valor fornecido não corresponde com o tipo de dado requerido.',
          error: `Field: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2006':
      return new PrismaException(
        {
          message:
            'PrismaException' + error.code + ' - O valor fornecido para o campo não é válido.',
          error: `Field: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2007':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro de validação de dados.',
          error: `Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2008':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha ao analisar a consulta.',
          error: `Query parsing error: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2009':
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha ao validar a consulta.',
          error: `Query validation error: ${error.meta?.target}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2010': // Raw query failed
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `Raw query failed. Code: ${error.meta?.code}. Message: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    case 'P2011': // Null constraint violation
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Null constraint violation on the ${error.meta?.constraint}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2012': // Missing a required value
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Missing a required value at ${error.meta?.path}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2013': // Missing required argument
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Missing the required argument ${error.meta?.argument_name} for field ${error.meta?.field_name} on ${error.meta?.object_name}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2014': // Required relation violation
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `The change you are trying to make would violate the required relation '${error.meta?.relation_name}' between the ${error.meta?.model_a_name} and ${error.meta?.model_b_name} models. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2015': // Related record not found
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `A related record could not be found. Details: ${error.meta?.details}. Original error: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2016': // Query interpretation error
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Query interpretation error. Details: ${error.meta?.details}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2017': // Records not connected
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro de consulta',
          error: `The records for relation ${error.meta?.relation_name} between the ${error.meta?.parent_name} and ${error.meta?.child_name} models are not connected. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2018': // Required connected records not found
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro de consulta',
          error: `The required connected records were not found. Details: ${error.meta?.details}. Original error: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2019': // Input error
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro de entrada',
          error: `Input error. Details: ${error.meta?.details}. Original error: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2020': // Value out of range for the type
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Valor fora do intervalo',
          error: `Value out of range: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2021': // The table does not exist in the current database
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - O recurso não existe',
          error: `Table does not exist: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2022': // The column does not exist in the current database
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - O recurso não existe',
          error: `Column does not exist: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2023': // Inconsistent column data
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - O recurso não existe',
          error: `Inconsistent column data: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2024': // Timed out fetching a new connection from the connection pool
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro de conexão',
          error: `Connection pool timeout: ${error.message}`,
        },
        HttpStatus.GATEWAY_TIMEOUT,
      );

    case 'P2025': // Operation failed due to missing required records
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Operação inválida',
          error: `Required records not found: ${error.message}`,
        },
        HttpStatus.NOT_FOUND,
      );

    case 'P2026': // Database provider doesn't support a feature used in the query
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Unsupported feature: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2027': // Multiple errors occurred
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Erro na consulta',
          error: `Multiple errors occurred on the database: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    case 'P2028': // Transaction API error
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `Transaction API error: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    case 'P2030': // No fulltext index found
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `Cannot find a fulltext index: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2031': // MongoDB replica set required
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `MongoDB replica set required: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    case 'P2033': // Number does not fit in a 64-bit signed integer
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `Number too large for 64-bit signed integer: ${error.message}`,
        },
        HttpStatus.BAD_REQUEST,
      );

    case 'P2034': // Transaction failed
      return new PrismaException(
        {
          message: 'PrismaException' + error.code + ' - Falha na consulta',
          error: `Transaction failed: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );

    default:
      return new PrismaException(
        {
          message: 'PrismaException - Internal Server Error',
          error: `Unexpected database error. Original error: ${error.message}`,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
  }
}
