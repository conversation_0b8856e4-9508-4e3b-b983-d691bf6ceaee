export enum AuthExceptionStatus {
  EMAIL_AND_PASSWORD_DO_NOT_MATCH,
  USER_NOT_FOUND,
  CREDENTIALS_NOT_FOUND,
  SUSPENDED_ACCESS,
  PENDING_CONFIRMATION,
  UNKNOWN_STATUS,
  DELETED_ACCOUNT,
  ACCOUNT_NOT_FOUND,
  ACCOUNT_INACTIVE,
  NO_TOKEN_IN_PROTECTED_ROUTE,
  INVALID_TOKEN_SIGNATURE,
  TOKEN_NOT_FOUND,
  TOKEN_IS_REVOKED,
  TOKEN_EXPIRED,
  BLOCKED_ENDPOINT,
  UNDEFINED_ACCOUNT_ROLE_IN_ROUTE,
  UNDEFINED_ACCOUNT_ROLE_IN_USER,
  INVALID_ACCOUNT_ROLE,
  UNDEFINED_USER_ROLE_IN_ROUTE,
  USER_HAS_NO_ROLE_IN_ACCOUNT,
  INVALID_USER_ROLE_IN_ACCOUNT,
  INVALID_TOKEN,
}
export class AuthException implements Error {
  constructor(
    name: string,
    message: string,
    status: AuthExceptionStatus,
    code: string,
    reason?: string,
    stack?: string,
  ) {
    this.message = message;
    this.name = name;
    this.status = status;
    this.code = code;
    this.reason = reason;
    this.stack = stack;
  }

  name: string;
  message: string;
  code: string;
  stack?: string;
  reason?: string;
  status: AuthExceptionStatus;
}
