export enum BusinessExceptionStatus {
  ITEM_NOT_FOUND,
  GENERAL_ERROR,
  INVALID_INPUT,
}
export class BusinessException implements Error {
  constructor(name: string, message: string, status: BusinessExceptionStatus, stack?: string) {
    this.message = message;
    this.name = name;
    this.status = status;
    this.stack = stack;
  }

  name: string;
  message: string;
  stack?: string;
  status: BusinessExceptionStatus;
}
