import { CommunicationChannel, MessageType, RecordStatus } from '@common/enums';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class IncomingMessageEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly from: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsEnum(MessageType)
  readonly messageType: MessageType;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsEnum(CommunicationChannel)
  readonly channel: CommunicationChannel;

  @IsString()
  @IsOptional()
  readonly fileUrl?: string;

  @IsBoolean()
  @IsNotEmpty()
  read: boolean;

  @IsDate()
  @IsNotEmpty()
  readAt: Date;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    id: string,
    from: string,
    to: string,
    messageType: MessageType,
    channel: CommunicationChannel,
    message?: string,
    fileUrl?: string,
    read?: boolean,
    readAt?: Date,
    status?: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.from = from;
    this.to = to;
    this.messageType = messageType;
    this.message = message;
    this.channel = channel;
    this.fileUrl = fileUrl;
    this.read = read;
    this.readAt = readAt;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
