import { IsDate, IsNotEmpty, IsUUID } from 'class-validator';

export class CustomerChannelIntegrationDataEntity {
  @IsUUID('4')
  readonly id: string;

  @IsNotEmpty()
  readonly integrationData: any;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(id: string, integrationData: any, createdAt?: Date, updatedAt?: Date) {
    this.id = id;
    this.integrationData = integrationData;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
