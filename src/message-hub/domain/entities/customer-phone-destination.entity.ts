import { CommunicationChannel, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CustomerPhoneDestinationEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsString()
  @IsNotEmpty()
  readonly destination: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    customerId: string,
    phoneNumber: string,
    destination: string,
    communicationChannel: CommunicationChannel,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.destination = destination;
    this.communicationChannel = communicationChannel;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
