import { CommunicationChannel, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class CustomerPhoneEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsString()
  @IsNotEmpty()
  readonly apiUrl?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsString()
  @IsNotEmpty()
  readonly incomingCron: string;

  @IsString()
  @IsNotEmpty()
  readonly outgoingCron: string;

  @IsNumber()
  @IsNotEmpty()
  readonly outgoingMaxDelay: number;

  @IsNumber()
  @IsNotEmpty()
  readonly dailyLimit: number;

  @IsNumber()
  @IsNotEmpty()
  readonly weight: number;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
    incomingCron: string,
    outgoingCron: string,
    outgoingMaxDelay: number,
    dailyLimit: number,
    weight: number,
    apiUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.communicationChannel = communicationChannel;
    this.incomingCron = incomingCron;
    this.outgoingCron = outgoingCron;
    this.outgoingMaxDelay = outgoingMaxDelay;
    this.dailyLimit = dailyLimit;
    this.weight = weight;
    this.apiUrl = apiUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
