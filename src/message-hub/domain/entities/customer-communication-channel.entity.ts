import { CommunicationChannel, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class CustomerCommunicationChannelEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId?: string;

  @IsEnum(CommunicationChannel)
  channel: CommunicationChannel;

  @IsUUID('4')
  @IsNotEmpty()
  readonly integrationDataId: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(
    id: string,
    customerId: string,
    portfolioId: string,
    channel: CommunicationChannel,
    integrationDataId: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioId = portfolioId;
    this.customerId = customerId;
    this.channel = channel;
    this.integrationDataId = integrationDataId;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
