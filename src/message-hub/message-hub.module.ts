import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { Module } from '@nestjs/common';
import { MessageController } from '@message-hub/application/controllers/message.controller';
import { MessageUseCase } from '@message-hub/application/use-cases/message.use-case';
import { SQSService } from '@common/sqs/sqs.service';
import { CustomerCommunicationChannelAdapter } from '@message-hub/infrastructure/adapters/db/customer-communication-channel.adapter';
import { CustomerCommunicationChannelUseCase } from '@message-hub/application/use-cases/customer-communication-channel.use-case';
import { CustomerCommunicationChannelController } from '@message-hub/application/controllers/customer-communication-channel.controller';
import { CustomerCommunicationChannelIntegrationDataAdapter } from '@message-hub/infrastructure/adapters/db/customer-channel-integration-data.adapter';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { OutgoingMessageAdapter } from '@message-hub/infrastructure/adapters/db/outgoing-message.adapter';
import { SQSConsumerService } from '@message-hub/application/services/sqs-consumers.service';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { InfraWhatsAppSelfhostedAdapter } from '@message-hub/infrastructure/adapters/http/whatsapp-serfhosted.adapter';
import { InfraBusinessBaseAdapter } from '@message-hub/infrastructure/adapters/http/business-base.adapter';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { NumberService } from '@common/utils/number-service';
import { IncomingMessageAdapter } from '@message-hub/infrastructure/adapters/db/incoming-message.adapter';
import { CustomerPhoneAdapter } from '@message-hub/infrastructure/adapters/db/customer-phone.adapter';
import { CustomerPhoneUseCase } from '@message-hub/application/use-cases/customer-phone.use-case';
import { CustomerPhoneController } from '@message-hub/application/controllers/customer-phone.controller';
import { CustomerPhoneDestinationAdapter } from '@message-hub/infrastructure/adapters/db/customer-phone-destination.adapter';
import { SmsController } from '@message-hub/application/controllers/sms.controller';
import { VonageIntegrationAdapter } from '@message-hub/infrastructure/adapters/http/vonage.adapter';
import { PhoneController } from '@message-hub/application/controllers/phone.controller';
import { IncomingMessagesReadService } from '@message-hub/application/services/incoming-messages-read.service';
import { S3Service } from '@common/s3/s3.service';
import { OutgoingMessagesSendService } from '@message-hub/application/services/outgoing-messages-send.service';
import { MetricsController } from '@message-hub/application/controllers/metric.controller';
import { MetricUseCase } from '@message-hub/application/use-cases/metric.use-case';
import { CustomerChannelIntegrationDataDefinitionAdapter } from '@common/auth/db/adapters/customer-channel-integration-data-definition.adapter';
import { PhonesJobService } from '@message-hub/application/services/phones-job.service';
import { SlackMessage } from '@edutalent/commons-sdk';
import { InfraLovelaceAdapter } from '@message-hub/infrastructure/adapters/http/lovelace.adapter';
import { OutgoingMessageController } from '@message-hub/application/controllers/outgoing-message.controller';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 50000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [httpModule, ScheduleModule.forRoot()],
  providers: [
    { provide: 'CustomerCommunicationChannelPort', useClass: CustomerCommunicationChannelAdapter },
    {
      provide: 'CustomerCommunicationChannelIntegrationDataPort',
      useClass: CustomerCommunicationChannelIntegrationDataAdapter,
    },
    {
      provide: 'CustomerPhonePort',
      useClass: CustomerPhoneAdapter,
    },
    {
      provide: 'CustomerPhoneDestinationPort',
      useClass: CustomerPhoneDestinationAdapter,
    },
    {
      provide: 'OutgoingMessagePort',
      useClass: OutgoingMessageAdapter,
    },
    {
      provide: 'IncomingMessagePort',
      useClass: IncomingMessageAdapter,
    },
    {
      provide: 'InfraWhatsappSelfhostedPort',
      useClass: InfraWhatsAppSelfhostedAdapter,
    },
    {
      provide: 'InfraLovelacePort',
      useClass: InfraLovelaceAdapter,
    },
    {
      provide: 'InfraBusinessBasePort',
      useClass: InfraBusinessBaseAdapter,
    },
    {
      provide: 'SmsServiceProviderPort',
      useClass: VonageIntegrationAdapter,
    },
    {
      provide: 'CustomerChannelIntegrationDataDefinitionPort',
      useClass: CustomerChannelIntegrationDataDefinitionAdapter,
    },
    S3Service,
    SQSService,
    DynamoService,
    SQSConsumerService,
    SlackMessage,
    IncomingMessagesReadService,
    OutgoingMessagesSendService,
    PhonesJobService,
    NumberService,
    MessageUseCase,
    CustomerCommunicationChannelUseCase,
    CustomerPhoneUseCase,
    OutgoingMessageUseCase,
    IncomingMessageUseCase,
    MetricUseCase,
  ],
  controllers: [
    MessageController,
    CustomerCommunicationChannelController,
    CustomerPhoneController,
    PhoneController,
    SmsController,
    MetricsController,
    OutgoingMessageController,
  ],
})
export class MessageHubModule {}
