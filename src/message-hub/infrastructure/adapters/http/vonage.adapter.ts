import { logger } from '@edutalent/commons-sdk';
import { SmsServiceProviderPort } from '@message-hub/infrastructure/ports/http/sms-service-provider.port';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';

@Injectable()
export class VonageIntegrationAdapter implements SmsServiceProviderPort {
  constructor(private readonly httpService: HttpService) {}

  async sendSms(from: string, to: string, text: string, apiUrl: string): Promise<any> {
    try {
      logger.info(
        `VonageIntegrationAdapter - send message to ${to} from ${from} with text: ${text}`,
      );

      const payload = {
        api_key: process.env.VONAGE_API_KEY.toString(),
        api_secret: process.env.VONAGE_API_SECRET.toString(),
        to: to,
        from: from,
        text: text,
        type: 'unicode',
      };

      const headers = {
        'Content-Type': 'application/json',
      };

      const response = await lastValue<PERSON>rom(this.httpService.post(apiUrl, payload, { headers }));

      return response;
    } catch (error) {
      logger.error('VonageIntegrationAdapter - error: ' + JSON.stringify(error));
      throw new Error(`Failed to send SMS: ${error.message}`);
    }
  }
}
