import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { InfraWhatsappSelfhostedPort } from '@message-hub/infrastructure/ports/http/whatsapp-selfhosted.port';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { S3Service } from '@common/s3/s3.service';
import { Readable } from 'node:stream';
import { MessageType } from '@common/enums';

@Injectable()
export class InfraWhatsAppSelfhostedAdapter implements InfraWhatsappSelfhostedPort {
  private readonly directMessageFilesBucketName;

  constructor(
    private readonly httpService: HttpService,
    private readonly s3Service: S3Service,
  ) {
    this.directMessageFilesBucketName = process.env.DIRECT_MESSAGE_FILES_BUCKET;
  }

  async sendMessage(to: string, message: string, apiUrl: string): Promise<void> {
    try {
      const url = `${apiUrl}/api/v1/messages/send-text`;
      logger.info(
        `Posting data to ${url} to send message to whatsapp selfhosted. To: ${to} message: ${message}`,
      );
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };
      const sendMessageDto = {
        phone: to,
        message: message,
      };

      const whatsAppSelfHostedResponse = await lastValueFrom(
        this.httpService.post(url, sendMessageDto, { headers }),
      );

      if (whatsAppSelfHostedResponse?.data?.status !== 200) {
        throw new Error('whatsapp selfhosted error status code receveid');
      }
    } catch (error) {
      logger.error(`Error sending message to whatsapp selfhosted: ${JSON.stringify(error)}`);
      handleHttpError(error, 'Infra-WhatsAppSelfhosted-adapter');
    }
  }

  async sendMessageWithFile(
    to: string,
    message: string,
    apiUrl: string,
    fileUrl: string,
    fileType: string,
  ): Promise<void> {
    try {
      if (fileType == MessageType.PDF) {
        const url = `${apiUrl}/api/v1/messages/send-file`;
        logger.info(
          `Posting data to ${url} to send message to whatsapp selfhosted. To: ${to} message: ${message}`,
        );

        const splitedFileKey = fileUrl.split('/');
        const fileKey = splitedFileKey[splitedFileKey.length - 1];
        const fileStream = await Promise.resolve(
          this.s3Service.getFileStream(this.directMessageFilesBucketName, fileKey),
        );

        if (!(fileStream instanceof Readable)) {
          throw new Error('File stream is not a readable stream');
        }

        const chunks: Buffer[] = [];
        for await (const chunk of fileStream) {
          chunks.push(chunk);
        }
        const blob = new Blob(chunks);

        const form = new FormData();
        form.append('phone', to);
        form.append('file', blob);
        form.append('filename', fileKey);
        form.append('mimetype', fileType);
        form.append('caption', message);

        const whatsAppSelfHostedResponse = await lastValueFrom(this.httpService.post(url, form));

        if (whatsAppSelfHostedResponse?.data?.status !== 200) {
          throw new Error(
            `whatsapp selfhosted error status code receveid: ${JSON.stringify(
              whatsAppSelfHostedResponse,
            )}`,
          );
        }
      } else if (fileType == MessageType.AUDIO) {
        const url = `${apiUrl}/api/v1/messages/send-audio`;
        const fileStream = await Promise.resolve(this.s3Service.getFileStreamFromPath(fileUrl));

        if (!(fileStream instanceof Readable)) {
          throw new Error(`File stream is not a readable stream: ${fileUrl}`);
        }

        const audioBuffer = await this.streamToAudioBuffer(fileStream);

        const sendMessageDto = {
          phone: to,
          audio: audioBuffer,
        };

        const whatsAppSelfHostedResponse = await lastValueFrom(
          this.httpService.post(url, sendMessageDto),
        );

        if (whatsAppSelfHostedResponse?.data?.status !== 200) {
          throw new Error(
            `whatsapp selfhosted error status code receveid: ${JSON.stringify(
              whatsAppSelfHostedResponse,
            )}`,
          );
        }
      }
    } catch (error) {
      logger.debug(
        `Error sending message with file to whatsapp selfhosted: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-WhatsAppSelfhosted-adapter: sendMessageWithFile');
    }
  }

  private async streamToAudioBuffer(stream: Readable): Promise<Buffer> {
    const chunks: Buffer[] = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }

    return Buffer.concat(chunks);
  }
}
