import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { InfraBusinessBasePort } from '@message-hub/infrastructure/ports/http/business-base.port';
import { CommunicationChannel, MessageType } from '@common/enums';

@Injectable()
export class InfraBusinessBaseAdapter implements InfraBusinessBasePort {
  private readonly businessBaseServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.businessBaseServiceUrl = process.env.BUSINESS_BASE_SERVICE_URL.toString();
  }

  async executeItem(
    from: string,
    to: string,
    message: string,
    messageType: MessageType,
    channel: CommunicationChannel,
    filesUrl: string[],
  ): Promise<void> {
    const executePortfolioItemDto = {
      from,
      to,
      message,
      messageType,
      channel,
      filesUrl,
    };

    logger.info(`Executing item in business base: ${JSON.stringify(executePortfolioItemDto)}`);

    try {
      const url = `${this.businessBaseServiceUrl}/api/v1/business-base/portfolio-items/execute`;
      logger.info(`Posting data to: ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      await lastValueFrom(
        this.httpService.post(url, executePortfolioItemDto, { headers, timeout: 29000 }),
      );
    } catch (error) {
      logger.error(
        `Error executing item in business base: ${JSON.stringify(
          executePortfolioItemDto,
        )}Error: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-BusinessBase-adapter');
    }
  }
}
