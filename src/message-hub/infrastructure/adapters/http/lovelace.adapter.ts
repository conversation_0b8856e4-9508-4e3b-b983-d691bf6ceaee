import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';

import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { InfraLovelacePort } from '@message-hub/infrastructure/ports/http/lovelace.port';

@Injectable()
export class InfraLovelaceAdapter implements InfraLovelacePort {
  constructor(private readonly httpService: HttpService) {}

  async sendMessage(from: string, to: string, message: string, apiUrl: string): Promise<void> {
    const url = `${apiUrl}/api/v1/survey/whatsapp/messages/outbound`;

    const headers = {
      'Content-Type': 'application/json',
    };

    const sendMessageDto = {
      from: from,
      to: to,
      message: message,
      messageType: 'TEXT',
    };

    logger.info(
      `InfraLovelaceAdapter - sending message to lovelace - url: ${url} - request body: ${JSON.stringify(
        sendMessageDto,
      )}`,
    );

    try {
      const lovalaceResponse = await lastValueFrom(
        this.httpService.post(url, sendMessageDto, { headers }),
      );

      if (lovalaceResponse.status !== 201) {
        logger.error(
          `InfraLovelaceAdapter - Lovelace error status code receveid - status: ${lovalaceResponse.status} - statusText: ${lovalaceResponse.statusText}`,
        );

        throw new Error(
          `InfraLovelaceAdapter - Lovelace error status code receveid - status: ${lovalaceResponse.status} - statusText: ${lovalaceResponse.statusText}`,
        );
      }
    } catch (error) {
      logger.error(
        `InfraLovelaceAdapter - Error sending message to lovelace: ${JSON.stringify(error)}`,
      );
      handleHttpError(error, 'Infra-Lovelace-adapter');
    }
  }
}
