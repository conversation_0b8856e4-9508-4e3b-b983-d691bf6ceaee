import { Injectable } from '@nestjs/common';
import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  DeleteCommand,
} from '@aws-sdk/lib-dynamodb';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import { CustomerCommunicationChannelIntegrationDataPort } from '@message-hub/infrastructure/ports/db/customer-communication-channel-integration-data.port';
import { CustomerChannelIntegrationDataEntity } from '@message-hub/domain/entities/customer-channel-integration-data.entity';

@Injectable()
export class CustomerCommunicationChannelIntegrationDataAdapter
  implements CustomerCommunicationChannelIntegrationDataPort
{
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_customer_channel_integration_data';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async getById(id: string): Promise<CustomerChannelIntegrationDataEntity> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            id,
          },
        }),
      );
      return Item.integrationData as CustomerChannelIntegrationDataEntity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while fetching customer channel integration data for id: ${id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async create(
    entity: CustomerChannelIntegrationDataEntity,
  ): Promise<CustomerChannelIntegrationDataEntity> {
    try {
      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            integrationData: entity.integrationData,
          },
        }),
      );
      return entity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while saving customer channel integration data for id: ${
          entity.id
        }. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async deleteById(id: string): Promise<void> {
    try {
      await this.dynamoClient.send(
        new DeleteCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            id,
          },
        }),
      );
    } catch (error) {
      throw new DynamoException({
        message: `Error while deleting customer channel integration data for id: ${id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }
}
