import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CommunicationChannel, RecordStatus } from '@common/enums';
import { CustomerCommunicationChannelEntity } from '@message-hub/domain/entities/customer-communication-channel.entity';
import { CustomerCommunicationChannelPort } from '@message-hub/infrastructure/ports/db/customer-communication-channel.port';

@Injectable()
export class CustomerCommunicationChannelAdapter
  extends PrismaCommonAdapter<CustomerCommunicationChannelEntity>
  implements CustomerCommunicationChannelPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'customerCommunicationChannel');
  }

  async getAllByCustomerId(customerId: string): Promise<CustomerCommunicationChannelEntity[]> {
    const communicationChannels = await this.prisma.client.customerCommunicationChannel.findMany({
      where: { customerId: customerId },
    });

    return communicationChannels.map(communicationChannel => ({
      ...communicationChannel,
      channel: communicationChannel.channel as CommunicationChannel,
      status: communicationChannel.status as RecordStatus,
    }));
  }

  async deleteAllByCustomerId(customerId: string): Promise<void> {
    await this.prismaClient.customerCommunicationChannel.updateMany({
      where: { customerId: customerId },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteByCustomerIdAndPortfolioId(customerId: string, portfolioId: string): Promise<void> {
    await this.prismaClient.customerCommunicationChannel.updateMany({
      where: { customerId: customerId, portfolioId: portfolioId },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteByCustomerIdAndPortfolioIdAndChannel(
    customerId: string,
    portfolioId: string,
    channel: CommunicationChannel,
  ): Promise<void> {
    await this.prismaClient.customerCommunicationChannel.updateMany({
      where: { customerId: customerId, portfolioId: portfolioId, channel: channel },
      data: { status: RecordStatus.DELETED },
    });
  }
}
