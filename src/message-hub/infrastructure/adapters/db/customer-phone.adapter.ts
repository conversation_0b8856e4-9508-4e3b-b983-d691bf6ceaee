import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CommunicationChannel, RecordStatus } from '@common/enums';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';

@Injectable()
export class CustomerPhoneAdapter
  extends PrismaCommonAdapter<CustomerPhoneEntity>
  implements CustomerPhonePort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'customerPhone');
  }

  async getByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneEntity> {
    const phone = await this.prisma.client.customerPhone.findFirst({
      where: { phoneNumber, communicationChannel, status: RecordStatus.ACTIVE },
    });

    if (!phone) {
      return null;
    }

    return {
      ...phone,
      communicationChannel: phone.communicationChannel as CommunicationChannel,
      status: phone.status as RecordStatus,
      outgoingMaxDelay: Number(phone.outgoingMaxDelay),
      weight: Number(phone.weight),
    };
  }

  async getByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneEntity[]> {
    const phones = await this.prisma.client.customerPhone.findMany({
      where: { customerId, phoneNumber },
    });

    return phones.map(phone => ({
      ...phone,
      communicationChannel: phone.communicationChannel as CommunicationChannel,
      status: phone.status as RecordStatus,
      outgoingMaxDelay: Number(phone.outgoingMaxDelay),
      weight: Number(phone.weight),
    }));
  }

  async getByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneEntity> {
    const customerPhone = await this.prisma.client.customerPhone.findUnique({
      where: {
        customer_phones_pkey: { customerId, phoneNumber, communicationChannel },
      },
    });

    return {
      ...customerPhone,
      communicationChannel: customerPhone.communicationChannel as CommunicationChannel,
      status: customerPhone.status as RecordStatus,
      outgoingMaxDelay: Number(customerPhone.outgoingMaxDelay),
      weight: Number(customerPhone.weight),
    };
  }

  async update(entity: CustomerPhoneEntity): Promise<CustomerPhoneEntity> {
    const updatedAtEntity = {
      ...entity,
      updatedAt: new Date(),
    };

    const updatedEntity = await this.prisma.client.customerPhone.update({
      where: {
        customer_phones_pkey: {
          customerId: updatedAtEntity.customerId,
          phoneNumber: updatedAtEntity.phoneNumber,
          communicationChannel: updatedAtEntity.communicationChannel,
        },
      },
      data: updatedAtEntity,
    });

    return {
      ...updatedEntity,
      communicationChannel: updatedEntity.communicationChannel as CommunicationChannel,
      status: updatedEntity.status as RecordStatus,
      outgoingMaxDelay: Number(updatedEntity.outgoingMaxDelay),
      weight: Number(updatedEntity.weight),
    };
  }

  async deleteAllByCustomerId(customerId: string): Promise<void> {
    await this.prismaClient.customerPhone.updateMany({
      where: { customerId: customerId },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteByCustomerIdAndPhoneNumber(customerId: string, phoneNumber: string): Promise<void> {
    await this.prismaClient.customerPhone.updateMany({
      where: { customerId: customerId, phoneNumber: phoneNumber },
      data: { status: RecordStatus.DELETED },
    });
  }

  async deleteByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    await this.prismaClient.customerPhone.updateMany({
      where: {
        customerId: customerId,
        phoneNumber: phoneNumber,
        communicationChannel: communicationChannel,
      },
      data: { status: RecordStatus.DELETED },
    });
  }
}
