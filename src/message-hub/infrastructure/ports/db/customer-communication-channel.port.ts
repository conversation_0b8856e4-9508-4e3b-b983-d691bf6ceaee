import { DbCommonPort } from '@common/db/ports/common.port';
import { CustomerCommunicationChannelEntity } from '@message-hub/domain/entities/customer-communication-channel.entity';

export interface CustomerCommunicationChannelPort
  extends DbCommonPort<CustomerCommunicationChannelEntity> {
  getAllByCustomerId(customerId: string): Promise<CustomerCommunicationChannelEntity[]>;

  deleteAllByCustomerId(customerId: string): Promise<void>;

  deleteByCustomerIdAndPortfolioId(customerId: string, portfolioId: string): Promise<void>;

  deleteByCustomerIdAndPortfolioIdAndChannel(
    customerId: string,
    portfolioId: string,
    channel: string,
  ): Promise<void>;
}
