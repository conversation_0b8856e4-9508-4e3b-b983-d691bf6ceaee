import { CustomerChannelIntegrationDataEntity } from '@message-hub/domain/entities/customer-channel-integration-data.entity';

export interface CustomerCommunicationChannelIntegrationDataPort {
  getById(id: string): Promise<CustomerChannelIntegrationDataEntity>;

  deleteById(id: string): Promise<void>;

  create(
    entity: CustomerChannelIntegrationDataEntity,
  ): Promise<CustomerChannelIntegrationDataEntity>;
}
