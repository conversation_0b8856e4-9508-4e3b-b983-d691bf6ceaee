import { DbCommonPort } from '@common/db/ports/common.port';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { CommunicationChannel } from '@common/enums';

export interface CustomerPhonePort extends DbCommonPort<CustomerPhoneEntity> {
  getByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneEntity[]>;

  getByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneEntity>;

  getByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneEntity>;

  deleteAllByCustomerId(customerId: string): Promise<void>;

  update(entity: CustomerPhoneEntity): Promise<CustomerPhoneEntity>;

  deleteByCustomerIdAndPhoneNumber(customerId: string, phoneNumber: string): Promise<void>;

  deleteByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void>;
}
