import { CommunicationChannel } from '@common/enums';
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class CustomerCommunicationChannelResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  readonly portfolioId?: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsUUID('4')
  readonly integrationDataId?: string;

  readonly integrationData?: any;

  readonly createdAt?: Date;

  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    portfolioId: string,
    communicationChannel: CommunicationChannel,
    integrationDataId?: string,
    integrationData?: any,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.portfolioId = portfolioId;
    this.communicationChannel = communicationChannel;
    this.integrationDataId = integrationDataId;
    this.integrationData = integrationData;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
