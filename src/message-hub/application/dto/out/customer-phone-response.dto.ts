import { IsEnum, <PERSON>NotEmpty, <PERSON>N<PERSON><PERSON>, IsString, IsUUID } from 'class-validator';
import { CommunicationChannel } from '@common/enums';

export class CustomerPhoneResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsString()
  @IsNotEmpty()
  readonly incomingCron: string;

  @IsString()
  @IsNotEmpty()
  readonly outgoingCron: string;

  @IsNumber()
  @IsNotEmpty()
  readonly outgoingMaxDelay: number;

  @IsNumber()
  @IsNotEmpty()
  readonly dailyLimit: number;

  @IsString()
  readonly apiUrl?: string;

  readonly createdAt?: Date;

  readonly updatedAt?: Date;

  constructor(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
    incomingCron: string,
    outgoingCron: string,
    outgoingMaxDelay: number,
    apiUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.communicationChannel = communicationChannel;
    this.incomingCron = incomingCron;
    this.outgoingCron = outgoingCron;
    this.outgoingMaxDelay = outgoingMaxDelay;
    this.apiUrl = apiUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
