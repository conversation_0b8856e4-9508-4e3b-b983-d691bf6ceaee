import { CommunicationChannel, MessageType } from '@common/enums';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsDate } from 'class-validator';

export class RetrievePendingMessageResponseDto {
  @IsString()
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly from: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsString()
  @IsNotEmpty()
  readonly message: string;

  @IsDate()
  @IsNotEmpty()
  readonly timeToGo: Date;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly channel: CommunicationChannel;

  @IsString()
  @IsNotEmpty()
  readonly apiUrl: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsString()
  @IsOptional()
  readonly fileUrl?: string;

  constructor(
    id: string,
    from: string,
    to: string,
    message: string,
    timeToGo: Date,
    channel: CommunicationChannel,
    apiUrl: string,
    messageType: MessageType,
    fileUrl?: string,
  ) {
    this.id = id;
    this.from = from;
    this.to = to;
    this.message = message;
    this.timeToGo = timeToGo;
    this.channel = channel;
    this.apiUrl = apiUrl;
    this.messageType = messageType;
    this.fileUrl = fileUrl;
  }
}
