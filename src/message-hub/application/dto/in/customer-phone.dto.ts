import { IsEnum, <PERSON>NotEmpty, IsN<PERSON>ber, IsString } from 'class-validator';
import { CommunicationChannel } from '@common/enums';

export class CustomerPhoneDto {
  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsString()
  readonly apiUrl: string;

  @IsString()
  @IsNotEmpty()
  readonly incomingCron: string;

  @IsString()
  @IsNotEmpty()
  readonly outgoingCron: string;

  @IsNumber()
  @IsNotEmpty()
  readonly outgoingMaxDelay: number;

  @IsNumber()
  @IsNotEmpty()
  readonly dailyLimit: number;

  @IsNumber()
  @IsNotEmpty()
  readonly weight: number;

  constructor(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
    apiUrl: string,
    incomingCron: string,
    outgoingCron: string,
    outgoingMaxDelay: number,
    weight: number,
  ) {
    this.phoneNumber = phoneNumber;
    this.communicationChannel = communicationChannel;
    this.apiUrl = apiUrl;
    this.incomingCron = incomingCron;
    this.outgoingCron = outgoingCron;
    this.outgoingMaxDelay = outgoingMaxDelay;
    this.weight = weight;
  }
}
