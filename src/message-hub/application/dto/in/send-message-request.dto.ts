import { CommunicationChannel, MessageType } from '@common/enums';
import { IsBoolean, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class SendMessageRequestDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsEnum(MessageType)
  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsBoolean()
  @IsNotEmpty()
  readonly isFirstMessage: boolean;

  @IsOptional()
  @IsString()
  readonly fileUrl?: string;

  constructor(
    customerId: string,
    to: string,
    messageType: MessageType,
    communicationChannel: CommunicationChannel,
    isFirstMessage: boolean,
    message: string,
    fileUrl?: string,
  ) {
    this.customerId = customerId;
    this.to = to;
    this.messageType = messageType;
    this.message = message;
    this.communicationChannel = communicationChannel;
    this.isFirstMessage = isFirstMessage;
    this.fileUrl = fileUrl;
  }
}
