import { CommunicationChannel } from '@common/enums';
import { IsEnum, IsNotEmpty, IsObject, IsUUID } from 'class-validator';

export class CustomerCommunicationChannelDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  readonly portfolioId?: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsObject()
  readonly integrationData?: any;

  constructor(
    customerId: string,
    portfolioId: string,
    communicationChannel: CommunicationChannel,
    integrationData?: any,
  ) {
    this.customerId = customerId;
    this.portfolioId = portfolioId;
    this.communicationChannel = communicationChannel;
    this.integrationData = integrationData;
  }
}
