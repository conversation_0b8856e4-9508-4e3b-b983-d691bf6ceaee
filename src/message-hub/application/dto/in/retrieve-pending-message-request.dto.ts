import { CommunicationChannel } from '@common/enums';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';

export class RetrievePendingMessageRequestDto {
  @IsString()
  @IsNotEmpty()
  readonly from: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  constructor(
    from: string,
    to: string,
    communicationChannel: CommunicationChannel = CommunicationChannel.WHATSAPPSELFHOSTED,
  ) {
    this.from = from;
    this.to = to;
    this.communicationChannel = communicationChannel;
  }
}
