import { CommunicationChannel, MessageType } from '@common/enums';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class IncomingMessageRequestDto {
  @IsString()
  @IsNotEmpty()
  readonly from: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsOptional()
  readonly fileBuffer?: Buffer;

  constructor(
    from: string,
    to: string,
    messageType: MessageType,
    communicationChannel: CommunicationChannel = CommunicationChannel.WHATSAPPSELFHOSTED,
    message?: string,
    fileBuffer?: Buffer,
  ) {
    this.from = from;
    this.to = to;
    this.messageType = messageType;
    this.message = message;
    this.communicationChannel = communicationChannel;
    this.fileBuffer = fileBuffer;
  }
}
