import { Message } from '@aws-sdk/client-sqs';
import { SQSService } from '@common/sqs/sqs.service';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { sanitizeMessageBody } from '@common/utils/message-sanitization.util';
import { logger } from '@edutalent/commons-sdk';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { CommunicationChannel } from '@common/enums';
import { randomUUID as uuidv4 } from 'crypto';

@Injectable()
export class SQSConsumerService implements OnModuleInit {
  constructor(
    private readonly sqsService: SQSService,
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
  ) {}

  onModuleInit() {
    logger.info('Starting SQS consumers for message_hub module...');

    const queueConsumerFunctionMap = new Map<string, (messages: Message[]) => void>();

    queueConsumerFunctionMap.set(
      process.env.OUTGOING_MESSAGE_QUEUE_URL,
      this.outGoingMessageConsumer.bind(this),
    );

    queueConsumerFunctionMap.set(
      process.env.INCOMING_MESSAGE_QUEUE_URL,
      this.incomingMessageConsumer.bind(this),
    );

    for (const [queueUrl, handler] of queueConsumerFunctionMap.entries()) {
      this.sqsService.createConsumer(queueUrl, 10, handler);
    }
  }

  private async outGoingMessageConsumer(messages: Message[]): Promise<void> {
    const batchStartTime = Date.now();
    const batchCorrelationId = `batch-${uuidv4()}`;

    logger.info('SQS outgoing message consumer batch started', {
      traceId: batchCorrelationId,
      operation: 'outGoingMessageConsumer',
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    for (const message of messages) {
      const messageStartTime = Date.now();
      const messageCorrelationId = `msg-${uuidv4()}`;

      try {
        const { Body: body } = message;
        const sendMessageRequestDto = JSON.parse(body);

        logger.info('Outgoing message data parsed', {
          traceId: messageCorrelationId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'consumeOutgoingMessage',
          messageBody: sanitizeMessageBody(sendMessageRequestDto),
          layer: 'SQS_CONSUMER',
        });

        await CorrelationContextService.run(
          {
            traceId: messageCorrelationId,
            layer: 'SQS_CONSUMER',
            operation: 'consumeOutgoingMessage',
          },
          async () => {
            await this.outgoingMessageUseCase.consumeMessage(sendMessageRequestDto);
          },
        );

        const messageDuration = Date.now() - messageStartTime;
        logger.info('SQS outgoing message processed successfully', {
          traceId: messageCorrelationId,
          batchCorrelationId,
          operation: 'outGoingMessageConsumer',
          messageId: message.MessageId,
          duration: `${messageDuration}ms`,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      } catch (error) {
        const messageDuration = Date.now() - messageStartTime;
        logger.error('Error processing outgoing messages', {
          traceId: messageCorrelationId,
          batchCorrelationId,
          operation: 'outGoingMessageConsumer',
          messageId: message.MessageId,
          duration: `${messageDuration}ms`,
          error: error.message,
          errorType: error.constructor?.name,
          severity: 'HIGH',
          retryable: true,
          stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
          messageBody: message.Body,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      }
    }

    const batchDuration = Date.now() - batchStartTime;
    logger.info('SQS outgoing message consumer batch completed', {
      traceId: batchCorrelationId,
      operation: 'outGoingMessageConsumer',
      batchSize: messages.length,
      duration: `${batchDuration}ms`,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });
  }

  private async incomingMessageConsumer(messages: Message[]): Promise<void> {
    const batchStartTime = Date.now();
    const batchCorrelationId = `batch-${uuidv4()}`;

    logger.info('SQS incoming message consumer batch started', {
      traceId: batchCorrelationId,
      operation: 'incomingMessageConsumer',
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    for (const message of messages) {
      const messageStartTime = Date.now();
      const messageCorrelationId = `msg-${uuidv4()}`;

      try {
        const { Body: body } = message;
        const incomingMessageDto = JSON.parse(body);

        const communicationChannelEnum =
          CommunicationChannel[incomingMessageDto.communicationChannel];

        // Use phone number as part of traceId for better business correlation
        const phoneBasedTraceId = `phone-${incomingMessageDto.from}-${messageCorrelationId}`;

        logger.info('Incoming message data parsed', {
          traceId: phoneBasedTraceId,
          correlationId: messageCorrelationId,
          operation: 'consumeIncomingMessage',
          from: incomingMessageDto.from,
          to: incomingMessageDto.to,
          messageType: incomingMessageDto.messageType,
          communicationChannel: incomingMessageDto.communicationChannel,
          messageBody: sanitizeMessageBody(incomingMessageDto),
          layer: 'SQS_CONSUMER',
        });

        await CorrelationContextService.run(
          {
            traceId: phoneBasedTraceId,
            layer: 'SQS_CONSUMER',
            operation: 'consumeIncomingMessage',
          },
          async () => {
            await this.incomingMessageUseCase.consumeIncomingMessage(
              incomingMessageDto.from,
              incomingMessageDto.to,
              incomingMessageDto.message,
              incomingMessageDto.messageType,
              communicationChannelEnum,
              incomingMessageDto.fileUrl,
            );
          },
        );

        const messageDuration = Date.now() - messageStartTime;
        logger.info('SQS incoming message processed successfully', {
          traceId: phoneBasedTraceId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'incomingMessageConsumer',
          messageId: message.MessageId,
          from: incomingMessageDto.from,
          to: incomingMessageDto.to,
          duration: `${messageDuration}ms`,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      } catch (error) {
        const messageDuration = Date.now() - messageStartTime;

        // Extract phone info for error logging even if parsing failed
        let errorPhoneTraceId = messageCorrelationId;
        let fromPhone: string | undefined;
        let toPhone: string | undefined;
        try {
          const errorData = JSON.parse(message.Body);
          fromPhone = errorData.from;
          toPhone = errorData.to;
          errorPhoneTraceId = `phone-${fromPhone}-${messageCorrelationId}`;
        } catch {
          // If parsing fails, use messageCorrelationId as traceId
        }

        logger.error('Error processing incoming messages', {
          traceId: errorPhoneTraceId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'incomingMessageConsumer',
          messageId: message.MessageId,
          from: fromPhone,
          to: toPhone,
          duration: `${messageDuration}ms`,
          error: error.message,
          errorType: error.constructor?.name,
          severity: 'HIGH',
          retryable: true,
          stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
          messageBody: message.Body,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      }
    }

    const batchDuration = Date.now() - batchStartTime;
    logger.info('SQS incoming message consumer batch completed', {
      traceId: batchCorrelationId,
      operation: 'incomingMessageConsumer',
      batchSize: messages.length,
      duration: `${batchDuration}ms`,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });
  }
}
