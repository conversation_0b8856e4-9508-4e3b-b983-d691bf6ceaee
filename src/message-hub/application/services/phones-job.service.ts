import { logger } from '@edutalent/commons-sdk';
import { Inject, Injectable } from '@nestjs/common';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import * as schedule from 'node-schedule';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { Cron } from '@nestjs/schedule';
import { CommunicationChannel } from '@common/enums';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class PhonesJobService {
  constructor(
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
  ) {}

  @Cron('* * * * *')
  async createNewPhonesJobs() {
    return CorrelationContextService.runBackgroundJob(
      'MESSAGE_HUB_CRON',
      'createNewPhonesJobs',
      async () => {
        logger.info('Checking Jobs for created customer phones...');

        const customerPhoneEntities = await this.customerPhoneAdapter.getAll();

        for (const customerPhone of customerPhoneEntities) {
          const incomingJobName = `${customerPhone.phoneNumber}_${customerPhone.communicationChannel}_incoming`;
          const outgoingJobName = `${customerPhone.phoneNumber}_${customerPhone.communicationChannel}_outgoing`;

          if (!schedule.scheduledJobs[incomingJobName]) {
            const incomingJob = schedule.scheduleJob(
              incomingJobName,
              customerPhone.incomingCron,
              async () => {
                await CorrelationContextService.runBackgroundJob(
                  'MESSAGE_HUB_JOB',
                  `incomingMessages-${customerPhone.phoneNumber}-${customerPhone.communicationChannel}`,
                  async () => {
                    await this.incomingMessageUseCase.readPendingMessage(
                      customerPhone.phoneNumber,
                      customerPhone.communicationChannel,
                    );
                  },
                );
              },
            );
            logger.info(
              `Scheduled missing incoming job for phone: ${
                customerPhone.phoneNumber
              } and channel: ${customerPhone.communicationChannel}, with cron: ${
                customerPhone.incomingCron
              }. Next invocation: ${incomingJob.nextInvocation()}`,
            );
          }

          if (!schedule.scheduledJobs[outgoingJobName]) {
            if (
              customerPhone.communicationChannel === CommunicationChannel.BLIP_COLINA ||
              customerPhone.communicationChannel === CommunicationChannel.LOVELACE
            ) {
              logger.info(
                `Channel ${customerPhone.communicationChannel} does not require a job to outgoing messages. So skipping job: ${outgoingJobName} creation...`,
              );
              continue;
            }
            const outgoingJob = schedule.scheduleJob(
              outgoingJobName,
              customerPhone.outgoingCron,
              async () => {
                await CorrelationContextService.runBackgroundJob(
                  'MESSAGE_HUB_JOB',
                  `outgoingMessages-${customerPhone.phoneNumber}-${customerPhone.communicationChannel}`,
                  async () => {
                    await this.outgoingMessageUseCase.sendPendingMessage(
                      customerPhone.phoneNumber,
                      customerPhone.communicationChannel,
                    );
                  },
                );
              },
            );
            logger.info(
              `Scheduled missing outgoing job for phone: ${
                customerPhone.phoneNumber
              } and channel: ${customerPhone.communicationChannel}, with cron: ${
                customerPhone.outgoingCron
              }. Next invocation: ${outgoingJob.nextInvocation()}`,
            );
          }
        }
      },
    );
  }

  @Cron('*/3 * * * *')
  async stopDeletedPhonesJobs() {
    return CorrelationContextService.runBackgroundJob(
      'MESSAGE_HUB_CRON',
      'stopDeletedPhonesJobs',
      async () => {
        logger.info('Checking Jobs for deleted customer phones...');

        const customerPhoneEntities = await this.customerPhoneAdapter.getAll();
        const phoneNumbersWithChannels = customerPhoneEntities.map(
          phone => `${phone.phoneNumber}_${phone.communicationChannel}`,
        );

        const filteredPhoneJobs = Object.keys(schedule.scheduledJobs).filter(
          jobName => jobName.includes('incoming') || jobName.includes('outgoing'),
        );

        for (const jobName of filteredPhoneJobs) {
          const jobParts = jobName.split('_');
          const direction = jobParts.pop();
          const phoneWithChannel = jobParts.join('_');

          if (!phoneNumbersWithChannels.includes(phoneWithChannel)) {
            schedule.scheduledJobs[jobName].cancel();
            logger.info(`Cancelled ${direction} job for deleted phone: ${phoneWithChannel}`);
          }
        }
      },
    );
  }
}
