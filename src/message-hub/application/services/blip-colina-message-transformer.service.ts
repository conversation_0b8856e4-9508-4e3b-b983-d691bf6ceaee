import { IMessageTransformerService } from '@message-hub/application/services/imessage-transformer.service';
import { SendMessageRequestDto } from '@message-hub/application/dto/in/send-message-request.dto';
import { DefaultOutgoingMessage } from '@message-hub/application/models/default-message.models';

export class BlipColinaMessageTransformerService
  implements IMessageTransformerService<DefaultOutgoingMessage>
{
  generateOutgoingMessage(sendMessageRequestDto: SendMessageRequestDto): DefaultOutgoingMessage {
    return new DefaultOutgoingMessage(
      sendMessageRequestDto.customerId,
      sendMessageRequestDto.to,
      sendMessageRequestDto.messageType,
      sendMessageRequestDto.message,
      sendMessageRequestDto.communicationChannel,
      sendMessageRequestDto.isFirstMessage,
      sendMessageRequestDto.fileUrl,
    );
  }
}
