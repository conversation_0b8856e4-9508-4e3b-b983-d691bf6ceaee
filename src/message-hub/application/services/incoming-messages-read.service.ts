import { logger } from '@edutalent/commons-sdk';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import * as schedule from 'node-schedule';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class IncomingMessagesReadService implements OnModuleInit {
  constructor(
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
  ) {}

  async onModuleInit() {
    logger.info('Starting Jobs to read messages for each customer phone...');

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll();

    for (const customerPhone of customerPhoneEntities) {
      const job = schedule.scheduleJob(
        `${customerPhone.phoneNumber}_${customerPhone.communicationChannel}_incoming`,
        customerPhone.incomingCron,
        async () => {
          await CorrelationContextService.runBackgroundJob(
            'MESSAGE_HUB_JOB',
            `incomingMessages-${customerPhone.phoneNumber}-${customerPhone.communicationChannel}`,
            async () => {
              await this.incomingMessageUseCase.readPendingMessage(
                customerPhone.phoneNumber,
                customerPhone.communicationChannel,
              );
            },
          );
        },
      );

      logger.info(
        `Job to read incoming messages for phone: ${customerPhone.phoneNumber} of customer: ${
          customerPhone.customerId
        } with cron expression: ${customerPhone.incomingCron} for channel: ${
          customerPhone.communicationChannel
        } and will start running at: ${job.nextInvocation()}`,
      );
    }
  }
}
