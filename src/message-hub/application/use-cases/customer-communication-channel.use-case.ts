import { Inject, Injectable } from '@nestjs/common';
import { CustomerCommunicationChannelPort } from '@message-hub/infrastructure/ports/db/customer-communication-channel.port';
import { CustomerCommunicationChannelDto } from '@message-hub/application/dto/in/customer-communication-channel.dto';
import { logger } from '@edutalent/commons-sdk';
import { randomUUID as uuidv4 } from 'crypto';
import { CustomerCommunicationChannelEntity } from '@message-hub/domain/entities/customer-communication-channel.entity';
import { CustomerCommunicationChannelResponseDto } from '@message-hub/application/dto/out/customer-communication-channel-response.dto';
import { CustomerCommunicationChannelIntegrationDataPort } from '@message-hub/infrastructure/ports/db/customer-communication-channel-integration-data.port';
import { CustomerChannelIntegrationDataEntity } from '@message-hub/domain/entities/customer-channel-integration-data.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

@Injectable()
export class CustomerCommunicationChannelUseCase {
  constructor(
    @Inject('CustomerCommunicationChannelPort')
    private readonly customerCommunicationChannelAdapter: CustomerCommunicationChannelPort,
    @Inject('CustomerCommunicationChannelIntegrationDataPort')
    private readonly customerCommunicationChannelIntegrationDataAdapter: CustomerCommunicationChannelIntegrationDataPort,
  ) {}

  async createCustomerCommunicationChannel(
    customerCommunicationChannelDto: CustomerCommunicationChannelDto,
  ): Promise<CustomerCommunicationChannelResponseDto> {
    logger.info(
      `Creating customer communication channel for customer:   ${customerCommunicationChannelDto.customerId}`,
    );

    //TODO: Add http adapter to call portfolio and customer endpoints to validate the customer and portfolio

    const integrationDataEntity =
      await this.customerCommunicationChannelIntegrationDataAdapter.create(
        new CustomerChannelIntegrationDataEntity(
          uuidv4(),
          customerCommunicationChannelDto.integrationData,
          new Date(),
          new Date(),
        ),
      );

    const customerCommunicationChannelEntity = new CustomerCommunicationChannelEntity(
      uuidv4(),
      customerCommunicationChannelDto.customerId,
      customerCommunicationChannelDto.portfolioId,
      customerCommunicationChannelDto.communicationChannel,
      integrationDataEntity.id,
    );

    const createdCustomerChannel = await this.customerCommunicationChannelAdapter.create(
      customerCommunicationChannelEntity,
    );

    return this.getCustomerCommunicationChannelResponseDto(
      createdCustomerChannel,
      integrationDataEntity,
    );
  }

  async getCustomerCommunicationChannelById(
    id: string,
  ): Promise<CustomerCommunicationChannelResponseDto> {
    logger.info(`Fetching customer communication channel by id: ${id}`);

    const customerCommunicationChannelEntity = await this.customerCommunicationChannelAdapter.get(
      id,
    );

    if (!customerCommunicationChannelEntity) {
      throw new BusinessException(
        'CustomerCommunicationChannel-use-case',
        `Customer communication channel with id ${id} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const integrationDataEntity =
      await this.customerCommunicationChannelIntegrationDataAdapter.getById(
        customerCommunicationChannelEntity.integrationDataId,
      );

    return this.getCustomerCommunicationChannelResponseDto(
      customerCommunicationChannelEntity,
      integrationDataEntity,
    );
  }

  async getCustomerCommunicationChannelsByCustomerId(
    id: string,
  ): Promise<CustomerCommunicationChannelResponseDto[]> {
    logger.info(`Fetching customer communication channels by customerId: ${id}`);

    const customerCommunicationChannelsEntity =
      await this.customerCommunicationChannelAdapter.getAllByCustomerId(id);

    return Promise.all(
      customerCommunicationChannelsEntity.map(async customerCommunicationChannelEntity => {
        const integrationDataEntity =
          await this.customerCommunicationChannelIntegrationDataAdapter.getById(
            customerCommunicationChannelEntity.integrationDataId,
          );

        return this.getCustomerCommunicationChannelResponseDto(
          customerCommunicationChannelEntity,
          integrationDataEntity,
        );
      }),
    );
  }

  async deleteCustomerCommunicationChannelById(
    id: string,
  ): Promise<CustomerCommunicationChannelResponseDto> {
    logger.info(`Deleting customer communication channel by id: ${id}`);

    const deletedCustomerChannel = await this.customerCommunicationChannelAdapter.delete(id);
    const integrationDataEntity =
      await this.customerCommunicationChannelIntegrationDataAdapter.getById(
        deletedCustomerChannel.integrationDataId,
      );
    await this.customerCommunicationChannelIntegrationDataAdapter.deleteById(
      deletedCustomerChannel.integrationDataId,
    );

    return this.getCustomerCommunicationChannelResponseDto(
      deletedCustomerChannel,
      integrationDataEntity,
    );
  }

  private getCustomerCommunicationChannelResponseDto(
    createdCustomerChannel: CustomerCommunicationChannelEntity,
    integrationDataEntity?: CustomerChannelIntegrationDataEntity,
  ) {
    return new CustomerCommunicationChannelResponseDto(
      createdCustomerChannel.id,
      createdCustomerChannel.customerId,
      createdCustomerChannel.portfolioId,
      createdCustomerChannel.channel,
      createdCustomerChannel.integrationDataId,
      integrationDataEntity?.integrationData || integrationDataEntity,
      createdCustomerChannel.createdAt,
      createdCustomerChannel.updatedAt,
    );
  }
}
