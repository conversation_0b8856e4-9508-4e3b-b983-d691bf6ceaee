import { Injectable } from '@nestjs/common';
import { CustomerPhoneUseCase } from '@message-hub/application/use-cases/customer-phone.use-case';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { IncomingMessageUseCase } from '@message-hub/application/use-cases/incoming-message.use-case';

@Injectable()
export class MetricUseCase {
  constructor(
    private readonly customerPhoneUseCase: CustomerPhoneUseCase,
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
    private readonly incomingMessageUseCase: IncomingMessageUseCase,
  ) {}

  async getSentAnswerMessagesByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    dateStart = new Date(dateStart + 'T00:00:00Z');
    dateEnd = new Date(dateEnd + 'T23:59:59Z');

    const totalAnswerMessagesSent = await this.outgoingMessageUseCase.getTotalAnswerMessagesSent(
      customerId,
      dateStart,
      dateEnd,
    );

    return totalAnswerMessagesSent;
  }

  async getSentFirstMessagesByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    dateStart = new Date(dateStart + 'T00:00:00Z');
    dateEnd = new Date(dateEnd + 'T23:59:59Z');

    const totalFirstMessagesSent = await this.outgoingMessageUseCase.getTotalFirstMessagesSent(
      customerId,
      dateStart,
      dateEnd,
    );

    return totalFirstMessagesSent;
  }

  async getReceivedMessagesByDate(
    customerId: string,
    dateStart: Date,
    dateEnd: Date,
  ): Promise<any> {
    dateStart = new Date(dateStart + 'T00:00:00Z');
    dateEnd = new Date(dateEnd + 'T23:59:59Z');

    const customerPhones = await this.customerPhoneUseCase.getCustomerPhonesByCustomerId(
      customerId,
    );

    const totalMessagesReceived = await this.incomingMessageUseCase.getTotalMessagesReceived(
      customerPhones.map(phone => phone.phoneNumber),
      dateStart,
      dateEnd,
    );

    return totalMessagesReceived;
  }
}
