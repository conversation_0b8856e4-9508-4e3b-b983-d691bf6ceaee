import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { CommunicationChannel, MessageType } from '@common/enums';
import { InfraBusinessBasePort } from '@message-hub/infrastructure/ports/http/business-base.port';
import { IncomingMessagePort } from '@message-hub/infrastructure/ports/db/incoming-message.port';
import { randomUUID as uuidv4 } from 'crypto';
import { IncomingMessageEntity } from '@message-hub/domain/entities/incoming-message.entity';
import { CustomerPhoneDestinationAdapter } from '@message-hub/infrastructure/adapters/db/customer-phone-destination.adapter';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import { CustomerPhoneDestinationEntity } from '@message-hub/domain/entities/customer-phone-destination.entity';

@Injectable()
export class IncomingMessageUseCase {
  constructor(
    @Inject('InfraBusinessBasePort')
    readonly businessBaseAdapter: InfraBusinessBasePort,
    @Inject('IncomingMessagePort')
    readonly incomingMessageAdapter: IncomingMessagePort,
    @Inject('CustomerPhonePort')
    readonly customerPhoneAdapter: CustomerPhonePort,
    @Inject('CustomerPhoneDestinationPort')
    readonly customerPhoneDestinationAdapter: CustomerPhoneDestinationAdapter,
  ) {}

  async readPendingMessage(phoneNumberTo: string, channel: CommunicationChannel): Promise<void> {
    try {
      await this.incomingMessageAdapter.processIncomingMessages(
        phoneNumberTo,
        channel,
        this.businessBaseAdapter.executeItem.bind(this.businessBaseAdapter),
      );
    } catch (error) {
      logger.info(
        `Error Reading pending messages for phone: ${phoneNumberTo} and channel: ${channel}... Error: ${JSON.stringify(
          error,
        )}`,
      );
      return;
    }
  }

  async consumeIncomingMessage(
    from: string,
    to: string,
    message: string,
    messageType: MessageType,
    channel: CommunicationChannel,
    fileUrl: string,
  ): Promise<void> {
    logger.info(
      `Consuming incoming message: ${JSON.stringify({
        from,
        to,
        message,
        messageType,
        channel,
        fileUrl,
      })}`,
    );

    const incomingMessage = new IncomingMessageEntity(
      uuidv4(),
      from,
      to,
      messageType,
      channel,
      message,
      fileUrl,
    );

    const customerPhone = await this.customerPhoneAdapter.getByPhoneNumberAndCommunicationChannel(
      to,
      channel,
    );
    if (customerPhone) {
      const customerPhoneDestination =
        await this.customerPhoneDestinationAdapter.getByCustomerIdAndDestinationAndCommunicationChannel(
          customerPhone.customerId,
          from,
          channel,
        );

      if (!customerPhoneDestination) {
        await this.customerPhoneDestinationAdapter.create(
          new CustomerPhoneDestinationEntity(
            customerPhone.customerId,
            customerPhone.phoneNumber,
            from,
            customerPhone.communicationChannel,
          ),
        );
      }

      await this.incomingMessageAdapter.create(incomingMessage);
    } else {
      logger.info(
        `Discarding incoming message... No customer phone found for phone number: ${to} and channel: ${channel}.`,
      );
    }
  }

  async getTotalMessagesReceived(
    phoneNumbers: string[],
    dateStart: Date,
    dateEnd: Date,
  ): Promise<{
    total: number;
    dailyTotals: { [date: string]: number };
  }> {
    const incomingMessages = await this.incomingMessageAdapter.getAll({
      to: {
        in: phoneNumbers,
      },
      read: true,
      readAt: {
        gte: dateStart.toISOString(),
        lte: dateEnd.toISOString(),
      },
    });

    const total = incomingMessages.length;

    const dailyTotals: { [date: string]: number } = {};
    incomingMessages.forEach(message => {
      const date = new Date(message.readAt).toISOString().split('T')[0];
      dailyTotals[date] = (dailyTotals[date] || 0) + 1;
    });

    return {
      total,
      dailyTotals,
    };
  }

  async reprocessLastMessage(phoneNumber: string): Promise<void> {
    logger.info(`Reprocessing last message for phone number: ${phoneNumber}`);

    const incomingMessages = await this.incomingMessageAdapter.getAll({
      from: phoneNumber,
      read: true,
    });

    if (incomingMessages.length === 0) {
      logger.error(`No incoming messages found for phone number: ${phoneNumber}`);
      return;
    }

    // Get the last incoming message by createdAt
    const incomingMessage = incomingMessages.sort(
      (a, b) => a.createdAt.getTime() - b.createdAt.getTime(),
    )[incomingMessages.length - 1];

    incomingMessage.read = false;
    incomingMessage.readAt = null;

    await this.incomingMessageAdapter.update(incomingMessage);
  }
}
