import { Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { CustomerPhonePort } from '@message-hub/infrastructure/ports/db/customer-phone.port';
import { CustomerPhoneDto } from '@message-hub/application/dto/in/customer-phone.dto';
import { CustomerPhoneEntity } from '@message-hub/domain/entities/customer-phone.entity';
import { CustomerPhoneResponseDto } from '@message-hub/application/dto/out/customer-phone-response.dto';
import { plainToClass } from 'class-transformer';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { CustomerPhoneDestinationPort } from '@message-hub/infrastructure/ports/db/customer-phone-destination.port';
import { CommunicationChannel } from '@common/enums';

@Injectable()
export class CustomerPhoneUseCase {
  constructor(
    @Inject('CustomerPhonePort')
    private readonly customerPhoneAdapter: CustomerPhonePort,
    @Inject('CustomerPhoneDestinationPort')
    private readonly customerPhoneDestinationAdapter: CustomerPhoneDestinationPort,
  ) {}

  async createCustomerPhone(
    customerId: string,
    customerPhoneDto: CustomerPhoneDto,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(`Creating customer phone for customer:   ${customerId}`);

    const customerPhoneEntity = new CustomerPhoneEntity(
      customerId,
      customerPhoneDto.phoneNumber,
      customerPhoneDto.communicationChannel,
      customerPhoneDto.incomingCron,
      customerPhoneDto.outgoingCron,
      customerPhoneDto.outgoingMaxDelay,
      customerPhoneDto.dailyLimit,
      customerPhoneDto.weight,
      customerPhoneDto.apiUrl,
    );

    const createdCustomerPhone = await this.customerPhoneAdapter.create(customerPhoneEntity);

    return this.getCustomerPhoneResponseDto(createdCustomerPhone);
  }

  async getCustomerPhoneByPhoneNumberAndCommunicationChannel(
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(`Fetching customer phone by phone number: ${phoneNumber}`);

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByPhoneNumberAndCommunicationChannel(
        phoneNumber,
        communicationChannel,
      );

    if (!customerPhoneEntity) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of phone: ${phoneNumber} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return this.getCustomerPhoneResponseDto(customerPhoneEntity);
  }

  async getCustomerPhonesByCustomerId(customerId: string): Promise<CustomerPhoneResponseDto[]> {
    logger.info(`Fetching customer phones by customerId: ${customerId}`);

    const customerPhoneEntities = await this.customerPhoneAdapter.getAll({
      customerId,
    });

    // Process customer phones sequentially to avoid connection pool exhaustion
    const results = [];
    for (const entity of customerPhoneEntities) {
      const result = this.getCustomerPhoneResponseDto(entity);
      results.push(result);
    }
    return results;
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumber(
    customerId: string,
    phoneNumber: string,
  ): Promise<CustomerPhoneResponseDto[]> {
    logger.info(
      `Fetching customer phone by customerId: ${customerId} and phone number: ${phoneNumber}`,
    );

    const customerPhoneEntities = await this.customerPhoneAdapter.getByCustomerIdAndPhoneNumber(
      customerId,
      phoneNumber,
    );

    if (!customerPhoneEntities) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of customer id: ${customerId} and ${phoneNumber} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return customerPhoneEntities.map(customerPhoneEntity =>
      this.getCustomerPhoneResponseDto(customerPhoneEntity),
    );
  }

  async getCustomerPhoneByCustomerIdAndPhoneNumberAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(
      `Fetching customer phone by customerId: ${customerId}, phone number: ${phoneNumber} and communication channel: ${communicationChannel}`,
    );

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      );

    if (!customerPhoneEntity) {
      throw new BusinessException(
        'Customer-Phone-Use-Case',
        `PhoneNumber of customer id: ${customerId}, phone: ${phoneNumber} and communicationChannel: ${communicationChannel} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    return this.getCustomerPhoneResponseDto(customerPhoneEntity);
  }

  async updatePhoneNumberApiUrl(
    customerId: string,
    phoneNumber: string,
    channel: CommunicationChannel,
    apiUrl: string,
  ): Promise<CustomerPhoneResponseDto> {
    logger.info(
      `Update customer phone api url by customerId: ${customerId} and phone number: ${phoneNumber}`,
    );

    const customerPhoneEntity =
      await this.customerPhoneAdapter.getByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        channel,
      );
    const updatedCustomerPhone = {
      ...customerPhoneEntity,
      apiUrl: apiUrl,
    };

    await this.customerPhoneAdapter.update(updatedCustomerPhone);

    return this.getCustomerPhoneResponseDto(updatedCustomerPhone);
  }

  async deleteAllCustomerPhoneByCustomerId(customerId: string): Promise<void> {
    logger.info(`Deleting customer phones by customer id: ${customerId}`);

    // Execute deletions sequentially to avoid connection pool exhaustion
    await this.customerPhoneAdapter.deleteAllByCustomerId(customerId);
    await this.customerPhoneDestinationAdapter.deleteAllByCustomerId(customerId);
  }

  async deleteCustomerPhoneByCustomerIdAndPhone(
    customerId: string,
    phoneNumber: string,
  ): Promise<void> {
    logger.info(
      `Deleting customer phones by customer id: ${customerId} and phone number: ${phoneNumber}`,
    );

    // Execute deletions sequentially to avoid connection pool exhaustion
    await this.customerPhoneAdapter.deleteByCustomerIdAndPhoneNumber(customerId, phoneNumber);
    await this.customerPhoneDestinationAdapter.deleteAllByCustomerIdAndPhoneNumber(
      customerId,
      phoneNumber,
    );
  }

  async deleteCustomerPhoneByCustomerIdAndPhoneAndCommunicationChannel(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
  ): Promise<void> {
    logger.info(
      `Deleting customer phones by customer id: ${customerId}, phone number: ${phoneNumber} and communication channel: ${communicationChannel}`,
    );

    await Promise.all([
      this.customerPhoneAdapter.deleteByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      ),
      this.customerPhoneDestinationAdapter.deleteAllByCustomerIdAndPhoneNumberAndCommunicationChannel(
        customerId,
        phoneNumber,
        communicationChannel,
      ),
    ]);
  }

  async getNextBalancedPhoneByCustomerIdAndCommunicationChannel(
    customerId: string,
    communicationChannel: CommunicationChannel,
  ): Promise<string> {
    logger.info(
      `Fetching next phone number for customer: ${customerId} and channel: ${communicationChannel}`,
    );

    const phoneNumbers =
      await this.customerPhoneDestinationAdapter.getNextPhoneByCustomerIdAndCommunicationChannel(
        customerId,
        communicationChannel,
      );

    logger.info(`Available phones for customer: ${customerId}: ${JSON.stringify(phoneNumbers)}`);
    if (!phoneNumbers || phoneNumbers.length === 0) {
      logger.error(
        `No phone numbers found for the given customer: ${customerId} and communication channel: ${communicationChannel}`,
      );
      return null;
    }

    try {
      const totalWeight = phoneNumbers.reduce((sum, n) => sum + n.weight, 0);
      if (totalWeight === 0) return null;
      const random = Math.random() * totalWeight;
      let cumulative = 0;
      for (const number of phoneNumbers) {
        cumulative += number.weight;
        if (random < cumulative) {
          logger.info(
            `Selected phone number for customer: ${customerId} and channel: ${communicationChannel}: ${number.phoneNumber}`,
          );
          return number.phoneNumber;
        }
      }
    } catch (error) {
      logger.error(
        `Unable to use round robin method in phone number balancing... Fall back to equal division method and selecting current less used phone: ${phoneNumbers[0].phoneNumber}`,
      );

      return phoneNumbers[0].phoneNumber;
    }
  }

  private getCustomerPhoneResponseDto(
    createdCustomerPhone: CustomerPhoneEntity,
  ): CustomerPhoneResponseDto {
    return plainToClass(CustomerPhoneResponseDto, createdCustomerPhone);
  }
}
