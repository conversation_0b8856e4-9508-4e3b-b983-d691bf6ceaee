import { CommunicationChannel, MessageType } from '@common/enums';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class DefaultOutgoingMessage {
  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly to: string;

  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsString()
  @IsNotEmpty()
  readonly message: string;

  @IsNotEmpty()
  @IsBoolean()
  readonly isFirstMessage: boolean; //can be the message text or the file url

  @IsOptional()
  @IsString()
  readonly fileUrl?: string;

  constructor(
    customerId: string,
    to: string,
    messageType: MessageType,
    message: string,
    communicationChannel: CommunicationChannel,
    isFirstMessage: boolean,
    fileUrl?: string,
  ) {
    this.customerId = customerId;
    this.to = to;
    this.messageType = messageType;
    this.message = message;
    this.communicationChannel = communicationChannel;
    this.isFirstMessage = isFirstMessage;
    this.fileUrl = fileUrl;
  }
}
