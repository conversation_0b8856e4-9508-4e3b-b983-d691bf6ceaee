import { Controller, Get, Query, Req, Version } from '@nestjs/common';
import { MetricUseCase } from '@message-hub/application/use-cases/metric.use-case';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { AccountRole, UserRoleInAccount } from '@common/enums';
import { ApiBearerAuth, ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';

@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.BASIC)
@Controller('message-hub/metrics')
export class MetricsController {
  constructor(private readonly metricUseCase: MetricUseCase) {}

  @Version('1')
  @ApiOperation({ summary: 'Get total answer messages sent' })
  @ApiQuery({ name: 'dateStart', type: String, required: true, example: '2024-06-01' })
  @ApiQuery({ name: 'dateEnd', type: String, required: true, example: '2024-06-12' })
  @ApiResponse({
    status: 200,
    description: 'Total answer messages sent',
    schema: {
      example: {
        statusCode: 200,
        data: {
          totalAnswerMessagesSent: {
            total: 4,
            dailyTotals: {
              '2025-06-10': 2,
              '2025-06-11': 1,
              '2025-06-12': 1,
            },
          },
        },
      },
    },
  })
  @ApiBearerAuth()
  @Get('answer-messages-sent')
  @Version('1')
  async getAnswerMessagesSent(
    @Query('dateStart') dateStart: Date,
    @Query('dateEnd') dateEnd: Date,
    @Req() request: Request,
  ): Promise<any> {
    const { customerId } = request['user'];
    const metrics = await this.metricUseCase.getSentAnswerMessagesByDate(
      customerId,
      dateStart,
      dateEnd,
    );

    return {
      statusCode: 200,
      data: {
        totalAnswerMessagesSent: metrics,
      },
    };
  }

  @ApiOperation({ summary: 'Get total first messages sent' })
  @ApiQuery({ name: 'dateStart', type: String, required: true, example: '2024-06-01' })
  @ApiQuery({ name: 'dateEnd', type: String, required: true, example: '2024-06-12' })
  @ApiResponse({
    status: 200,
    description: 'Total first messages sent',
    schema: {
      example: {
        statusCode: 200,
        data: {
          totalFirstMessagesSent: {
            total: 1,
            dailyTotals: {
              '2025-06-12': 1,
            },
          },
        },
      },
    },
  })
  @ApiBearerAuth()
  @Get('first-messages-sent')
  @Version('1')
  async getFirstMessagesSent(
    @Query('dateStart') dateStart: Date,
    @Query('dateEnd') dateEnd: Date,
    @Req() request: Request,
  ): Promise<any> {
    const { customerId } = request['user'];
    const metrics = await this.metricUseCase.getSentFirstMessagesByDate(
      customerId,
      dateStart,
      dateEnd,
    );

    return {
      statusCode: 200,
      data: {
        totalFirstMessagesSent: metrics,
      },
    };
  }

  @ApiOperation({ summary: 'Get total messages received' })
  @ApiQuery({ name: 'dateStart', type: String, required: true, example: '2024-06-01' })
  @ApiQuery({ name: 'dateEnd', type: String, required: true, example: '2024-06-12' })
  @ApiResponse({
    status: 200,
    description: 'Total messages received',
    schema: {
      example: {
        statusCode: 200,
        data: {
          totalMessagesReceived: {
            total: 4,
            dailyTotals: {
              '2025-06-12': 4,
            },
          },
        },
      },
    },
  })
  @ApiBearerAuth()
  @Get('messages-received')
  @Version('1')
  async getMessagesReceived(
    @Query('dateStart') dateStart: Date,
    @Query('dateEnd') dateEnd: Date,
    @Req() request: Request,
  ): Promise<any> {
    const { customerId } = request['user'];
    const metrics = await this.metricUseCase.getReceivedMessagesByDate(
      customerId,
      dateStart,
      dateEnd,
    );

    return {
      statusCode: 200,
      data: {
        totalMessagesReceived: metrics,
      },
    };
  }
}
