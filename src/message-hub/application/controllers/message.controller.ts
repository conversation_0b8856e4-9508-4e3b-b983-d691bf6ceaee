import { Body, Controller, Get, Param, Post, Put, Req, UseGuards, Version } from '@nestjs/common';
import { SendMessageRequestDto } from '@message-hub/application/dto/in/send-message-request.dto';
import { MessageUseCase } from '@message-hub/application/use-cases/message.use-case';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { IncomingMessageRequestDto } from '@message-hub/application/dto/in/incoming-message-request.dto';
import { SystemAuthnGuard } from '@common/auth/system-authn.guard';
import { RetrievePendingMessageRequestDto } from '@message-hub/application/dto/in/retrieve-pending-message-request.dto';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/message')
export class MessageController {
  constructor(
    private readonly messsageUseCase: MessageUseCase,
    private readonly outgoingMessageUseCase: OutgoingMessageUseCase,
  ) {}

  @Get()
  @Version('1')
  @UseGuards(SystemAuthnGuard)
  async Get(@Req() request: Request): Promise<any> {
    const customerId = request['user'].customerId;
    return `Hello from MessageHub v1 ${customerId}`;
  }

  @Post('/pending/outgoing')
  @Version('1')
  @UseGuards(SystemAuthnGuard)
  async getPendingOutgoingMessages(
    @Req() request: Request,
    @Body() pendingMessageRequestDto: RetrievePendingMessageRequestDto,
  ): Promise<any> {
    const customerId = request['user'].customerId;
    const messages = await this.outgoingMessageUseCase.getPendingOutgoingMessages(
      customerId,
      pendingMessageRequestDto,
    );

    return {
      statusCode: 201,
      data: messages,
    };
  }

  @Post()
  @Version('1')
  async sendMessage(@Body() sendMessageRequestDto: SendMessageRequestDto): Promise<any> {
    await this.messsageUseCase.sendMessage(sendMessageRequestDto);

    return {
      statusCode: 201,
      data: {},
    };
  }

  @Post('/receive')
  @Version('1')
  async receiveMessage(@Body() incomingMessageRequestDto: IncomingMessageRequestDto): Promise<any> {
    await this.messsageUseCase.receiveMessage(incomingMessageRequestDto);

    return {
      statusCode: 201,
      data: {},
    };
  }

  @Get('/timestamps')
  @Version('1')
  async getTimeStamps() {
    const currentAppTime = new Date();
    const currentAppTimeToString = new Date().toString();
    const currentDatabaseTime = await this.outgoingMessageUseCase.getCurrentDatabaseTimestamp();

    return {
      statusCode: 200,
      data: {
        appTime: currentAppTime,
        appTimeToString: currentAppTimeToString,
        databaseTimes: currentDatabaseTime,
      },
    };
  }

  @Put('/reprocess-last-message/:phoneNumber')
  @Version('1')
  async reprocessMessage(@Param('phoneNumber') phoneNumber: string): Promise<any> {
    await this.messsageUseCase.reprocessLastMessage(phoneNumber);

    return {
      statusCode: 200,
      data: {},
    };
  }
}
