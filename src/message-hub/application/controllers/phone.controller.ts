import { Controller, Get, Param, Version } from '@nestjs/common';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { CustomerPhoneUseCase } from '@message-hub/application/use-cases/customer-phone.use-case';
import { CommunicationChannel } from '@common/enums';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/phones/:phoneNumber/communication-channel/:communicationChannel')
export class PhoneController {
  constructor(private readonly customerPhoneUseCase: CustomerPhoneUseCase) {}

  @Get()
  @Version('1')
  async findByPhoneNumberAndCommunicationChannel(
    @Param('phoneNumber') phoneNumber: string,
    @Param('communicationChannel') communicationChannel: CommunicationChannel,
  ): Promise<any> {
    const customerPhone =
      await this.customerPhoneUseCase.getCustomerPhoneByPhoneNumberAndCommunicationChannel(
        phoneNumber,
        communicationChannel,
      );

    return {
      statusCode: 200,
      data: customerPhone,
    };
  }
}
