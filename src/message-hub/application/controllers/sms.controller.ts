import { Body, Controller, Get, Post, Version } from '@nestjs/common';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { MessageUseCase } from '@message-hub/application/use-cases/message.use-case';
import { IncomingMessageRequestDto } from '@message-hub/application/dto/in/incoming-message-request.dto';
import { CommunicationChannel, MessageType } from '@common/enums';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/sms')
export class SmsController {
  constructor(private readonly messsageUseCase: MessageUseCase) {}

  @Get()
  @Version('1')
  getHello(): string {
    return 'Hello from SMS Controller';
  }

  @Post('receive')
  @Version('1')
  async receiveSms(@Body() incomingMessageRequestDto: any) {
    await this.messsageUseCase.receiveMessage(
      new IncomingMessageRequestDto(
        incomingMessageRequestDto.msisdn,
        incomingMessageRequestDto.to,
        MessageType.TEXT,
        incomingMessageRequestDto.text,
        CommunicationChannel.SMS_VONAGE,
      ),
    );

    return {
      statusCode: 200,
      data: {
        success: true,
      },
    };
  }
}
