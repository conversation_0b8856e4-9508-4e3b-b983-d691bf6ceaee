import { Controller, Get, Param, Version } from '@nestjs/common';
import { OutgoingMessageUseCase } from '@message-hub/application/use-cases/outgoing-message.use-case';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { SystemAuthnGuard } from '@common/auth/system-authn.guard';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';

@ExcludeGuards(
  SystemAuthnGuard.name,
  AuthnGuard.name,
  AuthzAccountGuard.name,
  AuthzUserInAccountGuard.name,
)
@Controller('message-hub/outgoing-messages')
export class OutgoingMessageController {
  constructor(private readonly outgoingMessageUseCase: OutgoingMessageUseCase) {}

  @Get('/customer/:customerId/:to')
  @Version('1')
  async getOutgoingMessagesByCustomerId(
    @Param('customerId') customerId: string,
    @Param('to') to: string,
  ): Promise<any> {
    const messages = await this.outgoingMessageUseCase.getOutgoingMessagesByCustomerId(
      customerId,
      to,
    );

    return {
      statusCode: 200,
      data: messages,
    };
  }
}
