import { Body, Controller, Delete, Get, Param, Post, Version } from '@nestjs/common';
import { CustomerCommunicationChannelDto } from '@message-hub/application/dto/in/customer-communication-channel.dto';
import { CustomerCommunicationChannelUseCase } from '@message-hub/application/use-cases/customer-communication-channel.use-case';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('message-hub/communication-channel')
export class CustomerCommunicationChannelController {
  constructor(
    private readonly customerCommunicationChannelUseCase: CustomerCommunicationChannelUseCase,
  ) {}

  @Post()
  @Version('1')
  async createCustomerCommunicationChannel(
    @Body() customerCommunicationChannelDto: CustomerCommunicationChannelDto,
  ): Promise<any> {
    const createdCustomerCommunicationChannel =
      await this.customerCommunicationChannelUseCase.createCustomerCommunicationChannel(
        customerCommunicationChannelDto,
      );

    return {
      statusCode: 201,
      data: createdCustomerCommunicationChannel,
    };
  }

  @Get('/:customerCommunicationChannelId')
  @Version('1')
  async findById(
    @Param('customerCommunicationChannelId') customerCommunicationChannelId: string,
  ): Promise<any> {
    const customerCommunicationChannel =
      await this.customerCommunicationChannelUseCase.getCustomerCommunicationChannelById(
        customerCommunicationChannelId,
      );

    return {
      statusCode: 200,
      data: customerCommunicationChannel,
    };
  }

  @Get('/customers/:customerId')
  @Version('1')
  async findByCustomerId(@Param('customerId') customerId: string): Promise<any> {
    const customerCommunicationChannels =
      await this.customerCommunicationChannelUseCase.getCustomerCommunicationChannelsByCustomerId(
        customerId,
      );

    return {
      statusCode: 200,
      data: customerCommunicationChannels,
    };
  }

  @Delete('/:customerCommunicationChannelId')
  @Version('1')
  async deleteById(
    @Param('customerCommunicationChannelId') customerCommunicationChannelId: string,
  ): Promise<any> {
    const customerCommunicationChannel =
      await this.customerCommunicationChannelUseCase.deleteCustomerCommunicationChannelById(
        customerCommunicationChannelId,
      );

    return {
      statusCode: 200,
      data: customerCommunicationChannel,
    };
  }
}
