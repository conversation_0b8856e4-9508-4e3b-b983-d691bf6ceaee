import { FollowUpType, PortfolioItemStatus, RecordStatus } from '@common/enums';
import { IsUUID, IsNotEmpty, IsString, IsDate, IsEnum, IsBoolean } from 'class-validator';

export class PortfolioItemScheduledFollowUpEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsDate()
  @IsNotEmpty()
  readonly scheduledTo: Date;

  @IsDate()
  @IsNotEmpty()
  readonly doneAt: Date;

  @IsBoolean()
  @IsNotEmpty()
  readonly isDone: boolean;

  @IsEnum(FollowUpType)
  @IsNotEmpty()
  readonly type: FollowUpType;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    portfolioItemId: string,
    workflowId: string,
    scheduledTo: Date,
    doneAt: Date,
    isDone: boolean,
    type: FollowUpType,
    status: RecordStatus = RecordStatus.ACTIVE,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioItemId = portfolioItemId;
    this.workflowId = workflowId;
    this.scheduledTo = scheduledTo;
    this.doneAt = doneAt;
    this.type = type;
    this.isDone = isDone || false;
    this.status = status || RecordStatus.ACTIVE;
    this.createdAt = createdAt || new Date();
    this.updatedAt = updatedAt || new Date();
  }
}
