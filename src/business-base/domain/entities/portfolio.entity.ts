import {
  CommunicationChannel,
  PortfolioExecutionStatus,
  PortfolioImportStatus,
  RecordStatus,
} from '@common/enums';
import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';

export class PortfolioEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly name: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsEnum(PortfolioExecutionStatus)
  @IsNotEmpty()
  readonly executionStatus: PortfolioExecutionStatus;

  @IsEnum(PortfolioImportStatus)
  @IsNotEmpty()
  readonly importStatus: PortfolioImportStatus = PortfolioImportStatus.UPLOADED;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsString()
  readonly originalFileName: string;

  @IsString()
  @IsNotEmpty()
  readonly fileUrl: string;

  @IsString()
  @IsNotEmpty()
  readonly workExpression: string;

  @IsString()
  @IsNotEmpty()
  readonly followUpExpression: string = '0 11-23 * * 1-5';

  @IsUUID('4')
  @IsOptional()
  readonly followUpWorkflowId: string;

  @IsNumber()
  @IsNotEmpty()
  readonly followUpAfter: number = 24;

  @IsNumber()
  @IsNotEmpty()
  readonly maxFollowUps: number = 5;

  @IsNumber()
  @IsNotEmpty()
  readonly totalQuantity: number = 0;

  @IsNumber()
  @IsNotEmpty()
  readonly processedQuantity: number = 0;

  @IsNumber()
  @IsNotEmpty()
  readonly totalSuccessQuantity: number = 0;

  @IsNumber()
  @IsNotEmpty()
  readonly totalFailedQuantity: number = 0;

  @IsDate()
  readonly importFinishedAt?: Date;

  @IsBoolean()
  readonly executeImmediately: boolean = false;

  @IsNumber()
  @IsNotEmpty()
  readonly processingRateLimit: number = 0;

  @IsBoolean()
  @IsNotEmpty()
  readonly isDefault: boolean = false;

  @IsNumber()
  @IsOptional()
  readonly idleAfter?: number;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  @IsString()
  @IsOptional()
  readonly timezoneUTC?: string = '-3';

  constructor(
    id: string,
    name: string,
    customerId: string,
    workflowId: string,
    originalFileName: string,
    fileUrl: string,
    workExpression: string,
    status: RecordStatus,
    executeImmediately: boolean = false,
    processingRateLimit: number = 0,
    idleAfter: number,
    communicationChannel: CommunicationChannel,
    isDefalut: boolean = false,
    totalQuantity: number = 0,
    executionStatus: PortfolioExecutionStatus = PortfolioExecutionStatus.WAITING,
    importStatus: PortfolioImportStatus = PortfolioImportStatus.UPLOADED,
    processedQuantity: number = 0,
    totalSuccessQuantity: number = 0,
    totalFailedQuantity: number = 0,
    importFinishedAt?: Date,
    followUpWorkflowId?: string,
    followUpExpression?: string,
    followUpAfter?: number,
    maxFollowUps?: number,
    timezoneUTC?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.name = name;
    this.customerId = customerId;
    this.workflowId = workflowId;
    this.originalFileName = originalFileName;
    this.fileUrl = fileUrl;
    this.workExpression = workExpression;
    this.executeImmediately = executeImmediately;
    this.idleAfter = idleAfter;
    this.processingRateLimit = processingRateLimit;
    this.totalQuantity = totalQuantity;
    this.processedQuantity = processedQuantity;
    this.totalSuccessQuantity = totalSuccessQuantity;
    this.totalFailedQuantity = totalFailedQuantity;
    this.status = status;
    this.executionStatus = executionStatus;
    this.importStatus = importStatus;
    this.communicationChannel = communicationChannel;
    this.isDefault = isDefalut;
    this.importFinishedAt = importFinishedAt;
    this.followUpWorkflowId = followUpWorkflowId;
    this.followUpExpression = followUpExpression ?? this.followUpExpression;
    this.followUpAfter = followUpAfter ?? this.followUpAfter;
    this.maxFollowUps = maxFollowUps ?? this.maxFollowUps;
    this.timezoneUTC = timezoneUTC ?? this.timezoneUTC;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
