import { RecordStatus } from '@common/enums';
import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsEmail,
  IsPhoneNumber,
  IsDate,
  IsEnum,
} from 'class-validator';

export class CustomerEntity {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  cnpj: string;

  @IsString()
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('BR')
  phone: string;

  @IsString()
  @IsNotEmpty()
  @IsPhoneNumber('BR')
  //deprecated -> use customer whatsapp phones in message hub
  whatsappPhone: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsString()
  @IsNotEmpty()
  segment: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    name: string,
    cnpj: string,
    email: string,
    phone: string,
    whatsappPhone: string,
    status: RecordStatus,
    segment: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.name = name;
    this.cnpj = cnpj;
    this.email = email;
    this.phone = phone;
    this.whatsappPhone = whatsappPhone;
    this.status = status;
    this.segment = segment;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
