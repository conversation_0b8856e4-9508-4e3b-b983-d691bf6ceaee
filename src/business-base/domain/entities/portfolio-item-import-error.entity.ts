import { RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class PortfolioItemImportErrorEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioId: string;

  @IsNumber()
  @IsNotEmpty()
  readonly lineNumber: number;

  @IsString()
  @IsNotEmpty()
  readonly reason: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  readonly status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    portfolioId: string,
    lineNumber: number,
    reason: string,
    status: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioId = portfolioId;
    this.lineNumber = lineNumber;
    this.reason = reason;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
