import { IsDate, IsNotEmpty, IsUUID } from 'class-validator';

export class PortfolioItemCustomDataEntity {
  @IsUUID('4')
  readonly id: string;

  @IsUUID('4')
  readonly portfolioItemId: string;

  @IsNotEmpty()
  readonly customData: any;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    portfolioItemId: string,
    customData: any,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioItemId = portfolioItemId;
    this.customData = customData;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
