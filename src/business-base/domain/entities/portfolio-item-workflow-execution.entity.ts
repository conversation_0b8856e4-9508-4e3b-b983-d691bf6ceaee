import { RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class PortfolioItemWorkflowExecutionEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowExecutionId: string;

  readonly followFlowExecutionId?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  readonly status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    portfolioItemId: string,
    workflowExecutionId: string,
    status: RecordStatus = RecordStatus.ACTIVE,
    followFlowExecutionId?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.portfolioItemId = portfolioItemId;
    this.workflowExecutionId = workflowExecutionId;
    this.followFlowExecutionId = followFlowExecutionId;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
