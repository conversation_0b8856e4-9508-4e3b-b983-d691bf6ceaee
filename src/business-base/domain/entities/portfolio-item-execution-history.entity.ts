import { PortfolioItemStatus, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class PortfolioItemExecutionHistoryEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsEnum(PortfolioItemStatus)
  @IsNotEmpty()
  readonly oldStatus: PortfolioItemStatus;

  @IsEnum(PortfolioItemStatus)
  @IsNotEmpty()
  readonly newStatus: PortfolioItemStatus;

  @IsString()
  readonly reason?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  readonly status?: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    portfolioItemId: string,
    oldStatus: PortfolioItemStatus,
    newStatus: PortfolioItemStatus,
    status: RecordStatus,
    createdAt?: Date,
    reason?: string,
  ) {
    this.id = id;
    this.portfolioItemId = portfolioItemId;
    this.oldStatus = oldStatus;
    this.newStatus = newStatus;
    this.status = status;
    this.createdAt = createdAt;
    this.reason = reason;
  }
}
