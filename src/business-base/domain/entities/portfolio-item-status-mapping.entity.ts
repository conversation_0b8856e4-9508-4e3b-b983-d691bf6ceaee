import { PortfolioItemStatus, RecordStatus } from '@common/enums';
import { IsUUID, IsNotEmpty, IsString, IsDate, IsEnum } from 'class-validator';

export class PortfolioItemStatusMappingEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsString()
  @IsNotEmpty()
  readonly postExecutionResponse: string;

  @IsString()
  @IsNotEmpty()
  readonly responseKey: string;

  @IsEnum(PortfolioItemStatus)
  @IsNotEmpty()
  readonly portfolioItemStatus: PortfolioItemStatus;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    workflowId: string,
    postExecutionResponse: string,
    responsekey: string,
    portfolioItemStatus: PortfolioItemStatus,
    status?: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.workflowId = workflowId;
    this.postExecutionResponse = postExecutionResponse;
    this.portfolioItemStatus = portfolioItemStatus;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.responseKey = responsekey;
  }
}
