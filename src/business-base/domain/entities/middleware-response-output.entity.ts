import { IsDate, IsNotEmpty, IsUUID } from 'class-validator';

export class MiddlewareResponseOutputEntity {
  @IsUUID('4')
  readonly id: string;

  @IsUUID('4')
  readonly portfolioItemId: string;

  @IsNotEmpty()
  readonly data: any;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(id: string, portfolioItemId: string, data: any, createdAt?: Date, updatedAt?: Date) {
    this.id = id;
    this.portfolioItemId = portfolioItemId;
    this.data = data;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
