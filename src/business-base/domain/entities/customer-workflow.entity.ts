import { RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class CustomerWorkflowEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  readonly status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    workflowId: string,
    status: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.workflowId = workflowId;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
