import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { logWithContext } from '@common/utils/structured-logger.util';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { SQSService } from '@common/sqs/sqs.service';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { PortfolioJobExecutionUseCase } from '@business-base/application/use-cases/portfolio-job-execution.use-case';
import { BusinessException } from '@common/exception/types/BusinessException';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class PortfolioWorkflowExecutionUseCase {
  constructor(
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
    private readonly sqsService: SQSService,
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject(forwardRef(() => PortfolioJobExecutionUseCase))
    private readonly portfolioJobExecutionUseCase: PortfolioJobExecutionUseCase,
  ) { }

  async fetchAndQueuePendingPortfolioItems(fireDate: Date, portfolioId: string): Promise<any> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_JOB',
      `fetchPendingItems-${portfolioId}`,
      async () => {
        logWithContext(
          'info',
          `Starting job to fetch pending items for portfolio ${portfolioId}. Fired at: ${fireDate.toISOString()}`,
        );
        const portfolio = await this.portfolioAdapter.get(portfolioId);
        const customer = await this.customerAdapter.get(portfolio.customerId);

        if (!portfolio) {
          logWithContext(
            'error',
            `Portfolio ${portfolioId} not found. Unable to fetch pending items.`,
          );
          return;
        }

        if (!customer) {
          logWithContext(
            'error',
            `Customer ${portfolio.customerId} not found. Unable to fetch pending items.`,
          );
          return;
        }

        const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);

        await this.portfolioItemAdapter.fetchAndQueuePendingItems(
          portfolioId,
          portfolio.processingRateLimit,
          queueUrl,
          this.createBatchAndQueueItems.bind(this),
        );

        logWithContext('info', `Finished job to fetch pending items for portfolio ${portfolioId}`);
      },
    );
  }

  async fetchAndQueueFollowUpPortfolioItems(fireDate: Date, portfolioId: string): Promise<any> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_JOB',
      `fetchFollowUpItems-${portfolioId}`,
      async () => {
        logWithContext(
          'info',
          `Starting job to fetch followUp items for portfolio ${portfolioId}. Fired at: ${fireDate.toISOString()}`,
        );
        const portfolio = await this.portfolioAdapter.get(portfolioId);
        const customer = await this.customerAdapter.get(portfolio.customerId);

        if (!portfolio) {
          logWithContext(
            'error',
            `FetchAndQueueFollowUpPortfolioItems: Portfolio ${portfolioId} not found. Unable to fetch pending follow-up items.`,
          );
          return;
        }

        if (!customer) {
          logWithContext(
            'error',
            `FetchAndQueueFollowUpPortfolioItems: Customer ${portfolio.customerId} not found. Unable to fetch pending follow-up items.`,
          );
          return;
        }

        if (!portfolio.followUpWorkflowId) {
          logWithContext(
            'info',
            `FetchAndQueueFollowUpPortfolioItems: Portfolio ${portfolioId} does not have a follow-up workflow. Skipping...`,
          );
          return;
        }

        const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', customer.segment);

        logWithContext(
          'info',
          `Follow-up fetch for portfolio ${portfolioId} with rate limit: ${portfolio.processingRateLimit} and after ${portfolio.followUpAfter} minutes with max follow-ups: ${portfolio.maxFollowUps} times`,
        );

        await this.portfolioItemAdapter.fetchAndQueueFollowUpItems(
          portfolioId,
          portfolio.processingRateLimit,
          queueUrl,
          portfolio.followUpAfter,
          portfolio.maxFollowUps,
          this.createBatchAndQueueItems.bind(this),
        );

        logWithContext('info', `Finished job to fetch pending items for portfolio ${portfolioId}`);
      },
    );
  }
  //'*/1 * * * *'
  @Cron('*/30 8-20 * * 1-5', {
    timeZone: 'UTC',
    name: 'scheduled-follow-up-portfolio-items-task',
  })
  async fetchAndQueueScheduledFollowUpPortfolioItems(): Promise<any> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_JOB',
      `fetchScheduledFollowUpItems`,
      async () => {
        logWithContext(
          'info',
          `Starting job to fetch scheduled followUp items for all executing portfolios.`,
        );

        const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', 'collectcash');

        logWithContext(
          'info',
          `Scheduled follow-up fetch for all executing portfolios with rate limit: 100.`,
        );

        await this.portfolioItemAdapter.fetchAndQueueScheduledFollowUpItems(
          100,
          queueUrl,
          this.createBatchAndQueueItems.bind(this),
        );

        logWithContext(
          'info',
          `Finished job to fetch scheduled follow-up pending items for all executing portfolio.`,
        );
      },
    );
  }

  @Cron('*/30 8-14 * * 6', {
    timeZone: 'UTC',
    name: 'scheduled-follow-up-portfolio-items-weekend-task',
  })
  async fetchAndQueueScheduledFollowUpPortfolioItemsWeekend(): Promise<any> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_JOB',
      `fetchScheduledFollowUpItems`,
      async () => {
        logWithContext(
          'info',
          `Starting job to fetch scheduled followUp items for all executing portfolios.`,
        );

        const queueUrl = this.sqsService.getQueueByTypeAndSegment('WORKFLOW', 'collectcash');

        logWithContext(
          'info',
          `Scheduled follow-up fetch for all executing portfolios with rate limit: 100.`,
        );

        await this.portfolioItemAdapter.fetchAndQueueScheduledFollowUpItems(
          100,
          queueUrl,
          this.createBatchAndQueueItems.bind(this),
        );

        logWithContext(
          'info',
          `Finished job to fetch scheduled follow-up pending items for all executing portfolio.`,
        );
      },
    );
  }

  async createBatchAndQueueItems(
    portfolioItemsIds: { id: string }[],
    portfolioId: string,
    queueUrl: string,
    isFirstMessage: boolean = true,
    isFollowUp: boolean = false,
  ): Promise<void> {
    if (portfolioItemsIds.length === 0) {
      logger.info(`No pending items for portfolio ${portfolioId}`);
      await this.portfolioJobExecutionUseCase.stopPortfolioJob(portfolioId);
      return;
    }

    const sqsBatchSize = 10;
    const batches = [];

    while (portfolioItemsIds.length > 0) {
      const batch = portfolioItemsIds.splice(0, sqsBatchSize);
      batches.push(batch);
    }

    for (const batch of batches) {
      await this.createProcessBatch(portfolioId, batch, queueUrl, isFirstMessage, isFollowUp);
    }
  }

  private async createProcessBatch(
    portfolioId: string,
    batch: any,
    queueUrl: string,
    isFirstMessage: boolean = true,
    isFollowUp: boolean = false,
  ): Promise<void> {
    try {
      const ids = batch.map(item => item.id).join(', ');
      logger.info(
        `Sending batch to SQS for portfolio item workflow: ${ids}, isFirstMessage: ${isFirstMessage}, isFollowUp: ${isFollowUp}`,
      );

      const sqsEntries = batch.map(item => ({
        Id: `${portfolioId}-${item.id}`,
        MessageBody: JSON.stringify({
          portfolioId,
          portfolioItemId: item.id,
          isFirstMessage: isFirstMessage,
          isFollowUp: isFollowUp,
        }),
      }));

      await this.sqsService.produceBatch(process.env[queueUrl], sqsEntries);
    } catch (error) {
      logger.error(`Error sending batch to SQS queue: ${queueUrl}. Error ${JSON.stringify(error)}`);
      throw new BusinessException(
        'PortfolioWorkflowExecutionUseCase',
        `Error sending batch to SQS queue: ${queueUrl}. Error ${JSON.stringify(error)}`,
        error,
      );
    }
  }
}
