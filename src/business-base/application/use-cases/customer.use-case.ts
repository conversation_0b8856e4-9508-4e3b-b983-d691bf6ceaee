import { Inject, Injectable } from '@nestjs/common';
import { randomUUID as uuidv4 } from 'crypto';
import { CustomerDto } from '@business-base/application/dto/customer.dto';
import { UpdateCustomerDto } from '@business-base/application/dto/in/update-customer.dto';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { logger } from '@edutalent/commons-sdk';
import { CustomerEntity } from '@business-base/domain/entities/customer.entity';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { RecordStatus } from '@common/enums';
import { CustomerWorkflowPort } from '@business-base/infrastructure/ports/db/customer-workflow.port';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { CustomerWorkflowDto } from '@business-base/application/dto/customer-workflow.dto';
import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { SQSService } from '@common/sqs/sqs.service';

@Injectable()
export class CustomerUseCase {
  constructor(
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
    @Inject('CustomerWorkflowPort')
    private readonly customerWorkflowAdapter: CustomerWorkflowPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
    @Inject('InfraMessageHubPort')
    private readonly messageHubAdapter: InfraMessageHubPort,
    private readonly sqsService: SQSService,
  ) {}

  async create(createCustomerDto: CustomerDto): Promise<CustomerDto> {
    logger.info(`Creating customer with name: ${createCustomerDto.name}`);
    createCustomerDto.id = uuidv4();

    await this.customerAdapter.create(
      new CustomerEntity(
        createCustomerDto.id,
        createCustomerDto.name,
        createCustomerDto.cnpj,
        createCustomerDto.email,
        createCustomerDto.phone,
        createCustomerDto.whatsappPhone,
        RecordStatus.ACTIVE,
        createCustomerDto.segment,
      ),
    );

    return createCustomerDto;
  }

  async findById(customerId: string): Promise<CustomerDto> {
    logger.info(`Finding customer with id: ${customerId}`);
    const customer = await this.customerAdapter.get(customerId);

    if (!customer) {
      throw new BusinessException(
        'Customer-use-case',
        `Customer with id ${customerId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const customerPhones = await this.messageHubAdapter.getCustomerPhonesByCustomerId(customerId);

    const customerReturn = new CustomerDto(
      customer.name,
      customer.cnpj,
      customer.email,
      customer.phone,
      customer.whatsappPhone,
      customer.status,
      customer.segment,
      customerPhones,
    );
    customerReturn.id = customer.id;

    return customerReturn;
  }

  async update(customerId: string, updateCustomerDto: UpdateCustomerDto): Promise<CustomerDto> {
    logger.info(`Updating customer with id: ${customerId}`);
    const customerEntity = await this.customerAdapter.get(customerId);

    if (!customerEntity) {
      throw new BusinessException(
        'Customer-use-case',
        `Customer with id ${customerId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    if (updateCustomerDto.whatsappPhone) {
      throw new BusinessException(
        'Customer-use-case',
        `Whatsapp phone cannot be updated, use customer whatsapp phones in message hub.`,
        BusinessExceptionStatus.INVALID_INPUT,
      );
    }

    const updatedCustomerEntity = await this.customerAdapter.update(
      new CustomerEntity(
        customerId,
        updateCustomerDto.name,
        updateCustomerDto.cnpj,
        updateCustomerDto.email,
        updateCustomerDto.phone,
        updateCustomerDto.whatsappPhone,
        customerEntity.status,
        updateCustomerDto.segment,
      ),
    );

    const updatedCustomerDto = new CustomerDto(
      updatedCustomerEntity.name,
      updatedCustomerEntity.cnpj,
      updatedCustomerEntity.email,
      updatedCustomerEntity.phone,
      updatedCustomerEntity.whatsappPhone,
      updatedCustomerEntity.status,
      updatedCustomerEntity.segment,
    );
    updatedCustomerDto.id = updatedCustomerEntity.id;

    return updatedCustomerDto;
  }

  async delete(customerId: string): Promise<CustomerDto> {
    logger.info(`Deleting customer with id: ${customerId}`);
    const customer = await this.customerAdapter.get(customerId);

    if (!customer) {
      throw new BusinessException(
        'Customer-use-case',
        `Customer with id ${customerId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    await this.messageHubAdapter.deleteCustomerPhones(customerId);

    const deletedCustomerEntity = await this.customerAdapter.delete(customerId);
    const deletedCustomerReturn = new CustomerDto(
      deletedCustomerEntity.name,
      deletedCustomerEntity.cnpj,
      deletedCustomerEntity.email,
      deletedCustomerEntity.phone,
      deletedCustomerEntity.whatsappPhone,
      deletedCustomerEntity.status,
      deletedCustomerEntity.segment,
    );
    deletedCustomerReturn.id = deletedCustomerEntity.id;

    return deletedCustomerReturn;
  }

  async createCustomerWorkflows(
    customerId: string,
    workflowId: string,
  ): Promise<CustomerWorkflowDto> {
    logger.info(`Creating customer workflow: ${workflowId}. Customer id: ${customerId}`);
    const workflow = await this.workflowAdapter.getWorkflowById(workflowId);

    if (!workflow) {
      throw new BusinessException(
        'Customer-use-case: createCustomerWorkflows',
        `Workflow with id ${workflowId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const customerWorkflows = await this.customerWorkflowAdapter.create({
      id: uuidv4(),
      customerId,
      workflowId,
      status: RecordStatus.ACTIVE,
    });

    return new CustomerWorkflowDto(
      customerWorkflows.id,
      customerId,
      workflowId,
      workflow.name,
      workflow.description,
      customerWorkflows.createdAt,
      customerWorkflows.updatedAt,
    );
  }

  async findCustomerWorkflowsByCustomerId(customerId: string): Promise<CustomerWorkflowDto[]> {
    logger.info(`Finding customer workflows. Customer id: ${customerId}`);
    const customerWorkflows = await this.customerWorkflowAdapter.getAll({ customerId });

    const combinedWorkflows = await Promise.all(
      customerWorkflows.map(async customerWorkflow => {
        const workflow = await this.workflowAdapter.getWorkflowById(customerWorkflow.workflowId);
        return {
          ...customerWorkflow,
          workflowName: workflow.name,
          workflowDescription: workflow.description,
        };
      }),
    );

    return combinedWorkflows.map(
      cw =>
        new CustomerWorkflowDto(
          cw.id,
          cw.customerId,
          cw.workflowId,
          cw.workflowName,
          cw.workflowDescription,
          cw.createdAt,
          cw.updatedAt,
        ),
    );
  }
}
