import { <PERSON><PERSON> } from '@nestjs/schedule';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { Inject, Injectable } from '@nestjs/common';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioItemImportErrorPort } from '@business-base/infrastructure/ports/db/portfolio-item-import-error.port';
import { PortfolioExecutionStatus, PortfolioImportStatus } from '@common/enums';
import { logWithContext } from '@common/utils/structured-logger.util';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { ResponsePortfolioDto } from '@business-base/application/dto/out/response-portfolio.dto';

@Injectable()
export class PortfolioJobUpdateImportDataUseCase {
  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
    @Inject('PortfolioItemImportErrorPort')
    private readonly portfolioItemImportErrorAdapter: PortfolioItemImportErrorPort,
    private readonly portfolioUseCase: PortfolioUseCase,
  ) {}

  @Cron('*/10 * * * * *', {
    timeZone: 'UTC',
    name: 'every-minute-update-portfolio-import-data-task',
  })
  private async startJob(): Promise<void> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_CRON',
      'updatePortfolioImportData',
      async () => {
        try {
          logWithContext('debug', `Finding all portfolios importing`);
          const portfolios = await this.portfolioUseCase.findAllImporting();
          logWithContext(
            'info',
            `Found ${portfolios.length} portfolios to update import data: ${portfolios.map(
              portfolio => portfolio.id,
            )}`,
          );
          await Promise.all(portfolios.map(portfolio => this.processPortfolio(portfolio)));
        } catch (error) {
          logWithContext('error', 'Error fetching portfolios', {
            error: error.message,
            stack: error.stack,
          });
          return;
        }
      },
    );
  }

  private async processPortfolio(portfolio: ResponsePortfolioDto): Promise<void> {
    try {
      logWithContext('info', `Update portfolio: ${portfolio.id} import status`);
      const [totalSuccessQuantity, totalFailedQuantity] = await Promise.all([
        this.portfolioItemAdapter.countByPortfolioId(portfolio.id),
        this.portfolioItemImportErrorAdapter.countByPortfolioId(portfolio.id),
      ]);

      const processedQuantity = totalSuccessQuantity + totalFailedQuantity;
      const isFinishImport = portfolio.totalQuantity == processedQuantity;

      await this.portfolioUseCase.updateImportData({
        id: portfolio.id,
        totalFailedQuantity,
        totalSuccessQuantity,
        processedQuantity,
      });

      if (isFinishImport) {
        await this.portfolioUseCase.updateImportData({
          id: portfolio.id,
          importStatus: PortfolioImportStatus.SUCCESS,
          importFinishedAt: new Date(),
        });

        if (!portfolio.executeImmediately) {
          await this.portfolioUseCase.updateExecutionStatus(
            portfolio.id,
            PortfolioExecutionStatus.INBOUND,
          );
        }
      }
    } catch (error) {
      logWithContext('error', `Error processing portfolio ${portfolio.id}`, {
        error: error.message,
        stack: error.stack,
      });
    }
  }
}
