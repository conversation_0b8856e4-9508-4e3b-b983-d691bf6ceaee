import { Inject, Injectable } from '@nestjs/common';
import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { Cron } from '@nestjs/schedule';
import { PortfolioItemStatus } from '@common/enums';
import { logWithContext } from '@common/utils/structured-logger.util';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class PortfolioItemJobUpdateIdleUseCase {
  constructor(
    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,
  ) {}

  @Cron('59 23 * * *', {
    timeZone: 'UTC',
    name: 'every-day-update-portfolio-itens-to-idle-task',
  })
  private async startJob(): Promise<void> {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_CRON',
      'updatePortfolioItemsToIdle',
      async () => {
        try {
          const date = new Date();
          const batchSize = 1000;
          let hasMoreItems = true;

          while (hasMoreItems) {
            const quantityUpdated = await this.portfolioItemAdapter.updateBatchToIdle(
              date,
              batchSize,
            );

            logWithContext(
              'info',
              `Atualizados ${quantityUpdated} itens para o status: ${PortfolioItemStatus.IDLE}.`,
            );

            if (quantityUpdated === 0) {
              hasMoreItems = false;
            }
          }
        } catch (error) {
          logWithContext('error', 'Error fetching pending portfolio itens for update to idle', {
            error: error.message,
            stack: error.stack,
          });
          return;
        }
      },
    );
  }
}
