import { AvailableColumn } from '@business-base/application/dto/available-export-column.dto';

export const portfolioItemColumns: AvailableColumn[] = [
  { entity: 'portfolioItem', field: 'id', displayName: 'Portfolio Item ID' },
  { entity: 'portfolioItem', field: 'portfolioId', displayName: 'Portfolio ID' },
  { entity: 'portfolioItem', field: 'currentStatus', displayName: 'Status' },
  { entity: 'portfolioItem', field: 'phoneNumber', displayName: 'Phone Number' },
  { entity: 'portfolioItem', field: 'customDataId', displayName: 'Custom Data ID' },
  {
    entity: 'portfolioItem',
    field: 'middlewareResponseOutputId',
    displayName: 'Middleware Response ID',
  },
  { entity: 'portfolioItem', field: 'line', displayName: 'Line Number' },
  { entity: 'portfolioItem', field: 'lastInteraction', displayName: 'Last Interaction' },
  { entity: 'portfolioItem', field: 'lastMessageSentAt', displayName: 'Last Message Sent At' },
  { entity: 'portfolioItem', field: 'lastFollowUpAt', displayName: 'Last Follow-Up At' },
  {
    entity: 'portfolioItem',
    field: 'waitingBusinessUserResponse',
    displayName: 'Waiting Business User Response',
  },
  { entity: 'portfolioItem', field: 'followUpCount', displayName: 'Follow-Up Count' },
  { entity: 'portfolioItem', field: 'createdAt', displayName: 'Created At' },
  { entity: 'portfolioItem', field: 'updatedAt', displayName: 'Updated At' },
];

export const portfolioColumns: AvailableColumn[] = [
  { entity: 'portfolio', field: 'name', displayName: 'Portfolio Name' },
  { entity: 'portfolio', field: 'status', displayName: 'Portfolio Status' },
  { entity: 'portfolio', field: 'customerId', displayName: 'Customer ID' },
  { entity: 'portfolio', field: 'workflowId', displayName: 'Workflow ID' },
  { entity: 'portfolio', field: 'executionStatus', displayName: 'Execution Status' },
  { entity: 'portfolio', field: 'importStatus', displayName: 'Import Status' },
  { entity: 'portfolio', field: 'communicationChannel', displayName: 'Communication Channel' },
  { entity: 'portfolio', field: 'originalFileName', displayName: 'Original File Name' },
  { entity: 'portfolio', field: 'fileUrl', displayName: 'File URL' },
  { entity: 'portfolio', field: 'workExpression', displayName: 'Work Expression' },
  { entity: 'portfolio', field: 'followUpExpression', displayName: 'Follow-Up Expression' },
  { entity: 'portfolio', field: 'followUpWorkflowId', displayName: 'Follow-Up Workflow ID' },
  { entity: 'portfolio', field: 'followUpAfter', displayName: 'Follow-Up After' },
  { entity: 'portfolio', field: 'maxFollowUps', displayName: 'Max Follow-Ups' },
  { entity: 'portfolio', field: 'totalQuantity', displayName: 'Total Quantity' },
  { entity: 'portfolio', field: 'processedQuantity', displayName: 'Processed Quantity' },
  { entity: 'portfolio', field: 'totalSuccessQuantity', displayName: 'Total Success Quantity' },
  { entity: 'portfolio', field: 'totalFailedQuantity', displayName: 'Total Failed Quantity' },
  { entity: 'portfolio', field: 'importFinishedAt', displayName: 'Import Finished At' },
  { entity: 'portfolio', field: 'executeImmediately', displayName: 'Execute Immediately' },
  { entity: 'portfolio', field: 'processingRateLimit', displayName: 'Processing Rate Limit' },
  { entity: 'portfolio', field: 'isDefault', displayName: 'Is Default' },
  { entity: 'portfolio', field: 'idleAfter', displayName: 'Idle After' },
  { entity: 'portfolio', field: 'createdAt', displayName: 'Portfolio Created At' },
  { entity: 'portfolio', field: 'updatedAt', displayName: 'Portfolio Updated At' },
];
