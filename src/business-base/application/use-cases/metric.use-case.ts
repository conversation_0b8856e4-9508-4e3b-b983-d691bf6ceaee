import { PortfolioItemPort } from '@business-base/infrastructure/ports/db/portfolio-item.port';
import { PortfolioPort } from '@business-base/infrastructure/ports/db/portfolio.port';
import { GroupByDate, PortfolioItemStatus } from '@common/enums';
import { Inject } from '@nestjs/common';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';

export class MetricUseCase {
  constructor(
    @Inject('PortfolioPort')
    private readonly portfolioAdapter: PortfolioPort,

    @Inject('PortfolioItemPort')
    private readonly portfolioItemAdapter: PortfolioItemPort,

    private readonly portfolioItemUseCase: PortfolioItemUseCase,
  ) {}

  async getPortfolioCreatedMetrics(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    startDate = new Date(startDate);
    startDate.setUTCHours(0, 0, 0, 0);

    endDate = new Date(endDate);
    endDate.setUTCHours(23, 59, 59, 999);

    const portfolios = await this.portfolioAdapter.count({
      customerId: customerId,
      createdAt: {
        gte: startDate.toISOString(),
        lte: endDate.toISOString(),
      },
    });

    const metrics = {
      portfoliosCreated: portfolios,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };

    return metrics;
  }

  async getPortfolioItemCreatedMetrics(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<any> {
    startDate = new Date(startDate);
    startDate.setUTCHours(0, 0, 0, 0);

    endDate = new Date(endDate);
    endDate.setUTCHours(23, 59, 59, 999);

    const customerPortfolios = await this.portfolioAdapter.getAll({
      customerId: customerId,
      createdAt: {
        gte: startDate.toISOString(),
        lte: endDate.toISOString(),
      },
    });

    const customerPortfoliosIds = customerPortfolios.map(portfolio => portfolio.id);

    const portfolioItems = await this.portfolioItemAdapter.count({
      portfolioId: { in: customerPortfoliosIds },
      createdAt: {
        gte: startDate.toISOString(),
        lte: endDate.toISOString(),
      },
    });

    const metrics = {
      portfolioItemsCreated: portfolioItems,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };

    return metrics;
  }

  async getPortfolioItemsCountByDateFilteredByStatus(
    customerId: string,
    startDate: Date,
    endDate: Date,
    GroupByDate: GroupByDate,
    currentStatus: PortfolioItemStatus,
  ): Promise<any> {
    const portfolioItems =
      await this.portfolioItemUseCase.getPortfolioItemsCountByDateFilteredByStatus(
        customerId,
        startDate,
        endDate,
        GroupByDate,
        currentStatus,
      );
    return portfolioItems;
  }

  async getPortfolioItemsWithInteractionCountByDate(
    customerId: string,
    startDate: Date,
    endDate: Date,
    GroupByDate: GroupByDate,
  ): Promise<any> {
    const portfolioItems =
      await this.portfolioItemUseCase.getPortfolioItemsWithInteractionCountByDate(
        customerId,
        startDate,
        endDate,
        GroupByDate,
      );
    return portfolioItems;
  }

  async getPortfolioItemsWithOnlyAiInteractionCountByDate(
    customerId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    const portfolioItemsCount =
      await this.portfolioItemUseCase.getPortfolioItemsWithAiOnlyInteractionCountByDate(
        customerId,
        startDate,
        endDate,
      );
    return portfolioItemsCount;
  }
}
