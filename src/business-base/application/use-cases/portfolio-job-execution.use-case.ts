import { <PERSON><PERSON> } from '@nestjs/schedule';
import * as schedule from 'node-schedule';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { PortfolioExecutionStatus } from '@common/enums';
import { logger } from '@edutalent/commons-sdk';
import { ResponsePortfolioDto } from '@business-base/application/dto/out/response-portfolio.dto';
import { PortfolioWorkflowExecutionUseCase } from '@business-base/application/use-cases/portfolio-workflow-execution.use-case';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class PortfolioJobExecutionUseCase {
  constructor(
    private readonly portfolioUseCase: PortfolioUseCase,
    @Inject(forwardRef(() => PortfolioWorkflowExecutionUseCase))
    private readonly portfolioWorkflowExecutionUseCase: PortfolioWorkflowExecutionUseCase,
  ) {}

  @Cron('*/60 * * * * *', {
    timeZone: 'UTC',
    name: 'every-minute-start-or-stop-portfolios-task',
  })
  private async startJob() {
    return CorrelationContextService.runBackgroundJob(
      'PORTFOLIO_CRON',
      'startStopPortfolios',
      async () => {
        try {
          const portfoliosToStart = await this.portfolioUseCase.findAllQueuedToStart();
          const portfoliosToStop =
            await this.portfolioUseCase.findAllPausedOrCancelledOrFinishedToStop();
          // Process portfolios sequentially to avoid connection pool exhaustion
          for (const portfolio of portfoliosToStart) {
            await this.startPortfolioJob(portfolio);
          }
          for (const portfolio of portfoliosToStop) {
            await this.stopPortfolioJob(portfolio.id);
          }
        } catch (error) {
          logger.error('Error fetching portfolios', error);
          return;
        }
      },
    );
  }

  public async startPortfolioJob(portfolio: ResponsePortfolioDto): Promise<void> {
    const portfolioJob = schedule.scheduledJobs[portfolio.id];

    if (portfolioJob) {
      logger.info(`Portfolio: ${portfolio.id} already running.`);
      return;
    }

    try {
      logger.info(
        `Starting portfolio ${portfolio.id} with cron expression: ${portfolio.workExpression}.`,
      );

      const job = schedule.scheduleJob(
        portfolio.id,
        portfolio.workExpression,
        async (fireDate: Date) => {
          await this.portfolioWorkflowExecutionUseCase.fetchAndQueuePendingPortfolioItems(
            fireDate,
            portfolio.id,
          );
        },
      );

      const followUpJob = schedule.scheduleJob(
        `${portfolio.id}-follow-up`,
        portfolio.followUpExpression,
        async (fireDate: Date) => {
          await this.portfolioWorkflowExecutionUseCase.fetchAndQueueFollowUpPortfolioItems(
            fireDate,
            portfolio.id,
          );
        },
      );

      logger.info(
        `Portfolio follow-up job: ${portfolio.id} scheduled with cron expression: ${
          portfolio.followUpExpression
        } and will start running at: ${followUpJob.nextInvocation()}`,
      );

      await this.portfolioUseCase.updateExecutionStatus(
        portfolio.id,
        PortfolioExecutionStatus.EXECUTING,
      );

      logger.info(
        `Portfolio: ${portfolio.id} scheduled with cron expression: ${
          portfolio.workExpression
        } and will start running at: ${job.nextInvocation()}`,
      );
    } catch (error) {
      logger.error(
        `Error starting portfolio ${portfolio.id} for workflow execution. Error: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }

  public async stopPortfolioJob(portfolioId: string): Promise<void> {
    const portfolioJob = schedule.scheduledJobs[portfolioId];

    if (!portfolioJob) {
      return;
    }

    try {
      logger.info(`Stoping portfolio: ${portfolioId} job.`);
      portfolioJob.cancel();
      logger.info(`Portfolio: ${portfolioId} stopped.`);
      if (schedule.scheduledJobs[`${portfolioId}-follow-up`]) {
        logger.info(`Stoping portfolio: ${portfolioId}-follow-up job.`);
        schedule.scheduledJobs[`${portfolioId}-follow-up`].cancel();
        logger.info(`Portfolio: ${portfolioId}-follow-up stopped.`);
      }
    } catch (error) {
      logger.error(
        `Error stopping portfolio ${portfolioId} for workflow execution. Error: ${JSON.stringify(
          error,
        )}`,
      );
    }
  }
}
