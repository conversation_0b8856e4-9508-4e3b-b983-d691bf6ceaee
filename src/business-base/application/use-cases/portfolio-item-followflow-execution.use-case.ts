import { logger } from '@edutalent/commons-sdk';
import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { PortfolioItemWorkflowExecutionPort } from '@business-base/infrastructure/ports/db/portfolio-item-worflow-execution.port';
import { InfraWorkflowPort } from '@business-base/infrastructure/ports/http/workflow.port';
import { InfraMessageHubPort } from '@business-base/infrastructure/ports/http/message-hub.port';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import {
  MessageType,
  PortfolioExecutionStatus,
  PortfolioItemStatus,
  RoleType,
} from '@common/enums';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { ResponsePortfolioItemDto } from '@business-base/application/dto/out/response-portfolio-item.dto';
import { PortfolioItemCustomDataPort } from '@business-base/infrastructure/ports/db/portfolio-item-custom-data.port';
import { BusinessException } from '@common/exception/types/BusinessException';
import { MiddlewareResponseOutputPort } from '@business-base/infrastructure/ports/db/middleware-response-output.port';

@Injectable()
export class PortfolioItemFollowFlowExecutionUseCase {
  constructor(
    @Inject('PortfolioItemWorkflowExecutionPort')
    private readonly portfolioItemWorkflowExecutionAdapter: PortfolioItemWorkflowExecutionPort,
    @Inject('PortfolioItemCustomDataPort')
    private readonly portfolioItemCustomDataAdapter: PortfolioItemCustomDataPort,
    @Inject('MiddlewareResponseOutputPort')
    private readonly middlewareResponseOutputAdapter: MiddlewareResponseOutputPort,
    @Inject('InfraWorkflowPort')
    private readonly workflowAdapter: InfraWorkflowPort,
    @Inject('InfraMessageHubPort')
    private readonly messageHubAdapter: InfraMessageHubPort,
    @Inject(forwardRef(() => PortfolioItemUseCase))
    private readonly portfolioItemUseCase: PortfolioItemUseCase,
    @Inject(forwardRef(() => PortfolioUseCase))
    private readonly portfolioUseCase: PortfolioUseCase,
  ) {}

  async processFollowFlowExecution(portfolioId: string, portfolioItemId: string): Promise<void> {
    try {
      const [portfolio, portfolioItem] = await Promise.all([
        this.portfolioUseCase.findByIdInternal(portfolioId),
        this.portfolioItemUseCase.findById(portfolioItemId),
        this.portfolioItemWorkflowExecutionAdapter.getAll({
          portfolioItemId,
        }),
      ]);

      //Check if the workflow can be executed because the portfolio item could be cancelled after the import
      if (
        !this.canExecuteFollowFlow(portfolio.executionStatus, portfolioItem.currentStatus) &&
        portfolioItem.followUpCount <= portfolio.maxFollowUps
      ) {
        logger.info(
          `ProcessWorkflowExecution - portofolio item ${portfolioItemId} cannot be executed because of the its status or of the portfolio status`,
        );
        return;
      }

      const workflowExecutionId = await this.startFollowFlowExecution(
        portfolioItem,
        portfolio.followUpWorkflowId,
      );

      await this.executeExistingFollowFlow(portfolioItem, workflowExecutionId);
    } catch (error) {
      logger.error(
        `Error processing workflow execution for item: ${portfolioItemId}. Error: ${JSON.stringify(
          error,
        )}`,
      );
      throw new BusinessException(
        'PortfolioItemFollowFlowExecutionUseCase',
        'executeExistingFollowFlow',
        error,
      );
    }
  }

  private async startFollowFlowExecution(
    portfolioItem: ResponsePortfolioItemDto,
    workflowId: string,
  ): Promise<string> {
    const portfolioItemId = portfolioItem.id;
    logger.info(`Starting workflow for portfolioItem ${portfolioItemId}`);
    const workflowExecution = await this.workflowAdapter.startWorkflow(workflowId);
    const [currentPortfolioItemWorkflowExecution] =
      await this.portfolioItemWorkflowExecutionAdapter.getAll({ portfolioItemId: portfolioItemId });

    await this.portfolioItemWorkflowExecutionAdapter.update({
      ...currentPortfolioItemWorkflowExecution,
      followFlowExecutionId: workflowExecution.workflowExecutionId,
    });

    return workflowExecution.workflowExecutionId;
  }

  private async executeExistingFollowFlow(
    portfolioItem: ResponsePortfolioItemDto,
    workflowExecutionId: string,
  ): Promise<void> {
    try {
      logger.info(
        `ExecuteExistingFollowFlow - starting execution id: ${workflowExecutionId} and portfolioItemId: ${portfolioItem.id}`,
      );

      const currentConversationHistory =
        await this.portfolioItemUseCase.findConversationHistoryById(portfolioItem.id);

      const followFlowExecutionResponse = await this.workflowAdapter.executeWorkflow({
        lang: 'en',
        messageType: MessageType.TEXT,
        params: { conversationHistory: currentConversationHistory, DATA_ATUAL: new Date() },
        workflowExecutionId,
      });

      const messageString = Buffer.from(followFlowExecutionResponse.output).toString('utf-8');

      logger.info(
        `ExecuteExistingFollowFlow -  response string for ${workflowExecutionId} and item:${portfolioItem.id}: ${messageString}`,
      );

      if (!this.shouldSkipSendMessage(followFlowExecutionResponse)) {
        await this.portfolioItemUseCase.sendDirectMessage(portfolioItem.id, {
          messageType: MessageType.TEXT,
          message: messageString,
          roleType: RoleType.ASSISTANT,
        });
      } else {
        logger.info(
          `ExecuteExistingFollowFlow -  discarding message for ${workflowExecutionId} and item:${portfolioItem.id}`,
        );

        await this.portfolioItemUseCase.updateItemCurrentStatus(
          portfolioItem.id,
          PortfolioItemStatus.FINISHED,
        );
      }
    } catch (error) {
      logger.error(
        `ExecuteExistingFollowFlow -  Error finishing followFlow execution for item: ${
          portfolioItem.id
        }. Error: ${JSON.stringify(error)}`,
      );

      throw new BusinessException(
        'PortfolioItemFollowFlowExecutionUseCase',
        'executeExistingFollowFlow',
        error,
      );
    }
  }

  private shouldSkipSendMessage(followFlowExecutionResponse: any): boolean {
    return (
      followFlowExecutionResponse.middlewaresResponse &&
      followFlowExecutionResponse.middlewaresResponse['skip-send-message'] &&
      followFlowExecutionResponse.middlewaresResponse['skip-send-message'].value === true
    );
  }

  private canExecuteFollowFlow(
    portfolioExecutionStatus: PortfolioExecutionStatus,
    portfolioItemStatus: PortfolioItemStatus,
  ): boolean {
    return (
      portfolioExecutionStatus !== PortfolioExecutionStatus.PAUSED &&
      portfolioExecutionStatus !== PortfolioExecutionStatus.CANCELLED &&
      portfolioItemStatus !== PortfolioItemStatus.UNLINKED &&
      portfolioItemStatus !== PortfolioItemStatus.CANCELLED &&
      portfolioItemStatus !== PortfolioItemStatus.PAUSED &&
      portfolioItemStatus !== PortfolioItemStatus.FAILED &&
      portfolioItemStatus !== PortfolioItemStatus.OPTED_OUT &&
      portfolioItemStatus !== PortfolioItemStatus.FINISHED
    );
  }
}
