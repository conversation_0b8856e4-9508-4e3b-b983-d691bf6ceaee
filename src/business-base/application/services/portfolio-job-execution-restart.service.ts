import { logger } from '@edutalent/commons-sdk';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { PortfolioJobExecutionUseCase } from '@business-base/application/use-cases/portfolio-job-execution.use-case';

@Injectable()
export class PortfolioJobExecutionRestartSerivce implements OnModuleInit {
  constructor(
    private readonly portfolioUseCase: PortfolioUseCase,
    private readonly portfolioJobExecutionUseCase: PortfolioJobExecutionUseCase,
  ) {}

  async onModuleInit() {
    logger.info('Restarting executing portfolio jobs...');

    const portfoliosToRestart = await this.portfolioUseCase.findAllExecutingToRestart();
    // Process portfolios sequentially to avoid connection pool exhaustion
    for (const portfolio of portfoliosToRestart) {
      await this.portfolioJobExecutionUseCase.startPortfolioJob(portfolio);
    }
  }
}
