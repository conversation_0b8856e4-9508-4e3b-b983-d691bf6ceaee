import { Message } from '@aws-sdk/client-sqs';
import { SQSService } from '@common/sqs/sqs.service';
import { logger, SlackMessage } from '@edutalent/commons-sdk';
import { Inject, Injectable, OnModuleInit } from '@nestjs/common';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { sanitizeMessageBody } from '@common/utils/message-sanitization.util';
import { randomUUID as uuidv4 } from 'crypto';
import { PortfolioUseCase } from '@business-base/application/use-cases/portfolio.use-case';
import { PortfolioItemWorkflowExecutionUseCase } from '@business-base/application/use-cases/portfolio-item-workflow-execution.use-case';
import { PortfolioItemFollowFlowExecutionUseCase } from '@business-base/application/use-cases/portfolio-item-followflow-execution.use-case';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';

@Injectable()
export class SQSConsumerService implements OnModuleInit {
  private readonly slackApiKey: string;
  //TODO: We should use an environment variable for this in the future.
  private readonly maxBatchSize = 10;
  private readonly defaultConsumerCount = 1;
  private readonly workflowConsumerCount = 3; // Multiple consumers for higher throughput

  constructor(
    private readonly sqsService: SQSService,
    private readonly portfolioUseCase: PortfolioUseCase,
    private readonly portfolioItemWorkflowExecutionUseCase: PortfolioItemWorkflowExecutionUseCase,
    private readonly portfolioItemFollowFlowExecutionUseCase: PortfolioItemFollowFlowExecutionUseCase,
    private readonly slackMessage: SlackMessage,
    @Inject('CustomerPort')
    private readonly customerAdapter: CustomerPort,
  ) {
    this.slackApiKey = process.env.SLACK_TOKEN;
  }

  async onModuleInit() {
    logger.info('Starting SQS consumers for business_base module...');
    const activeCustomers = await this.customerAdapter.getAll();
    const queueConsumerFunctionMap = new Map<string, (messages: Message[]) => void>();

    activeCustomers.forEach(customer => {
      queueConsumerFunctionMap.set(
        this.sqsService.getImportItemQueueByCustomer(customer.id),
        this.portfolioItemConsumer.bind(this),
      );
    });

    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_IMPORT_FIDELEASY_QUEUE_URL,
      this.portfolioConsumer.bind(this),
    );
    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_IMPORT_SALESZAP_QUEUE_URL,
      this.portfolioConsumer.bind(this),
    );
    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL,
      this.portfolioConsumer.bind(this),
    );
    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_WORKFLOW_FIDELEASY_QUEUE_URL,
      this.portfolioItemWorkflowExecutionConsumer.bind(this),
    );
    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_WORKFLOW_SALESZAP_QUEUE_URL,
      this.portfolioItemWorkflowExecutionConsumer.bind(this),
    );
    queueConsumerFunctionMap.set(
      process.env.PORTFOLIO_WORKFLOW_COLLECTCASH_QUEUE_URL,
      this.portfolioItemWorkflowExecutionConsumer.bind(this),
    );

    for (const [queueUrl, handler] of queueConsumerFunctionMap.entries()) {
      try {
        // Create multiple consumer instances for workflow queues to increase throughput
        const consumerCount = queueUrl.includes('PORTFOLIO_WORKFLOW_FIDELEASY_QUEUE_URL')
          ? this.workflowConsumerCount
          : this.defaultConsumerCount;

        for (let i = 0; i < consumerCount; i++) {
          // Add random delay between consumer instances to prevent polling conflicts
          const randomDelay = Math.floor(Math.random() * 1000) + i * 500; // 0-1000ms + staggered delay

          setTimeout(() => {
            this.sqsService.createConsumer(queueUrl, this.maxBatchSize, handler);
            logger.info(
              `Created consumer instance ${
                i + 1
              }/${consumerCount} for queue: ${queueUrl} with ${randomDelay}ms delay`,
            );
          }, randomDelay);
        }
      } catch (error) {
        logger.error(
          `Error creating consumer for queue: ${queueUrl}. Error: ${JSON.stringify(error)}`,
        );
      }
    }
  }

  private async portfolioConsumer(messages: Message[]): Promise<void> {
    const batchStartTime = Date.now();
    const batchCorrelationId = uuidv4();

    logger.info('SQS portfolio consumer batch started', {
      correlationId: batchCorrelationId,
      operation: 'portfolioConsumer',
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    for (const message of messages) {
      const messageStartTime = Date.now();
      const messageCorrelationId = uuidv4();

      try {
        const { Body: body, MessageId } = message;

        logger.info('Processing SQS portfolio message', {
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioConsumer',
          messageId: MessageId,
          messageSize: body?.length || 0,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });

        const data = JSON.parse(body);
        const { fileKey, portfolioId } = data;

        logger.debug('Portfolio message data parsed', {
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioConsumer',
          fileKey,
          portfolioId,
          layer: 'SQS_CONSUMER',
        });

        // Run portfolio processing in correlation context
        await CorrelationContextService.run(
          { traceId: messageCorrelationId, layer: 'SQS_CONSUMER', operation: 'portfolioConsumer' },
          async () => {
            await this.portfolioUseCase.processPortfolioImported(fileKey, portfolioId);
          },
        );

        const messageDuration = Date.now() - messageStartTime;

        logger.info('SQS portfolio message processed successfully', {
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioConsumer',
          messageId: MessageId,
          fileKey,
          portfolioId,
          duration: `${messageDuration}ms`,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      } catch (error) {
        const messageDuration = Date.now() - messageStartTime;

        logger.error('SQS portfolio message processing failed', {
          traceId: messageCorrelationId, // Use messageCorrelationId as traceId for consistency
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioConsumer',
          messageId: message.MessageId,
          duration: `${messageDuration}ms`,
          error: error.message,
          errorType: error.constructor?.name,
          severity: 'HIGH',
          retryable: true,
          stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
          messageBody: message.Body,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      }
    }

    const batchDuration = Date.now() - batchStartTime;

    logger.info('SQS portfolio consumer batch completed', {
      correlationId: batchCorrelationId,
      operation: 'portfolioConsumer',
      batchSize: messages.length,
      duration: `${batchDuration}ms`,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });
  }

  private async portfolioItemConsumer(messages: Message[]): Promise<void> {
    const batchStartTime = Date.now();
    const batchCorrelationId = uuidv4();

    logger.info('SQS portfolio item consumer batch started', {
      correlationId: batchCorrelationId,
      operation: 'portfolioItemConsumer',
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    try {
      for (const message of messages) {
        const messageStartTime = Date.now();
        const messageCorrelationId = uuidv4();

        try {
          const { Body: body, MessageId } = message;

          logger.info('Processing SQS portfolio item message', {
            correlationId: messageCorrelationId,
            batchCorrelationId,
            operation: 'portfolioItemConsumer',
            messageId: MessageId,
            messageSize: body?.length || 0,
            timestamp: new Date().toISOString(),
            layer: 'SQS_CONSUMER',
          });

          const data = JSON.parse(body);
          const { line, portfolioId } = data;
          const portfolioItem = Object.fromEntries(
            Object.entries(data.portfolioItem).map(([key, value]) => [key.trim(), value]),
          );

          // Use portfolioItemId as primary trace identifier for business correlation
          const portfolioItemId = portfolioItem?.id;
          const traceId = (portfolioItemId || messageCorrelationId) as string; // Fallback to UUID if no portfolioItemId

          logger.info('Portfolio item message data parsed', {
            traceId,
            correlationId: messageCorrelationId,
            batchCorrelationId,
            operation: 'portfolioItemConsumer',
            portfolioId,
            line,
            portfolioItemId,
            messageBody: sanitizeMessageBody(data),
            layer: 'SQS_CONSUMER',
          });

          // Run portfolio item processing in correlation context with portfolioItemId as traceId
          await CorrelationContextService.run(
            {
              traceId,
              layer: 'SQS_CONSUMER',
              operation: 'portfolioItemConsumer',
            },
            async () => {
              await this.portfolioUseCase.processPortfolioItem(portfolioId, line, portfolioItem);
            },
          );

          const messageDuration = Date.now() - messageStartTime;

          logger.info('SQS portfolio item message processed successfully', {
            traceId,
            correlationId: messageCorrelationId,
            batchCorrelationId,
            operation: 'portfolioItemConsumer',
            messageId: MessageId,
            portfolioId,
            line,
            portfolioItemId,
            duration: `${messageDuration}ms`,
            timestamp: new Date().toISOString(),
            layer: 'SQS_CONSUMER',
          });
        } catch (messageError) {
          const messageDuration = Date.now() - messageStartTime;

          // Extract portfolioItemId for error logging even if parsing failed
          let portfolioItemId: string | undefined;
          let traceId: string = messageCorrelationId; // Default fallback
          try {
            const data = JSON.parse(message.Body);
            portfolioItemId = data.portfolioItem?.id;
            traceId = (portfolioItemId || messageCorrelationId) as string;
          } catch {
            // If parsing fails, use messageCorrelationId as traceId
          }

          logger.error('SQS portfolio item message processing failed', {
            traceId, // Use portfolioItemId as primary trace identifier
            correlationId: messageCorrelationId,
            batchCorrelationId,
            operation: 'portfolioItemConsumer',
            messageId: message.MessageId,
            portfolioItemId,
            duration: `${messageDuration}ms`,
            error: messageError.message,
            errorType: messageError.constructor?.name,
            severity: 'HIGH',
            retryable: true,
            stack: process.env.NODE_ENV !== 'production' ? messageError.stack : undefined,
            messageBody: message.Body,
            timestamp: new Date().toISOString(),
            layer: 'SQS_CONSUMER',
          });
        }
      }
    } catch (error) {
      const batchDuration = Date.now() - batchStartTime;

      logger.error('SQS portfolio item consumer batch failed', {
        correlationId: batchCorrelationId,
        operation: 'portfolioItemConsumer',
        batchSize: messages.length,
        duration: `${batchDuration}ms`,
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString(),
        layer: 'SQS_CONSUMER',
      });
    }

    const batchDuration = Date.now() - batchStartTime;

    logger.info('SQS portfolio item consumer batch completed', {
      correlationId: batchCorrelationId,
      operation: 'portfolioItemConsumer',
      batchSize: messages.length,
      duration: `${batchDuration}ms`,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });
  }

  private async portfolioItemWorkflowExecutionConsumer(messages: Message[]): Promise<void> {
    const batchCorrelationId = uuidv4();
    const batchStartTime = Date.now();

    logger.info(`Processing batch of ${messages.length} workflow execution messages`, {
      batchCorrelationId,
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    // Process messages in parallel for better throughput
    const messagePromises = messages.map(async sqsMessage => {
      const messageStartTime = Date.now();
      const messageCorrelationId = uuidv4();

      try {
        const { Body: body, MessageId } = sqsMessage;

        logger.info('Processing SQS workflow execution message', {
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioWorkflowExecution',
          messageId: MessageId,
          messageSize: body?.length || 0,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });

        const data = JSON.parse(body);
        const {
          portfolioId,
          portfolioItemId,
          isFirstMessage,
          isFollowUp,
          portfolioItem,
          workflowId,
          workflowExecutionId,
          message,
          messageType,
          fileUrl,
        } = data;

        // Use portfolioItemId as primary trace identifier for business correlation
        const traceId: string = (portfolioItemId || messageCorrelationId) as string;

        logger.info('Workflow execution message data parsed', {
          traceId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioWorkflowExecution',
          portfolioId,
          portfolioItemId,
          isFirstMessage,
          isFollowUp,
          messageBody: sanitizeMessageBody(data),
          layer: 'SQS_CONSUMER',
        });

        // Run workflow execution processing in correlation context with portfolioItemId as traceId
        await CorrelationContextService.run(
          {
            traceId,
            layer: 'SQS_CONSUMER',
            operation: 'portfolioWorkflowExecution',
          },
          async () => {
            if (!isFollowUp) {
              if (isFirstMessage) {
                logger.debug(
                  `Processing portfolioItem(${portfolioItemId}) workflow execution for first message.`,
                );

                await this.portfolioItemWorkflowExecutionUseCase.processWorkflowExecution(
                  portfolioId,
                  portfolioItemId,
                );
              } else {
                logger.debug(
                  `Processing portfolioItem(${portfolioItemId}) workflow execution for conversation flow.`,
                );

                await this.portfolioItemWorkflowExecutionUseCase.executeWorkflowToAnswer(
                  portfolioItem,
                  workflowId,
                  workflowExecutionId,
                  message,
                  messageType,
                  fileUrl,
                );
              }
            } else {
              logger.debug(
                `Processing portfolioItem(${portfolioItemId}) follow-up workflow execution`,
              );
              await this.portfolioItemFollowFlowExecutionUseCase.processFollowFlowExecution(
                portfolioId,
                portfolioItemId,
              );
            }
          },
        );

        const messageDuration = Date.now() - messageStartTime;

        logger.info('SQS workflow execution message processed successfully', {
          traceId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioWorkflowExecution',
          messageId: MessageId,
          portfolioId,
          portfolioItemId,
          duration: `${messageDuration}ms`,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      } catch (error) {
        const messageDuration = Date.now() - messageStartTime;

        // Extract portfolioItemId for error logging and Slack notification
        let errorPortfolioItemId: string | undefined;
        let errorTraceId: string = messageCorrelationId;

        let errorData;

        try {
          errorData = JSON.parse(sqsMessage.Body);
          errorPortfolioItemId = errorData.portfolioItemId;
          errorTraceId = (errorPortfolioItemId || messageCorrelationId) as string;
        } catch (parseError) {
          logger.warn('Failed to parse SQS message body as JSON', {
            correlationId: messageCorrelationId,
            messageId: sqsMessage.MessageId,
            error: parseError.message,
            stack: process.env.NODE_ENV !== 'production' ? parseError.stack : undefined,
          });
          errorPortfolioItemId = undefined;
          errorTraceId = messageCorrelationId;
        }

        const messageSlack = `
        :alert: Transcedence! Está acontecendo alguma coisa, fica de olho! :alert:
        PortfolioItemId: ${errorPortfolioItemId || 'N/A'}
        TraceId: ${errorTraceId}
        Error processing portfolioItem from message: ${JSON.stringify(
          sqsMessage,
        )}. Error: ${JSON.stringify(error)}`;

        try {
          await this.slackMessage.sendMessage(this.slackApiKey, 'C06B0KX9M3R', messageSlack);
        } catch (slackError) {
          logger.error('Error sending slack message', {
            traceId: errorTraceId,
            correlationId: messageCorrelationId,
            batchCorrelationId,
            portfolioItemId: errorPortfolioItemId,
            error: slackError.message,
            errorType: slackError.constructor?.name,
            severity: 'MEDIUM',
            layer: 'SLACK_NOTIFICATION',
            timestamp: new Date().toISOString(),
          });
        }

        logger.error('Error processing portfolioItem from workflow execution message', {
          traceId: errorTraceId,
          correlationId: messageCorrelationId,
          batchCorrelationId,
          operation: 'portfolioWorkflowExecution',
          messageId: sqsMessage.MessageId,
          portfolioItemId: errorPortfolioItemId,
          duration: `${messageDuration}ms`,
          error: error.message,
          errorType: error.constructor?.name,
          severity: 'HIGH',
          retryable: true,
          sqsMessage: JSON.stringify(sqsMessage),
          stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
          timestamp: new Date().toISOString(),
          layer: 'SQS_CONSUMER',
        });
      }
    });

    // Wait for all messages to be processed
    await Promise.allSettled(messagePromises);

    const batchDuration = Date.now() - batchStartTime;

    logger.info(`Completed processing batch of ${messages.length} workflow execution messages`, {
      batchCorrelationId,
      operation: 'portfolioWorkflowExecution',
      batchSize: messages.length,
      duration: `${batchDuration}ms`,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });
  }
}
