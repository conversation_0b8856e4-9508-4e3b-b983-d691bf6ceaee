import { CommunicationChannel } from '@common/enums';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  Matches,
} from 'class-validator';

export class PortfolioDto {
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @IsNotEmpty({ message: 'name is required' })
  @IsString({ message: 'name must be a string' })
  readonly name: string;

  @IsOptional()
  @IsUUID('4', { message: 'workflowId must be a valid UUID when provided' })
  readonly workflowId?: string;

  @IsNotEmpty({ message: 'workExpression is required' })
  //Para uma expressão cron ser válida, ela precisa passagar pela regeX: https://regexr.com/869rv
  @Matches(
    /(@(annually|yearly|monthly|weekly|daily|hourly|reboot))|(@every (\d+(ns|us|µs|ms|s|m|h))+)|((((\d+,)+\d+|(\d+(\/|-)\d+)|\d+|\*) ?){5,7})/,
    { message: 'workExpression must be a valid Cron expression.' },
  )
  readonly workExpression: string;

  @IsNumber()
  @IsOptional()
  readonly idleAfter?: number;

  @IsBoolean()
  @IsOptional()
  readonly executeImmediately?: boolean = false;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  @IsBoolean()
  readonly isDefault: boolean = false;

  @IsString()
  @IsOptional()
  timezoneUTC?: string = '-3';

  @IsString()
  @IsOptional()
  followUpCronExpression?: string = '0 11-23 * * 1-5';

  @IsNumber()
  @IsOptional()
  followUpQuantity?: number = 1;

  @IsNumber()
  @IsOptional()
  followUpAfter?: number = 120;

  @IsString()
  @IsOptional()
  followUpWorkflowId?: string;

  constructor(
    name: string,
    workflowId: string | undefined,
    workExpression: string,
    communicationChannel: CommunicationChannel,
    idleAfter?: number,
    executeImmediately?: boolean,
    isDefault?: boolean,
    timezoneUTC?: string,
    followUpCronExpression?: string,
    followUpQuantity?: number,
    followUpAfter?: number,
    followUpWorkflowId?: string,
  ) {
    this.name = name;
    this.workflowId = workflowId;
    this.workExpression = workExpression;
    this.communicationChannel = communicationChannel;
    this.idleAfter = idleAfter;
    this.executeImmediately = executeImmediately;
    this.isDefault = isDefault;
    this.timezoneUTC = timezoneUTC;
    this.followUpCronExpression = followUpCronExpression;
    this.followUpQuantity = followUpQuantity;
    this.followUpAfter = followUpAfter;
    this.followUpWorkflowId = followUpWorkflowId;
  }
}
