import { IsArray, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { IsString, IsIn } from 'class-validator';
import { Type } from 'class-transformer';

export class SelectedColumnsDto {
  @IsArray()
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => SelectedColumn)
  selectedColumns: SelectedColumn[];

  constructor(selectedColumns: SelectedColumn[]) {
    this.selectedColumns = selectedColumns;
  }
}

export class SelectedColumn {
  @IsString()
  @IsIn(['portfolioItem', 'customData', 'middlewareData', 'portfolio'])
  entity: 'portfolioItem' | 'customData' | 'middlewareData' | 'portfolio';

  @IsString()
  @IsNotEmpty()
  field: string;

  @IsString()
  @IsNotEmpty()
  displayName: string;
}
