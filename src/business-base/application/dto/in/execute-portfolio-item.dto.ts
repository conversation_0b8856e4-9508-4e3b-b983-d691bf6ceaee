import { IsEnum, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { CommunicationChannel, MessageType } from '@common/enums';

export class ExecutePortfolioItemDto {
  @IsString()
  @IsNotEmpty({ message: 'from is required' })
  readonly from: string;

  @IsString()
  @IsNotEmpty({ message: 'to is required' })
  readonly to: string;

  @IsString()
  @IsOptional()
  readonly message?: string;

  @IsEnum(MessageType)
  @IsNotEmpty({ message: 'messageType is required' })
  readonly messageType: MessageType;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty({ message: 'channel is required' })
  readonly channel: CommunicationChannel;

  @IsOptional()
  readonly filesUrl?: string[];

  constructor(
    from: string,
    to: string,
    messageType: MessageType,
    channel: CommunicationChannel,
    message?: string,
    filesUrl?: string[],
  ) {
    this.from = from;
    this.to = to;
    this.message = message;
    this.messageType = messageType;
    this.channel = channel;
    this.filesUrl = filesUrl;
  }
}
