import { PortfolioImportStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsNumber, IsString, IsUUID } from 'class-validator';

export class PortfolioUpdateImportDataDto {
  @IsUUID('4')
  @IsString()
  @IsNotEmpty()
  readonly id: string;

  @IsEnum(PortfolioImportStatus)
  readonly importStatus?: PortfolioImportStatus;

  @IsDate()
  readonly importFinishedAt?: Date;

  @IsNumber()
  readonly totalQuantity?: number;

  @IsNumber()
  readonly totalFailedQuantity?: number;

  @IsNumber()
  readonly totalSuccessQuantity?: number;

  @IsNumber()
  readonly processedQuantity?: number;
}
