import { PortfolioItemStatus, RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class PortfolioItemExecutionHistoryDto {
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly portfolioItemId: string;

  @IsEnum(PortfolioItemStatus)
  @IsNotEmpty()
  readonly oldStatus: PortfolioItemStatus;

  @IsEnum(PortfolioItemStatus)
  @IsNotEmpty()
  readonly newStatus: PortfolioItemStatus;

  @IsString()
  readonly reason?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  readonly status?: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    portfolioItemId: string,
    oldStatus: PortfolioItemStatus,
    newStatus: PortfolioItemStatus,
    reason?: string,
    status?: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.portfolioItemId = portfolioItemId;
    this.oldStatus = oldStatus;
    this.newStatus = newStatus;
    this.reason = reason;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
