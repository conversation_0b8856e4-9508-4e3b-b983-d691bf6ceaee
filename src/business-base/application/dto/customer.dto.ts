import { IsEmail, <PERSON>NotEmpty, IsO<PERSON>al, IsPhoneNumber, IsString, IsU<PERSON><PERSON> } from 'class-validator';
import { CustomerPhoneDto } from '@business-base/infrastructure/dto/out/customer-phone.dto';

export class CustomerDto {
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @IsNotEmpty({ message: 'name is required' })
  @IsString({ message: 'name must be a string' })
  readonly name: string;

  @IsNotEmpty({ message: 'cnpj is required' })
  @IsString({ message: 'cnpj must be a string' })
  readonly cnpj: string;

  @IsNotEmpty({ message: 'email is required' })
  @IsEmail({}, { message: 'email must be a valid email' })
  readonly email: string;

  @IsNotEmpty({ message: 'phone is required' })
  @IsPhoneNumber('BR', { message: 'phone must be a valid phone number' })
  readonly phone: string;

  @IsNotEmpty({ message: 'whatsappPhone is required' })
  @IsPhoneNumber('BR', { message: 'whatsappPhone must be a valid phone number' })
  //deprecated -> use customer whatsapp phones
  readonly whatsappPhone: string;

  @IsNotEmpty({ message: 'segment is required' })
  @IsString({ message: 'segment must be a string' })
  readonly segment: string;

  readonly whatsappPhones?: CustomerPhoneDto[];

  @IsNotEmpty()
  @IsOptional()
  readonly status?: string;

  constructor(
    name: string,
    cnpj: string,
    email: string,
    phone: string,
    whatsappPhone: string, //deprecated -> use customer whatsappPhones
    status: string,
    segment: string,
    whatsappPhones?: CustomerPhoneDto[],
  ) {
    this.name = name;
    this.cnpj = cnpj;
    this.email = email;
    this.phone = phone;
    this.whatsappPhone = whatsappPhone; //deprecated -> use customer whatsappPhones
    this.whatsappPhones = whatsappPhones;
    this.status = status;
    this.segment = segment;
  }
}
