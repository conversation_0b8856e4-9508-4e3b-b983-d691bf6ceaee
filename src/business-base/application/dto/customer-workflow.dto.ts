import { IsDate, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class CustomerWorkflowDto {
  @IsUUID('4')
  @IsOptional()
  readonly id?: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly customerId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsString()
  @IsNotEmpty()
  readonly workflowName: string;

  @IsString()
  @IsNotEmpty()
  readonly workflowDescription: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    customerId: string,
    workflowId: string,
    workflowName: string,
    workflowDescription: string,
    createdAt: Date,
    updatedAt: Date,
  ) {
    this.id = id;
    this.customerId = customerId;
    this.workflowId = workflowId;
    this.workflowName = workflowName;
    this.workflowDescription = workflowDescription;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
