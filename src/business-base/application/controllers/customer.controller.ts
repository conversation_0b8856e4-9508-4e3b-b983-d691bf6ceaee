import { Body, Controller, Delete, Get, Param, Post, Put, Version } from '@nestjs/common';
import { CustomerDto } from '@business-base/application/dto/customer.dto';
import { UpdateCustomerDto } from '@business-base/application/dto/in/update-customer.dto';
import { CustomerUseCase } from '@business-base/application/use-cases/customer.use-case';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('business-base/customers')
export class CustomerController {
  constructor(private readonly customerUseCase: CustomerUseCase) {}

  @Post()
  @Version('1')
  async create(@Body() createCustomerDto: CustomerDto): Promise<any> {
    const customer = await this.customerUseCase.create(createCustomerDto);

    return {
      statusCode: 201,
      data: customer,
    };
  }

  @Get('/:customerId')
  @Version('1')
  async findById(@Param('customerId') customerId: string): Promise<any> {
    const customer = await this.customerUseCase.findById(customerId);

    return {
      statusCode: 200,
      data: customer,
    };
  }

  @Put('/:customerId')
  @Version('1')
  async update(
    @Param('customerId') customerId: string,
    @Body() updateCustomerDto: UpdateCustomerDto,
  ): Promise<any> {
    const updatedCustomer = await this.customerUseCase.update(customerId, updateCustomerDto);

    return {
      statusCode: 200,
      data: updatedCustomer,
    };
  }

  @Delete('/:customerId')
  @Version('1')
  async deleteById(@Param('customerId') customerId: string): Promise<any> {
    const customer = await this.customerUseCase.delete(customerId);

    return {
      statusCode: 200,
      data: customer,
    };
  }

  @Post('/:customerId/workflows/:workflowId')
  @Version('1')
  async createCustomerWorkflows(
    @Param('customerId') customerId: string,
    @Param('workflowId') workflowId: string,
  ): Promise<any> {
    const customerWorkflows = await this.customerUseCase.createCustomerWorkflows(
      customerId,
      workflowId,
    );

    return {
      statusCode: 201,
      data: customerWorkflows,
    };
  }

  @Get('/:customerId/workflows')
  @Version('1')
  async findCustomerWorkflows(@Param('customerId') customerId: string): Promise<any> {
    const customerWorkflows = await this.customerUseCase.findCustomerWorkflowsByCustomerId(
      customerId,
    );

    return {
      statusCode: 200,
      data: customerWorkflows,
    };
  }
}
