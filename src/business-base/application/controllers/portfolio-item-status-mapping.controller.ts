import { Body, Controller, Delete, Get, Param, Post, Version } from '@nestjs/common';
import { PortfolioItemStatusMappingDto } from '@business-base/application/dto/in/portfolio-item-status-mapping.dto';
import { PortfolioItemStatusMappingUseCase } from '@business-base/application/use-cases/portfolio-item-status-mapping.use-case';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('business-base/portfolio/items/status-mapping')
export class PortfolioItemStatusMappingController {
  constructor(
    private readonly portfolioItemStatusMappingUseCase: PortfolioItemStatusMappingUseCase,
  ) {}

  @Post()
  @Version('1')
  async create(@Body() portfolioItemStausMappingDto: PortfolioItemStatusMappingDto): Promise<any> {
    const portfolioItemStatusMapping = await this.portfolioItemStatusMappingUseCase.create(
      portfolioItemStausMappingDto,
    );

    return {
      statusCode: 201,
      data: portfolioItemStatusMapping,
    };
  }

  @Get('/:workflowId')
  @Version('1')
  async findAllByWorkflowId(@Param('workflowId') workflowId: string): Promise<any> {
    const portfolioItemStatusMappings =
      await this.portfolioItemStatusMappingUseCase.listAllByWorkflowId(workflowId);

    return {
      statusCode: 200,
      data: portfolioItemStatusMappings,
    };
  }

  @Delete('/:workflowId')
  @Version('1')
  async deleteAllByWorkflowId(@Param('workflowId') workflowId: string): Promise<any> {
    const portfolioItemStatusMappings =
      await this.portfolioItemStatusMappingUseCase.deleteAllByWorkflowId(workflowId);

    return {
      statusCode: 200,
      data: portfolioItemStatusMappings,
    };
  }
}
