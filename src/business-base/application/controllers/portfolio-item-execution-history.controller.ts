import { PortfolioItemExecutionHistoryUseCase } from '@business-base/application/use-cases/portfolio-item-execution-history.use-case';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { Controller, Get, Param, Version } from '@nestjs/common';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('business-base/portfolio/items/execution/history')
export class PortfolioItemExecutionHistoryController {
  constructor(
    private readonly portfolioItemExecutionHistoryUseCase: PortfolioItemExecutionHistoryUseCase,
  ) {}

  @Get('/portfolio-item/:portfolioItemId')
  @Version('1')
  async findAll(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const executions = await this.portfolioItemExecutionHistoryUseCase.findAllByPortfolioItemId(
      portfolioItemId,
    );

    return {
      statusCode: 200,
      data: executions,
    };
  }
}
