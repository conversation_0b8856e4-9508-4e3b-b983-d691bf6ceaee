import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  StreamableFile,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  Version,
} from '@nestjs/common';
import { PortfolioItemUseCase } from '@business-base/application/use-cases/portfolio-item.use-case';
import { PortfolioItemDto } from '@business-base/application/dto/in/portfolio-item.dto';
import { MessageType, PortfolioItemStatus, RoleType } from '@common/enums';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { ExecutePortfolioItemDto } from '@business-base/application/dto/in/execute-portfolio-item.dto';
import { SendDirectMessageDto } from '@business-base/application/dto/out/send-direct-message.dto';
import { Express } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { SystemAuthnGuard } from '@common/auth/system-authn.guard';
import { SystemAuthnURLGuard } from '@common/auth/system-authn-url-guard';
import { ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ResponsePortfolioItemDto } from '@business-base/application/dto/out/response-portfolio-item.dto';
import { SlackMessage } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('business-base/portfolio-items')
export class PortfolioItemController {
  private readonly slackApiKey: string;

  constructor(
    private readonly portfolioItemUseCase: PortfolioItemUseCase,
    private readonly slackMessage: SlackMessage,
  ) {
    this.slackApiKey = process.env.SLACK_TOKEN;
  }

  @ApiBody({ type: PortfolioItemDto })
  @ApiOperation({
    summary: 'Create a portfolio item',
    description: `
      Create a portfolio item with the following fields:
      - portfolioId: string
      - phoneNumber: string
      - customData: any
      - line: number
      - createdAt: Date
      - updatedAt: Date
      `,
  })
  @ApiResponse({
    status: 201,
    description: 'The portfolio item has been successfully created.',
    type: ResponsePortfolioItemDto,
    example: {
      statusCode: 201,
      data: {
        id: '123e4567-e89b-12d3-a456-************',
        portfolioId: '123e4567-e89b-12d3-a456-************',
        phoneNumber: '+5511999999999',
        customData: {
          name: 'John Doe',
          email: '<EMAIL>',
        },
        line: 1,
        createdAt: '2021-01-01',
        updatedAt: '2021-01-01',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
  })
  @Post()
  @Version('1')
  async create(@Body() createPortfolioItemDto: PortfolioItemDto): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.create(createPortfolioItemDto);
    return {
      statusCode: 201,
      data: portfolioItem,
    };
  }

  @Post('/search/paginated/:customerId')
  @Version('1')
  async findAll(
    @Body() searchParams: any,
    @Param('customerId') customerId: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('sort') sort?: string,
  ): Promise<any> {
    const paginatedPortfolioItems = await this.portfolioItemUseCase.findAll(
      searchParams,
      customerId,
      page,
      limit,
      sort,
    );

    return {
      statusCode: 200,
      data: paginatedPortfolioItems,
    };
  }

  @Get('/:portfolioItemId')
  @Version('1')
  async findById(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.findById(portfolioItemId);

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Post('/:portfolioItemId/conversation-history')
  @Version('1')
  @UseInterceptors(FileInterceptor('file'))
  async sendDirectMessage(
    @Body('message') message: string,
    @Body('messageType') messageType: MessageType,
    @Body('roleType') roleType: RoleType,
    @Param('portfolioItemId') portfolioItemId: string,
    @UploadedFile() file?: Express.Multer.File,
  ): Promise<any> {
    const sendDirectMessageDto = new SendDirectMessageDto(message, messageType, roleType);

    const conversationData = await this.portfolioItemUseCase.sendDirectMessage(
      portfolioItemId,
      sendDirectMessageDto,
      file,
    );

    return {
      statusCode: 200,
      data: conversationData,
    };
  }

  @Get('/:portfolioItemId/conversation-history/files/:fileName')
  @Version('1')
  async getDirectMessageFile(@Param('fileName') fileName: string): Promise<StreamableFile> {
    const fileStream = await this.portfolioItemUseCase.getDirectMessageFileStream(fileName);

    return new StreamableFile(fileStream, {
      type: 'application/pdf',
      disposition: `attachment; filename="${fileName}"`,
    });
  }

  @Get('/:portfolioItemId/conversation-history/files/audio/:filePath')
  @Version('1')
  async getDirectMessageAudioFile(@Param('filePath') filePath: string): Promise<StreamableFile> {
    const fileStream = await this.portfolioItemUseCase.getDirectMessageFileStreamFromPath(filePath);

    return new StreamableFile(fileStream, {
      type: 'audio/mpeg',
      disposition: `attachment; filename="${filePath.split('/')[filePath.split('/').length - 1]}"`,
    });
  }

  @Get('/system/:fileName')
  @UseGuards(SystemAuthnGuard)
  @Version('1')
  async getDirectMessageFileFromSystem(
    @Param('fileName') fileName: string,
  ): Promise<StreamableFile> {
    const fileStream = await this.portfolioItemUseCase.getDirectMessageFileStream(fileName);

    return new StreamableFile(fileStream, {
      type: 'application/pdf',
      disposition: `attachment; filename="${fileName}"`,
    });
  }

  @Get('/system/url/:fileName')
  @UseGuards(SystemAuthnURLGuard)
  @Version('1')
  async getDirectMessageFileFromSystemURL(
    @Param('fileName') fileName: string,
  ): Promise<StreamableFile> {
    const fileStream = await this.portfolioItemUseCase.getDirectMessageFileStream(fileName);

    return new StreamableFile(fileStream, {
      type: 'application/pdf',
      disposition: `attachment; filename="${fileName}"`,
    });
  }

  @Get('/:portfolioItemId/conversation-history')
  @Version('1')
  async findConversationHistoryById(
    @Param('portfolioItemId') portfolioItemId: string,
  ): Promise<any> {
    const portfolioItemConversationHistoryMap =
      await this.portfolioItemUseCase.findConversationHistoryById(portfolioItemId);

    return {
      statusCode: 200,
      data: portfolioItemConversationHistoryMap,
    };
  }

  @Put('/:portfolioItemId/execute')
  @Version('1')
  async updateItemToInProgress(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.updateItemCurrentStatus(
      portfolioItemId,
      PortfolioItemStatus.IN_PROGRESS,
      'Atendimento iniciado manualmente pelo operador',
    );

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Put('/:portfolioItemId/succeed')
  @Version('1')
  async updateItemToSucceed(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.updateItemCurrentStatus(
      portfolioItemId,
      PortfolioItemStatus.SUCCEED,
      'Negociação concluída manualmente pelo operador',
    );

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Put('/:portfolioItemId/pause')
  @Version('1')
  async updateItemToPaused(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.updateItemCurrentStatus(
      portfolioItemId,
      PortfolioItemStatus.PAUSED,
      'Atendimento pausado manualmente pelo operador',
    );

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Put('/:portfolioItemId/cancel')
  @Version('1')
  async updateItemToCancelled(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.updateItemCurrentStatus(
      portfolioItemId,
      PortfolioItemStatus.CANCELLED,
      'Atendimento cancelado manualmente pelo operador',
    );

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Put('/:portfolioItemId/finish')
  @Version('1')
  async updateItemToFinished(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.updateItemCurrentStatus(
      portfolioItemId,
      PortfolioItemStatus.FINISHED,
      'Atendimento encerrado manualmente pelo operador',
    );

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Delete('/:portfolioItemId')
  @Version('1')
  async deleteById(@Param('portfolioItemId') portfolioItemId: string): Promise<any> {
    const portfolioItem = await this.portfolioItemUseCase.delete(portfolioItemId);

    return {
      statusCode: 200,
      data: portfolioItem,
    };
  }

  @Post('/execute')
  @Version('1')
  async executeItem(@Body() executePortfolioItemDto: ExecutePortfolioItemDto): Promise<any> {
    try {
      await this.portfolioItemUseCase.executeItem(executePortfolioItemDto);
    } catch (error) {
      const traceId = CorrelationContextService.getTraceId();

      // Check if traceId is a portfolioItemId (UUID format) for better context
      const isPortfolioItemId =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(traceId);

      const messageSlack = `
        :alert: Transcedence! Está acontecendo alguma coisa, fica de olho! :alert:
        ${isPortfolioItemId ? `PortfolioItemId: ${traceId}` : `TraceId: ${traceId}`}
        PhoneNumber: ${executePortfolioItemDto.from}
        Error executing business-base/portfolio-items/execute.
        Body: ${JSON.stringify(executePortfolioItemDto)}
        Error message: ${JSON.stringify(error.message)}
        Error JSON: ${JSON.stringify(error)}`;

      await this.slackMessage.sendMessage(this.slackApiKey, 'C06B0KX9M3R', messageSlack);
    }
  }
}
