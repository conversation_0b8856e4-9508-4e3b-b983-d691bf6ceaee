import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { logger } from '@edutalent/commons-sdk';
import { lastValueFrom } from 'rxjs';
import { handleHttpError } from '@common/utils/handle-http-error';
import { InfraConversationHistoryPort } from '@business-base/infrastructure/ports/http/conversation-history.port';
import { MessageHistoryResponseDto } from '@business-base/misc/interfaces/in/message-history-response.dto';
import { SendDirectMessageDto } from '@business-base/application/dto/out/send-direct-message.dto';

@Injectable()
export class InfraConversationHistoryAdapter implements InfraConversationHistoryPort {
  private readonly intelligenceServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.intelligenceServiceUrl = String(process.env.INTELLIGENCE_SERVICE_URL);
  }

  async createConversationHistoryByWorkflowExecutionId(
    workflowExecutionId: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistoryResponseDto[]> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/orchestrator/workflow-executions/conversation-history/${workflowExecutionId}`;
      logger.info(`Posting data to: ${url}`);
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(
        this.httpService.post(url, sendDirectMessageDto, { headers }),
      );

      return data.data;
    } catch (error) {
      handleHttpError(error, 'infra-conversation-history-adapter');
    }
  }

  async retrieveConversationHistoryByWorkflowExecutionId(
    workflowExecutionId: string,
  ): Promise<MessageHistoryResponseDto[]> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/orchestrator/workflow-executions/conversation-history/${workflowExecutionId}`;
      logger.info(`Getting data from ${url}`);

      const { data } = await lastValueFrom(this.httpService.get(url));

      return data.data;
    } catch (error) {
      handleHttpError(error, 'infra-conversation-history-adapter');
    }
  }
}
