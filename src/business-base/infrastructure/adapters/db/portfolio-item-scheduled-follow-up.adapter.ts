import { Injectable } from '@nestjs/common';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PrismaService } from '@common/prisma/prisma.service';
import { PortfolioItemScheduledFollowUpEntity } from '@business-base/domain/entities/portfolio-item-scheduled-follow-up.entity';
import { PortfolioItemScheduledFollowUpPort } from '@business-base/infrastructure/ports/db/portfolio-item-scheduled-follow-up.port';

@Injectable()
export class PortfolioItemScheduledFollowUpAdapter
  extends PrismaCommonAdapter<PortfolioItemScheduledFollowUpEntity>
  implements PortfolioItemScheduledFollowUpPort
{
  constructor(prisma: PrismaService) {
    super(prisma, 'portfolioItemScheduledFollowUp');
  }
}
