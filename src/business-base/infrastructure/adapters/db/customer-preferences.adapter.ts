import { Injectable } from '@nestjs/common';
import {
  DynamoDBDocumentClient,
  GetCommand,
  PutCommand,
  UpdateCommand,
} from '@aws-sdk/lib-dynamodb';
import { CustomerPreferencesEntity } from '@business-base/domain/entities/customer-preferences.entity';
import { CustomerPreferencesPort } from '@business-base/infrastructure/ports/db/customer-preferences.port';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { RecordStatus } from '@common/enums';

@Injectable()
export class CustomerPreferencesAdapter implements CustomerPreferencesPort {
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_customer_preferences';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async create(entity: CustomerPreferencesEntity): Promise<CustomerPreferencesEntity> {
    try {
      const now = new Date().toISOString();
      const item = {
        ...entity,
        status: entity.status || RecordStatus.ACTIVE,
        createdAt: now,
        updatedAt: now,
      };

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: item,
          ConditionExpression: 'attribute_not_exists(customerId)',
        }),
      );

      return {
        ...entity,
        status: entity.status || RecordStatus.ACTIVE,
        createdAt: new Date(now),
        updatedAt: new Date(now),
      };
    } catch (error) {
      // Handle conditional check failure (duplicate customer preferences)
      if (error.name === 'ConditionalCheckFailedException') {
        throw new BusinessException(
          'CustomerPreferencesAlreadyExist',
          `Customer preferences already exist for customerId: ${entity.customerId}`,
          BusinessExceptionStatus.INVALID_INPUT,
        );
      }

      throw new DynamoException({
        message: `Error while creating customer preferences for customerId: ${
          entity.customerId
        }. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async getById(customerId: string): Promise<CustomerPreferencesEntity> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            customerId: customerId.toString(),
          },
        }),
      );

      if (!Item || Item.status === RecordStatus.DELETED) {
        return null;
      }

      return {
        ...Item,
        createdAt: new Date(Item.createdAt),
        updatedAt: new Date(Item.updatedAt),
      } as CustomerPreferencesEntity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while fetching customer preferences for customerId: ${customerId}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  //get by id regardless of status
  async getByIdIgnoreStatus(customerId: string): Promise<CustomerPreferencesEntity | null> {
    try {
      const { Item } = await this.dynamoClient.send(
        new GetCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            customerId: customerId.toString(),
          },
        }),
      );
      if (!Item) {
        return null;
      }
      return {
        ...Item,
        createdAt: new Date(Item.createdAt),
        updatedAt: new Date(Item.updatedAt),
      } as CustomerPreferencesEntity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while fetching customer preferences for customerId: ${customerId}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async update(
    customerId: string,
    entity: CustomerPreferencesEntity,
  ): Promise<CustomerPreferencesEntity> {
    try {
      const now = new Date().toISOString();
      const item = {
        ...entity,
        customerId,
        createdAt:
          entity.createdAt instanceof Date ? entity.createdAt.toISOString() : entity.createdAt,
        updatedAt: now,
      };

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: item,
          ConditionExpression: 'attribute_exists(customerId)',
        }),
      );

      return { ...entity, customerId, updatedAt: new Date(now) };
    } catch (error) {
      throw new DynamoException({
        message: `Error while updating customer preferences for customerId: ${customerId}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }

  async delete(customerId: string): Promise<void> {
    try {
      const now = new Date().toISOString();
      await this.dynamoClient.send(
        new UpdateCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Key: {
            customerId: customerId.toString(),
          },
          UpdateExpression: 'SET #status = :status, updatedAt = :updatedAt',
          ExpressionAttributeNames: {
            '#status': 'status',
          },
          ExpressionAttributeValues: {
            ':status': RecordStatus.DELETED,
            ':updatedAt': now,
          },
          ConditionExpression: 'attribute_exists(customerId) AND #status <> :status',
        }),
      );
    } catch (error) {
      throw new DynamoException({
        message: `Error while deleting customer preferences for customerId: ${customerId}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }
}
