import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PortfolioItemWorkflowExecutionEntity } from '@business-base/domain/entities/portfolio-item-workflow-execution.entity';
import { PortfolioItemWorkflowExecutionPort } from '@business-base/infrastructure/ports/db/portfolio-item-worflow-execution.port';

@Injectable()
export class PortfolioItemWorkflowExecutionAdapter
  extends PrismaCommonAdapter<PortfolioItemWorkflowExecutionEntity>
  implements PortfolioItemWorkflowExecutionPort
{
  constructor(prisma: PrismaService) {
    super(prisma, 'portfolioItemWorkflowExecution');
  }
}
