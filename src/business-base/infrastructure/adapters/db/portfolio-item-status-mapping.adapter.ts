import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PortfolioItemStatusMappingEntity } from '@business-base/domain/entities/portfolio-item-status-mapping.entity';
import { PortfolioItemStatusMappingPort } from '@business-base/infrastructure/ports/db/portfolio-item-status-mapping.port';
import { RecordStatus } from '@common/enums';

@Injectable()
export class PortfolioItemStatusMappingAdapter
  extends PrismaCommonAdapter<PortfolioItemStatusMappingEntity>
  implements PortfolioItemStatusMappingPort
{
  constructor(prisma: PrismaService) {
    super(prisma, 'portfolioItemStatusMapping');
  }

  async deleteAllByWorkflowId(workflowId: string): Promise<void> {
    await this.prismaClient.portfolioItemStatusMapping.updateMany({
      where: { workflowId: workflowId },
      data: { status: RecordStatus.DELETED },
    });
  }
}
