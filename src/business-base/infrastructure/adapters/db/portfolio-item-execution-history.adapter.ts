import { Injectable } from '@nestjs/common';
import { PortfolioItemExecutionHistoryEntity } from '@business-base/domain/entities/portfolio-item-execution-history.entity';
import { PortfolioItemExecutionHistoryPort } from '@business-base/infrastructure/ports/db/portfolio-item-execution-history.port';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PrismaService } from '@common/prisma/prisma.service';

@Injectable()
export class PortfolioItemExecutionHistoryAdapter
  extends PrismaCommonAdapter<PortfolioItemExecutionHistoryEntity>
  implements PortfolioItemExecutionHistoryPort
{
  constructor(prisma: PrismaService) {
    super(prisma, 'portfolioItemExecutionHistory');
  }
}
