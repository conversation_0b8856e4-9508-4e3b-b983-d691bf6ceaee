import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CustomerWorkflowEntity } from '@business-base/domain/entities/customer-workflow.entity';
import { CustomerWorkflowPort } from '@business-base/infrastructure/ports/db/customer-workflow.port';

@Injectable()
export class CustomerWorkflowAdapter
  extends PrismaCommonAdapter<CustomerWorkflowEntity>
  implements CustomerWorkflowPort
{
  constructor(prisma: PrismaService) {
    super(prisma, 'customerWorkflow');
  }
}
