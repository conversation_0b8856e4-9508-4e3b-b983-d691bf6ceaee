import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { PortfolioItemImportErrorEntity } from '@business-base/domain/entities/portfolio-item-import-error.entity';
import { PortfolioItemImportErrorPort } from '@business-base/infrastructure/ports/db/portfolio-item-import-error.port';
import { Injectable } from '@nestjs/common';

@Injectable()
export class PortfolioItemImportErrorAdapter
  extends PrismaCommonAdapter<PortfolioItemImportErrorEntity>
  implements PortfolioItemImportErrorPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'portfolioItemImportError');
  }

  async countByPortfolioId(portfolioId: string): Promise<number> {
    return this.prisma.client.portfolioItemImportError.count({
      where: {
        portfolioId,
      },
    });
  }
}
