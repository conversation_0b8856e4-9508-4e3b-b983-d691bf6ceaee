import { Injectable } from '@nestjs/common';
import { CustomerEntity } from '@business-base/domain/entities/customer.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { CustomerPort } from '@business-base/infrastructure/ports/db/customer.port';
import { RecordStatus } from '@common/enums';

@Injectable()
export class CustomerAdapter extends PrismaCommonAdapter<CustomerEntity> implements CustomerPort {
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'customer');
  }

  async findByWhatsappPhone(whatsappPhone: string): Promise<CustomerEntity> {
    const customer = await this.prisma.client.customer.findFirst({
      where: {
        whatsappPhone,
        status: RecordStatus.ACTIVE,
      },
    });

    if (!customer) {
      return null;
    }

    return {
      ...customer,
      status: customer.status as RecordStatus,
    };
  }
}
