import { Injectable } from '@nestjs/common';
import { NectarPort } from '@business-base/infrastructure/ports/soap/nectar.port';
import * as soap from 'soap';
import { logger } from '@edutalent/commons-sdk';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { NectarAuthenticationResponseDto } from '@business-base/infrastructure/dto/out/nectar-authentication-response.dto';
import { NectarGetDadosDividaResponse } from '@business-base/infrastructure/dto/out/nectar-getdadosdivida-response.dto';
import {
  NectarGetOpcoesNegociacaoResponse,
  NectarOpcoesNegociacao,
} from '@business-base/infrastructure/dto/out/nectar-getopcoesnegociacao-response.dto';
import { NectarGravarNegociacaoResponse } from '@business-base/infrastructure/dto/out/nectar-gravaracordo-response.dto';
import { NectarGetBoletoAcordoRequest } from '@business-base/infrastructure/dto/in/nectar-getboletoacordo-resquest.dto';
import { NectarGravarNegociacaoRequest } from '@business-base/infrastructure/dto/in/nectar-gravaracordo-resquest.dto';
import { NectarGetBoletoAcordoResponse } from '@business-base/infrastructure/dto/out/nectar-getboletoacordo-response.dto';
import {
  IntegrationException,
  IntegrationExceptionStatus,
} from '@common/exception/types/IntegrationException';

@Injectable()
export class NectarAdapter implements NectarPort {
  private nectarAuthentication: NectarAuthenticationResponseDto;

  constructor() {}

  async getDadosDivida(document: string): Promise<NectarGetDadosDividaResponse> {
    const traceId = CorrelationContextService.getTraceId();
    const startTime = Date.now();

    logger.info('SOAP GetDadosDivida request initiated', {
      traceId,
      operation: 'GetDadosDivida',
      document: document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4'), // Mask CPF for privacy
      timestamp: new Date().toISOString(),
      layer: 'SOAP_CLIENT',
    });

    try {
      const token = await this.getToken();
      const client = await this.createSoapClient();

      // SOAP request logging handled by HTTP interceptor

      const requestData = {
        cnpjcpf: document,
        codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
        codigoToken: token,
      };

      // Request data logging handled by HTTP interceptor

      return new Promise((resolve, reject) => {
        client.GetDadosDivida(requestData, (err, result) => {
          const duration = Date.now() - startTime;

          if (err || !result?.GetDadosDividaResult?.Contrato?.Contrato) {
            const responseError = { ...err, ...result };

            logger.error('SOAP GetDadosDivida failed', {
              traceId,
              operation: 'GetDadosDivida',
              document: document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4'),
              duration: `${duration}ms`,
              error: err?.message || 'No contracts found',
              soapFault: err?.body || err?.fault,
              responseError: responseError,
              resultado: result?.GetDadosDividaResult?.Resultado,
              timestamp: new Date().toISOString(),
              layer: 'SOAP_CLIENT',
            });

            reject(
              new IntegrationException(
                `Nectar GetDadosDivida Error: ${JSON.stringify(
                  result?.GetDadosDividaResult?.Resultado,
                )}`,
                IntegrationExceptionStatus.ITEM_NOT_FOUND,
              ),
            );
          } else {
            logger.info('SOAP GetDadosDivida completed successfully', {
              traceId,
              operation: 'GetDadosDivida',
              document: document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4'),
              duration: `${duration}ms`,
              contractsFound: result?.GetDadosDividaResult?.Contrato?.Contrato?.length || 0,
              timestamp: new Date().toISOString(),
              layer: 'SOAP_CLIENT',
            });

            const response = result as NectarGetDadosDividaResponse;

            if (!Array.isArray(response.GetDadosDividaResult.Contrato.Contrato)) {
              response.GetDadosDividaResult.Contrato.Contrato = [
                response.GetDadosDividaResult.Contrato.Contrato,
              ];
            }

            for (const contrato of response.GetDadosDividaResult.Contrato.Contrato) {
              if (!Array.isArray(contrato.Divida.Divida)) {
                contrato.Divida.Divida = [contrato.Divida.Divida];
              }

              const listDivida = contrato.Divida.Divida.filter(
                divida => divida.Descricao.toLowerCase() == 'carne',
              );

              if (listDivida.length > 0) {
                contrato.Divida.Divida = listDivida;
              }

              if (contrato.Acordo?.Acordo) {
                if (!Array.isArray(contrato.Acordo.Acordo)) {
                  contrato.Acordo.Acordo = [contrato.Acordo.Acordo];
                }

                contrato.Acordo.Acordo.forEach(acordo => {
                  if (!Array.isArray(acordo.AcordoParcela.AcordoParcela)) {
                    acordo.AcordoParcela.AcordoParcela = [acordo.AcordoParcela.AcordoParcela];
                  }
                });
              }
            }

            resolve(response);
          }
        });
      });
    } catch (error) {
      const duration = Date.now() - startTime;
      const context = CorrelationContextService.getContext();

      logger.error('SOAP GetDadosDivida operation failed', {
        traceId,
        operation: 'GetDadosDivida',
        document: document.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.***.***-$4'),
        duration: `${duration}ms`,
        error: error.message,
        errorType: this.getSoapErrorType(error),
        severity: this.getSoapErrorSeverity(error),
        retryable: this.isSoapRetryableError(error),
        businessContext: context?.operation,
        soapFault: error.body || error.fault,
        httpStatusCode: error.statusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SOAP_CLIENT',
        operationType: 'get_dados_divida_error',
      });

      throw new IntegrationException(
        `SOAP service error - GetDadosDivida failed: ${error.message}`,
        IntegrationExceptionStatus.GENERAL_ERROR,
      );
    }
  }

  async getOpcoesNegociacao(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    tpDesconto: string,
    percDescAplicNoPrincipal: string,
    percDescAplicNaCorrecao: string,
    vencPrimParcela: string,
  ): Promise<NectarGetOpcoesNegociacaoResponse> {
    const token = await this.getToken();

    const client = await this.createSoapClient();
    // SOAP request logging handled by HTTP interceptor

    const requestData = {
      idCon: idCon,
      idServ: 1,
      titulos: titulos,
      parcelasNum: parcelasNum,
      vencPrimParcela: vencPrimParcela,
      tiponegociacao: 3,
      tpDesconto: tpDesconto,
      percDescAplicNoPrincipal: percDescAplicNoPrincipal,
      percDescAplicNaCorrecao: percDescAplicNaCorrecao,
      codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
      codigoToken: token,
    };

    return new Promise((resolve, reject) => {
      client.GetOpcoesNegociacao(requestData, (err, result) => {
        if (err || !result?.GetOpcoesNegociacaoResult?.OpcoesNegociacao?.OpcoesNegociacao) {
          const responseError = { ...err, ...result };

          logger.error(
            'NectarAdapter - GetOpcoesNegociacao Error in: ' + JSON.stringify(responseError),
          );

          reject(
            new IntegrationException(
              `Nectar GetOpcoesNegociacao Error: ${JSON.stringify(
                result?.GetOpcoesNegociacaoResult?.Resultado,
              )}`,
              IntegrationExceptionStatus.ITEM_NOT_FOUND,
            ),
          );
        } else {
          logger.info('NectarAdapter - GetOpcoesNegociacao Result: ' + JSON.stringify(result));

          const response = result as NectarGetOpcoesNegociacaoResponse;

          if (
            !Array.isArray(response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao)
          ) {
            response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao = [
              response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao,
            ];
          }

          for (const opcao of response.GetOpcoesNegociacaoResult.OpcoesNegociacao
            .OpcoesNegociacao) {
            if (!Array.isArray(opcao.Parcelas.Parcelas)) {
              opcao.Parcelas.Parcelas = [opcao.Parcelas.Parcelas];
            }
          }

          resolve(response);
        }
      });
    });
  }

  async getOpcoesNegociacaoWithMaxDiscountLessOne(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    vencPrimParcela: string,
  ): Promise<NectarOpcoesNegociacao[]> {
    logger.info('NectarAdapter - getOpcoesNegociacaoWithMaxDiscount');

    const getOpcoesNegociacaoResponse = await this.getOpcoesNegociacao(
      idCon,
      titulos,
      parcelasNum,
      '2',
      '',
      '',
      vencPrimParcela,
    );

    const listOpcoesNegociacaoUpdated: NectarOpcoesNegociacao[] = [];

    for (
      let i = 0;
      i <
      getOpcoesNegociacaoResponse.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao
        .length;
      i++
    ) {
      const opcaoNegociacao =
        getOpcoesNegociacaoResponse.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao[i];

      logger.info(
        `NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Applying Discount to CodigoFaixa: ${opcaoNegociacao.CodigoFaixa}`,
      );

      let percDescAplicNaCorrecao = opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao;

      if (Number(opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao) >= 1) {
        percDescAplicNaCorrecao = (
          Number(opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao) - 1
        ).toString();
      }

      const getOpcoesNegociacao = await this.getOpcoesNegociacao(
        idCon,
        titulos,
        parcelasNum,
        '2',
        opcaoNegociacao.PercentualMaximoDeDescontoNoPrincipal,
        percDescAplicNaCorrecao,
        vencPrimParcela,
      );

      const opcaoNegociacaoUpdated =
        getOpcoesNegociacao.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao.filter(
          opcao => opcao.CodigoFaixa == opcaoNegociacao.CodigoFaixa,
        )[0];

      logger.info(
        `NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Discount Applied - CodigoFaixa: ${opcaoNegociacao.CodigoFaixa}`,
      );

      listOpcoesNegociacaoUpdated.push(opcaoNegociacaoUpdated);
    }

    logger.info(
      'NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Result: ' +
        JSON.stringify(listOpcoesNegociacaoUpdated),
    );

    return listOpcoesNegociacaoUpdated;
  }

  async gravarNegociacao(
    nectarGravarNegociacaoRequest: NectarGravarNegociacaoRequest,
  ): Promise<NectarGravarNegociacaoResponse> {
    const token = await this.getToken();

    const client = await this.createSoapClient();
    // SOAP request logging handled by HTTP interceptor

    nectarGravarNegociacaoRequest.codigoParceiro = process.env.NECTAR_LOGIN_CODIGO_PARCEIRO;
    nectarGravarNegociacaoRequest.codigoToken = token;

    return new Promise((resolve, reject) => {
      client.GravarNegociacao(nectarGravarNegociacaoRequest, (err, result) => {
        if (err || result?.GravarNegociacaoResult?.Resultado?.Mensagem !== 'Sucesso') {
          const responseError = { ...err, ...result };

          logger.error(
            'NectarAdapter - GravarNegociacao Error in: ' + JSON.stringify(responseError),
          );
          reject(responseError);
        } else {
          logger.info('NectarAdapter - GravarNegociacao Result: ' + JSON.stringify(result));

          const response = result as NectarGravarNegociacaoResponse;

          resolve(response);
        }
      });
    });
  }

  async getBoletoAcordo(
    nectarGetBoletoAcordoRequest: NectarGetBoletoAcordoRequest,
  ): Promise<NectarGetBoletoAcordoResponse> {
    const token = await this.getToken();

    const client = await this.createSoapClient();
    // SOAP request logging handled by HTTP interceptor

    nectarGetBoletoAcordoRequest.codigoParceiro = process.env.NECTAR_LOGIN_CODIGO_PARCEIRO;
    nectarGetBoletoAcordoRequest.codigoToken = token;

    return new Promise((resolve, reject) => {
      client.GetBoletoAcordo(nectarGetBoletoAcordoRequest, (err, result) => {
        if (err || result?.GetBoletoAcordoResult?.Resultado?.Resultado?.Mensagem !== 'Sucesso') {
          const responseError = { ...err, ...result };

          logger.error('GetBoletoAcordo Error in: ' + JSON.stringify(responseError));
          reject(responseError);
        } else {
          logger.info('NectarAdapter - GetBoletoAcordo Result: ' + JSON.stringify(result));

          const response = result as NectarGetBoletoAcordoResponse;

          resolve(response);
        }
      });
    });
  }

  private async getToken(): Promise<string> {
    try {
      const client = await this.createSoapClient();
      // SOAP request logging handled by HTTP interceptor

      return new Promise((resolve, reject) => {
        if (this.nectarAuthentication && this.nectarAuthentication.ValidadeToken > new Date()) {
          resolve(this.nectarAuthentication.CodigoToken);
          return;
        }

        const nectarLoginInfo = {
          cnpj: process.env.NECTAR_LOGIN_CNPJ.toString(),
          codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
          usu: process.env.NECTAR_LOGIN_USU.toString(),
          pass: process.env.NECTAR_LOGIN_PASS.toString(),
        };

        client.GetToken(nectarLoginInfo, (err, result) => {
          if (err || !result.GetTokenResult) {
            logger.error('NectarAdapter - GetToken Error: ' + JSON.stringify(err));
            reject(err);
          } else {
            logger.info('NectarAdapter - GetToken Result:', result);
            this.nectarAuthentication = result.GetTokenResult;
            resolve(this.nectarAuthentication.CodigoToken);
          }
        });
      });
    } catch (error) {
      const traceId = CorrelationContextService.getTraceId();
      const context = CorrelationContextService.getContext();

      logger.error('SOAP GetToken authentication failed', {
        traceId,
        operation: 'GetToken',
        error: error.message,
        errorType: this.getSoapErrorType(error),
        severity: 'CRITICAL', // Authentication failures are always critical
        retryable: this.isSoapRetryableError(error),
        businessContext: context?.operation,
        soapFault: error.body || error.fault,
        httpStatusCode: error.statusCode,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SOAP_CLIENT',
        operationType: 'authentication_error',
      });

      throw new IntegrationException(
        `SOAP authentication failed: ${error.message}`,
        IntegrationExceptionStatus.GENERAL_ERROR,
      );
    }
  }

  private async createSoapClient(): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();
    const nectarEndpoint = process.env.NECTAR_WSDL_URL.toString();
    const startTime = Date.now();

    logger.debug('Creating SOAP client', {
      traceId,
      endpoint: nectarEndpoint,
      timeout: 5000,
      timestamp: new Date().toISOString(),
      layer: 'SOAP_CLIENT',
    });

    return new Promise((resolve, reject) => {
      soap.createClient(
        nectarEndpoint,
        {
          disableCache: true,
          forceSoap12Headers: false,
          wsdl_options: {
            timeout: 5000, // 5 seconds timeout
          },
        },
        (err, client) => {
          const duration = Date.now() - startTime;

          if (err) {
            logger.error('SOAP client creation failed', {
              traceId,
              endpoint: nectarEndpoint,
              duration: `${duration}ms`,
              error: err.message,
              errorCode: err.code,
              stack: err.stack,
              timestamp: new Date().toISOString(),
              layer: 'SOAP_CLIENT',
            });
            reject(err);
          } else {
            logger.debug('SOAP client created successfully', {
              traceId,
              endpoint: nectarEndpoint,
              duration: `${duration}ms`,
              timestamp: new Date().toISOString(),
              layer: 'SOAP_CLIENT',
            });
            resolve(client);
          }
        },
      );
    });
  }

  private getSoapErrorType(error: any): string {
    if (!error) return 'UNKNOWN_ERROR';

    // Check for SOAP fault codes
    if (error.fault || error.body) {
      return 'SOAP_FAULT';
    }

    // Check for HTTP errors
    if (error.statusCode) {
      if (error.statusCode >= 500) return 'SERVER_ERROR';
      if (error.statusCode >= 400) return 'CLIENT_ERROR';
    }

    // Check for network errors
    if (error.code) {
      switch (error.code) {
        case 'ECONNREFUSED':
          return 'CONNECTION_REFUSED';
        case 'ENOTFOUND':
          return 'DNS_RESOLUTION_FAILED';
        case 'ECONNRESET':
          return 'CONNECTION_RESET';
        case 'ETIMEDOUT':
          return 'TIMEOUT';
        case 'ECONNABORTED':
          return 'CONNECTION_ABORTED';
        default:
          return `NETWORK_ERROR_${error.code}`;
      }
    }

    return 'SOAP_ERROR';
  }

  private getSoapErrorSeverity(error: any): string {
    if (!error) return 'HIGH';

    // Critical infrastructure errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      return 'CRITICAL';
    }

    // High priority errors
    if (error.statusCode >= 500 || error.code === 'ETIMEDOUT') {
      return 'HIGH';
    }

    // Medium priority errors
    if (error.statusCode >= 400 || error.fault) {
      return 'MEDIUM';
    }

    return 'LOW';
  }

  private isSoapRetryableError(error: any): boolean {
    if (!error) return false;

    // Retryable network errors
    const retryableCodes = ['ETIMEDOUT', 'ECONNRESET', 'ECONNABORTED'];
    if (error.code && retryableCodes.includes(error.code)) {
      return true;
    }

    // Retryable HTTP status codes
    if (error.statusCode >= 500) {
      return true;
    }

    return false;
  }
}
