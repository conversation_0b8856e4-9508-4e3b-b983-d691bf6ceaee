import { MiddlewareResponseOutputEntity } from '@business-base/domain/entities/middleware-response-output.entity';

export interface MiddlewareResponseOutputPort {
  getByPortfolioItemId(
    id: string,
    portfolioItemId: string,
  ): Promise<MiddlewareResponseOutputEntity>;

  create(entity: MiddlewareResponseOutputEntity): Promise<MiddlewareResponseOutputEntity>;

  update(id: string, portfolioItemId: string, data: any): Promise<MiddlewareResponseOutputEntity>;
}
