import { DbCommonPort } from '@common/db/ports/common.port';
import { CollectCashStatsEntity } from '@business-base/domain/entities/collect-cash-stats.entity';

export interface CollectCashStatsPort extends DbCommonPort<CollectCashStatsEntity> {
  getTotalDealValueByCustomerId(customerId: string): Promise<number>;
  getTotalDealValueByPortfolioId(portfolioId: string): Promise<number>;
  getTotalDealValueByCustomerIdWithDateRange(
    customerId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number>;
  getTotalDealValueByPortfolioIdWithDateRange(
    portfolioId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<number>;
}
