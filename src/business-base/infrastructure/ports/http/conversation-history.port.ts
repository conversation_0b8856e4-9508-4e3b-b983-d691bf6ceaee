import { MessageHistoryResponseDto } from '@business-base/misc/interfaces/in/message-history-response.dto';
import { SendDirectMessageDto } from '@business-base/application/dto/out/send-direct-message.dto';

export interface InfraConversationHistoryPort {
  createConversationHistoryByWorkflowExecutionId(
    workflowExecutionId: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistoryResponseDto[]>;

  retrieveConversationHistoryByWorkflowExecutionId(
    workflowExecutionId: string,
  ): Promise<MessageHistoryResponseDto[]>;
}
