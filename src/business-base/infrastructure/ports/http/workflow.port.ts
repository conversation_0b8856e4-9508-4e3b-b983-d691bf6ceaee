import { ExecuteWorkflow } from '@business-base/misc/interfaces/in/execute-workflow';
import { WorkflowResponseDto } from '@business-base/infrastructure/adapters/http/dto/workflow-response.dto';
import { ExecuteWorkflowResponseDto } from '@orchestrator/application/dto/out/execute-workflow-response.dto';

export interface InfraWorkflowPort {
  startWorkflow(
    workflowId: string,
  ): Promise<{ workflowExecutionId: string; stepExecutionId: string; stepExecutionOrder: number }>;
  executeWorkflow(executeWorkflowData: ExecuteWorkflow): Promise<ExecuteWorkflowResponseDto>;
  getWorkflowById(workflowId: string): Promise<WorkflowResponseDto>;
  getWorkflowByName(workflowName: string): Promise<WorkflowResponseDto>;
  getWorkflowVariables(workflowId: string): Promise<string[]>;
}
