import { NectarGetBoletoAcordoRequest } from '@business-base/infrastructure/dto/in/nectar-getboletoacordo-resquest.dto';
import { NectarGravarNegociacaoRequest } from '@business-base/infrastructure/dto/in/nectar-gravaracordo-resquest.dto';
import { NectarGetBoletoAcordoResponse } from '@business-base/infrastructure/dto/out/nectar-getboletoacordo-response.dto';
import { NectarGetDadosDividaResponse } from '@business-base/infrastructure/dto/out/nectar-getdadosdivida-response.dto';
import {
  NectarGetOpcoesNegociacaoResponse,
  NectarOpcoesNegociacao,
} from '@business-base/infrastructure/dto/out/nectar-getopcoesnegociacao-response.dto';
import { NectarGravarNegociacaoResponse } from '@business-base/infrastructure/dto/out/nectar-gravaracordo-response.dto';

export interface NectarPort {
  getDadosDivida(document: string): Promise<NectarGetDadosDividaResponse>;
  getOpcoesNegociacao(
    idCon: string,
    titulo: string,
    parcelasNum: string,
    tpDesconto: string,
    percDescAplicNoPrincipal: string,
    percDescAplicNaCorrecao: string,
    vencPrimParcela: string,
  ): Promise<NectarGetOpcoesNegociacaoResponse>;
  getOpcoesNegociacaoWithMaxDiscountLessOne(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    vencPrimParcela: string,
  ): Promise<NectarOpcoesNegociacao[]>;
  gravarNegociacao(
    nectarGravarNegociacaoRequest: NectarGravarNegociacaoRequest,
  ): Promise<NectarGravarNegociacaoResponse>;
  getBoletoAcordo(
    nectarGetBoletoAcordoRequest: NectarGetBoletoAcordoRequest,
  ): Promise<NectarGetBoletoAcordoResponse>;
}
