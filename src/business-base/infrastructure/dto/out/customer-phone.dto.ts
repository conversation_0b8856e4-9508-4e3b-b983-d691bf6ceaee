import { IsEnum, <PERSON>NotEmpty, IsString, IsUUID } from 'class-validator';
import { CommunicationChannel } from '@common/enums';

export class CustomerPhoneDto {
  @IsUUID()
  @IsNotEmpty()
  readonly customerId: string;

  @IsString()
  @IsNotEmpty()
  readonly phoneNumber: string;

  @IsEnum(CommunicationChannel)
  @IsNotEmpty()
  readonly communicationChannel: CommunicationChannel;

  readonly apiUrl?: string;

  constructor(
    customerId: string,
    phoneNumber: string,
    communicationChannel: CommunicationChannel,
    apiUrl?: string,
  ) {
    this.customerId = customerId;
    this.phoneNumber = phoneNumber;
    this.communicationChannel = communicationChannel;
    this.apiUrl = apiUrl;
  }
}
