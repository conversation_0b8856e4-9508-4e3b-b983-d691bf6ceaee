import { NectarResultadoBase } from '@business-base/infrastructure/dto/out/nectar-resultado-base-response.dto';

export class NectarGetBoletoAcordoResponse {
  GetBoletoAcordoResult: {
    BoletoAcordo?: {
      BoletoAcordo: BoletoAcordo;
    };
    Resultado: NectarResultadoBase;
  };
}

export class BoletoAcordo {
  CodigoBarras?: string;
  RetornoPDF?: string;
  LinhaDigitavel?: string;

  constructor(init?: Partial<BoletoAcordo>) {
    Object.assign(this, init);
  }
}
