import { NectarResultadoBase } from '@business-base/infrastructure/dto/out/nectar-resultado-base-response.dto';

class Informacao {
  Descricao: string;
  Valor: string | null;
}

class Divida {
  IDTRA: string;
  Descricao: string;
  NumeroTitulo: string;
  ChequeTitulo: string;
  DataVencimento: string;
  DataDevolucao: string;
  DataProjecao: string;
  DiasAtraso: string;
  ValorOriginal: string;
  ValorAtualizado: string;
  ContaTitulo: string;
  AgenciaTitulo: string;
  ScoreTitulo: string | null;
  InformacoesAdicionais: string;
  Produto: string;
  ValorMinimo: string;
  ValorDesconto: string;
}
class Contrato {
  Agrupamento: string;
  NomeFantasia: string;
  Contrato: Contrato;
  IDCON: string;
  Sequencia: string;
  IDSERV: string;
  ACWSR_CAR: string;
  Informacoes: {
    Info: Informacao[];
  };
  TipoNegociacao: string;
  UtilizaWS: string;
  Divida: {
    Divida: Divida[];
  };
  Campanha: string | null;
  Acordo: {
    Acordo: Acordo[];
  };
  AutorizacaoDeAcordo: string | null;
  Promessa: string | null;
}

class AcordoParcela {
  NumeroParcela: string;
  DataVencimento: string;
  ValorParcela: string;
  ValorPago: string;
  IDParcela: string;
  BoletoLiberado: string;
  EspeciePagamento: string;
  TaxaNegociacao: string;

  constructor(data: any) {
    this.NumeroParcela = data.NumeroParcela;
    this.DataVencimento = data.DataVencimento;
    this.ValorParcela = data.ValorParcela;
    this.ValorPago = data.ValorPago;
    this.IDParcela = data.IDParcela;
    this.BoletoLiberado = data.BoletoLiberado;
    this.EspeciePagamento = data.EspeciePagamento;
    this.TaxaNegociacao = data.TaxaNegociacao;
  }
}

class Acordo {
  AcordoAceito: string;
  CodigoAcordo: string;
  DataAceite: string;
  DataAcordo: string;
  IdAcordo: string;
  OrigemDoAcordo: string;
  VerificaAcordoAceito: string;
  DiasMaximoProjecao: string;
  AcordoParcela: {
    AcordoParcela: AcordoParcela[];
  };
  constructor(init?: Partial<Acordo>) {
    Object.assign(this, init);
  }
}

export class NectarGetDadosDividaResponse {
  GetDadosDividaResult: {
    NomeCliente: string;
    Server: string;
    Contrato: {
      Contrato: Contrato[];
    };
    Resultado: NectarResultadoBase;
  };
}
