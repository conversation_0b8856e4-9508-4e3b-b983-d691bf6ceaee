import { NectarResultadoBase } from '@business-base/infrastructure/dto/out/nectar-resultado-base-response.dto';

class NectarParcela {
  Numero: string;
  ValorEntrada: string;
  ValorDemaisParcelas: string;
  DataVencimento: string;
  ValorNegociarParcela: string;
  ValorDescontoNaCorrecaoParcela: string;
  ValorDescontoNoPrincipalParcela: string;
  TaxaNegociacao: string;
  ValorTotalAcordo: string;
}

export class NectarOpcoesNegociacao {
  Plano: string;
  CodigoFaixa: string;
  DescricaoFaixa: string;
  ParcelasNum: string;
  ValorDesconto: string;
  ValorOriginal: string;
  ValorCorrigido: string;
  ValorNegociar: string;
  ValorDescontoNoPrincipal: string;
  PercentualDescontoAplicadoNoPrincipal: string;
  PercentualMaximoDeDescontoNoPrincipal: string;
  ValorDescontoNaCorrecao: string;
  PercentualDescontoAplicadoNaCorrecao: string;
  PercentualMaximoDeDescontoNaCorrecao: string;
  Parcelas: {
    Parcelas: NectarParcela[];
  };
  TipoNegociacao: string;
}

export class NectarDadosEmpresa {
  DDR: string;
  Numero0800: string;
  TelWatsapp: string | null;
  EmailQuiosque: string | null;
}

export class NectarGetOpcoesNegociacaoResponse {
  GetOpcoesNegociacaoResult: {
    OpcoesNegociacao: {
      OpcoesNegociacao: NectarOpcoesNegociacao[];
    };
    DadosEmpresa: {
      DadosEmpresa: NectarDadosEmpresa;
    };
    Resultado: NectarResultadoBase;
  };
}
