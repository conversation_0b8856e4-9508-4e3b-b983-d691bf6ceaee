export class NectarGravarNegociacaoRequest {
  idCon: string;
  idServ: string;
  titulos: string;
  plano: string;
  codigoFaixa: string;
  descricaoFaixa: string;
  parcelasNum: string;
  valordesconto: string;
  vencimentoprimeira: string;
  valorprimeira: string;
  valororiginal: string;
  valorcorrigido: string;
  valornegociar: string;
  valordemais: string;
  tiponegociacao: string;
  boletodisponivel: string;
  tpDesconto: string;
  percDescAplicNoPrincipal: string;
  percDescAplicNaCorrecao: string;
  codigoParceiro: string;
  codigoToken: string;

  constructor(init?: Partial<NectarGravarNegociacaoRequest>) {
    Object.assign(this, init);
  }
}
