import { MessageType, RoleType } from '@common/enums';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsString,
  IsUUID,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export class MessageHistoryResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly role: RoleType;

  @IsString()
  @IsNotEmpty()
  readonly messageText: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsString()
  @IsNotEmpty()
  readonly lang: string;

  @IsString()
  readonly fileUrl?: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  @IsDate()
  @IsOptional()
  readonly sent_at: Date;

  @IsDate()
  @IsOptional()
  readonly time_to_go: Date;

  @IsBoolean()
  @IsOptional()
  readonly sent: boolean;

  constructor(
    id: string,
    role: RoleType,
    messageText: string,
    messageType: MessageType,
    lang: string,
    fileUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
    sent_at?: Date,
    time_to_go?: Date,
    sent?: boolean,
  ) {
    this.id = id;
    this.role = role;
    this.messageText = messageText;
    this.messageType = messageType;
    this.lang = lang;
    this.fileUrl = fileUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
    this.sent_at = sent_at;
    this.time_to_go = time_to_go;
    this.sent = sent;
  }
}
