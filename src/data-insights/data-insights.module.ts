import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { SQSService } from '@common/sqs/sqs.service';
import { ConversationController } from '@data-insights/application/controllers/conversation.controller';
import { ConversationUseCase } from '@data-insights/application/use-cases/conversation.use-case';
import { ConversationMessageUseCase } from '@data-insights/application/use-cases/conversation-message.use-case';
import { KnowledgeVectorUseCase } from '@data-insights/application/use-cases/knowledge-vector.use-case';
import { ConversationAdapter } from '@data-insights/infrastructure/adapters/db/conversation.adapter';
import { AgentAdapter } from '@data-insights/infrastructure/adapters/http/agent.adapter';
import { ConversationMessageAdapter } from '@data-insights/infrastructure/adapters/db/conversation-message.adapter';
import { KnowledgeVectorAdapter } from '@data-insights/infrastructure/adapters/db/knowledge-vector.adapter';
import { ConversationMessageController } from '@data-insights/application/controllers/conversation-message.controller';
import { ConversationalRagAdapter } from '@data-insights/infrastructure/adapters/conversational-rag.adapter';
import { VectorStoreAdapter } from '@data-insights/infrastructure/adapters/db/vector-store.adapter';
import { ChatUseCase } from '@data-insights/application/use-cases/chat.use-case';
import { SQSConsumerService } from '@data-insights/application/services/sqs-consumer.service';
import { KnowledgeVectorConsumerUseCase } from '@data-insights/application/use-cases/knowledge-vector-consumer.use-case';
import { GenerateConversationSubjectJobUseCase } from '@data-insights/application/use-cases/generate-conversation-subject-job.use-case';
import { ChainAdapter } from '@data-insights/infrastructure/adapters/chain.adapter';
import { CustomDynamoDBChatMessageHistory } from '@data-insights/application/services/custom-dynamo-chat.service';
import { SearchUseCase } from '@data-insights/application/use-cases/search.use-case';
import { OpenSearchAdapter } from '@data-insights/infrastructure/adapters/http/open-search.adapter';
import { SearchController } from '@data-insights/application/controllers/search.controller';

const providers = [
  { provide: 'ConversationPort', useClass: ConversationAdapter },
  { provide: 'AgentPort', useClass: AgentAdapter },
  { provide: 'ConversationMessagePort', useClass: ConversationMessageAdapter },
  { provide: 'KnowledgeVectorPort', useClass: KnowledgeVectorAdapter },
  { provide: 'ConversationalRagPort', useClass: ConversationalRagAdapter },
  { provide: 'VectorStorePort', useClass: VectorStoreAdapter },
  { provide: 'ChainPort', useClass: ChainAdapter },
  { provide: 'OpenSearchPort', useClass: OpenSearchAdapter },
  {
    provide: 'DynamoDBChatMessageHistoryFactory',
    useFactory: () => {
      return (tableName: string, partitionKey: string, sessionId: string) => {
        return new CustomDynamoDBChatMessageHistory(tableName, partitionKey, sessionId);
      };
    },
  },
  ConversationUseCase,
  ConversationMessageUseCase,
  KnowledgeVectorUseCase,
  ChatUseCase,
  KnowledgeVectorConsumerUseCase,
  GenerateConversationSubjectJobUseCase,
  SearchUseCase,
  SQSConsumerService,
  SQSService,
  DynamoService,
];

const controllers = [ConversationController, ConversationMessageController, SearchController];

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 50000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [httpModule, ScheduleModule.forRoot()],
  providers,
  controllers,
})
export class DataInsightsModule {}
