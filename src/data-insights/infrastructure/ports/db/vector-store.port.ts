import { knowledgeVector } from '@prisma/client';
import { VectorType } from '@common/enums';
import { PrismaVectorStore } from '@langchain/community/vectorstores/prisma';

export type VectorStoreResponse = PrismaVectorStore<
  knowledgeVector,
  'knowledgeVector',
  {
    id: true;
    content: true;
  },
  any
>;

export interface VectorStorePort {
  createVectorStore(vectorType: VectorType): { vectorStore: VectorStoreResponse; model: string };
}
