import { DbCommonPort } from '@common/db/ports/common.port';
import { ConversationEntity } from '@data-insights/domain/entities/conversation.entity';

export interface ConversationPort extends DbCommonPort<ConversationEntity> {
  getAllByOriginIdWithOrder(
    originId: string,
    orderBy: 'asc' | 'desc',
  ): Promise<ConversationEntity[]>;

  getConversationsWithoutSubject(limit: number): Promise<ConversationEntity[]>;
}
