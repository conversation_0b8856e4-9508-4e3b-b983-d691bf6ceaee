import { knowledgeVector } from '@prisma/client';
import { DbCommonPort } from '@common/db/ports/common.port';
import { KnowledgeVectorEntity } from '@data-insights/domain/entities/knowledge-vector.entity';
import { KnowledgeVectorData } from '@data-insights/misc/interfaces/in/knowledge-vector';

export interface KnowledgeVectorPort extends DbCommonPort<KnowledgeVectorEntity> {
  addVectors(data: KnowledgeVectorData[], embeddingModel: string): Promise<knowledgeVector[]>;
  deleteByRelatedUserIdAndOriginId(relatedUserId: string, originId: string): Promise<void>;
}
