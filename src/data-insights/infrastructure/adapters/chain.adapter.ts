import { Injectable } from '@nestjs/common';
import { ChatOpenAI } from '@langchain/openai';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { HumanMessage, SystemMessage } from '@langchain/core/messages';
import { ChainPort, ExecuteParams } from '@data-insights/infrastructure/ports/chain.port';

@Injectable()
export class ChainAdapter implements ChainPort {
  private readonly llm: ChatOpenAI;

  constructor() {
    this.llm = new ChatOpenAI({
      apiKey: String(process.env.OPENAI_API_KEY),
      temperature: 0,
    });
  }

  async executeSimpleChain(executeParams: ExecuteParams): Promise<string> {
    const { message, model, prompt } = executeParams;

    this.llm.model = model;
    const messages = [new SystemMessage(prompt), new HumanMessage(message)];

    const parser = new StringOutputParser();
    const chain = this.llm.pipe(parser);

    const result = await chain.invoke(messages);
    return result;
  }
}
