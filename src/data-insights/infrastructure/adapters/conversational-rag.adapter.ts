import { z } from 'zod';
import { Inject, Injectable } from '@nestjs/common';
import { ChatPromptTemplate, MessagesPlaceholder } from '@langchain/core/prompts';
import { createRetrievalChain } from 'langchain/chains/retrieval';
import { createStuffDocumentsChain } from 'langchain/chains/combine_documents';
import { ChatOpenAI } from '@langchain/openai';
import { StructuredOutputParser } from '@langchain/core/output_parsers';
import { logger } from '@edutalent/commons-sdk';

import {
  ConversationalRagPort,
  ConversationalRagType,
} from '@data-insights/infrastructure/ports/conversational-rag.port';
import { CustomDynamoDBChatMessageHistory } from '@data-insights/application/services/custom-dynamo-chat.service';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

type ResponseMessage = {
  text?: string;
  additional_kwargs?: any;
};

@Injectable()
export class ConversationalRagAdapter implements ConversationalRagPort {
  private readonly openai: ChatOpenAI;

  constructor(
    @Inject('DynamoDBChatMessageHistoryFactory')
    private readonly chatHistoryFactory: (
      tableName: string,
      partitionKey: string,
      sessionId: string,
    ) => CustomDynamoDBChatMessageHistory,
  ) {
    this.openai = new ChatOpenAI({
      apiKey: String(process.env.OPENAI_API_KEY),
      temperature: 0,
      maxTokens: 4096,
    });
  }

  async executeConversationRagChain(
    createData: ConversationalRagType,
  ): Promise<{ [k: string]: string | number | boolean | object }> {
    const { agent, sessionId, messageTable, retriever, input, isGlobal, originId } = createData;

    this.openai.model = agent.llmModel;

    const parser = StructuredOutputParser.fromZodSchema(this.getFormatIntructions());
    const clearPrompt = this.clearPrompt(agent.prompt, isGlobal);

    const prompt = await ChatPromptTemplate.fromMessages([
      ['system', clearPrompt],
      new MessagesPlaceholder('chat_history'),
      ['human', '{input}'],
    ]).partial({
      format_instructions: parser.getFormatInstructions(),
      originId: String(originId),
    });

    const questionAnswerChain = await createStuffDocumentsChain({
      llm: this.openai,
      prompt,
      outputParser: parser,
    });

    const ragChain = await createRetrievalChain({
      retriever,
      combineDocsChain: questionAnswerChain,
    });

    const chatHistory = this.createChatHistory(sessionId, messageTable);
    const chatHistoryMessages = await chatHistory.getMessages();
    const maxHistoryMessages = 5;
    const recentHistory = chatHistoryMessages.slice(-maxHistoryMessages);

    let aiAnswer: ResponseMessage;

    try {
      const ragResponse = await ragChain.invoke(
        {
          input,
          chat_history: recentHistory,
        },
        { configurable: { sessionId } },
      );

      aiAnswer = ragResponse?.answer;

      logger.debug(`AI Answer: ${JSON.stringify(ragResponse)}, global: ${isGlobal}`);

      if (aiAnswer?.text) {
        await chatHistory.addCustomMessages(input, aiAnswer?.text, aiAnswer?.additional_kwargs);
      }
    } catch (error) {
      logger.error('Conversation-rag-adapter', 'Data-Insights: Failed to generate IA response');
      return {
        text: error?.llmOutput,
      };
    }

    if (!aiAnswer?.text) {
      throw new BusinessException(
        'Conversation-rag-adapter',
        `Data-Insights: Failed to generate IA response after`,
        BusinessExceptionStatus.GENERAL_ERROR,
      );
    }

    return aiAnswer;
  }

  private clearPrompt(prompt: string, isGlobal: boolean): string {
    let clearPrompt = prompt;
    if (isGlobal) {
      clearPrompt +=
        '### Instruções Condicionais\n - Avalie triagens e candidatos de forma abrangente, considerando todas as triagens disponíveis.\n- Compare o desempenho e o alinhamento dos candidatos com os requisitos gerais das vagas.\n- Utilize este id como a triagem base `{originId}` como referência para contextualizar a análise.\n- Seja capaz de responder perguntas como: "Qual candidato de outra vaga se encaixaria nessa?". Para responder esta pergunta, avalie o desempenho dos candidatos de outras vagas e relacione com os requisitos da triagem atual.\n';
    } else {
      clearPrompt +=
        '\n### Instruções Condicionais\n - Restrinja a análise à triagem atual, limitando o escopo ao conjunto de dados da triagem base `{originId}`.\n- Responda apenas perguntas relacionadas aos candidatos e dados desta triagem.\n- Definição de uma pergunta global:\n-- Perguntas que exigem uma análise abrangente de todos os candidatos e triagens disponíveis.\n-- Exemplos: "Qual candidato de outra triagem que se encaixa nessa vaga?", "Quais candidatos de outras vagas têm o perfil mais alinhado com esta posição? "\n-- Exemplos de perguntas que não são globais: "Quem tem o melhor português?", "Qual candidato tem mais fit cultural?"\n-- Em geral, sao perguntas que citam outras triagens/vagas.\n- Caso o usuário faça uma pergunta com intuito de ser global, mas o isGlobal não estiver ativado:\n-- Informe o usuário no campo text do json que, para responder adequadamente à pergunta, o parâmetro isGlobal deve ser ativado.\n-- Exemplo de resposta para para esse cenario onde o isGlobal não está ativado: "A pergunta realizada parece exigir uma análise global. Para responder adequadamente, é necessário ativar a análise global no canto superior direito. Por favor, ative essa configuração e tente novamente."\n';
    }

    return clearPrompt;
  }

  private createChatHistory(
    sessionId: string,
    messageTable: string,
  ): CustomDynamoDBChatMessageHistory {
    return this.chatHistoryFactory(messageTable, 'id', sessionId);
  }

  private getFormatIntructions() {
    const CandidateSchema = z.object({
      userEmail: z.string().email(),
      triagemId: z.string(),
      userFullName: z.string(),
    });

    return z.object({
      text: z
        .string()
        .min(1)
        .describe(
          'Sua resposta detalhada aqui, com comparações, insights e contexto apropriado, incluindo o nome completo do usuário sempre que for possível.',
        ),
      additional_kwargs: z
        .object({
          candidates: z.array(CandidateSchema).optional(),
        })
        .optional(),
    });
  }
}
