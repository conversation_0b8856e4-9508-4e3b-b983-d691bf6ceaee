import { Injectable } from '@nestjs/common';
import { Embeddings } from '@langchain/core/embeddings';
import { OpenAIEmbeddings } from '@langchain/openai';
import { knowledgeVector, Prisma, PrismaClient } from '@prisma/client';
import { PrismaVectorStore } from '@langchain/community/vectorstores/prisma';
import { PrismaService } from '@common/prisma/prisma.service';
import { RecordStatus, VectorType } from '@common/enums';
import {
  VectorStorePort,
  VectorStoreResponse,
} from '@data-insights/infrastructure/ports/db/vector-store.port';

@Injectable()
export class VectorStoreAdapter implements VectorStorePort {
  private readonly prismaClient: PrismaClient;
  private readonly tableName = 'data_insights__knowledge_vector';
  private readonly vectorColumnName = 'vector';

  constructor(private readonly prisma: PrismaService) {
    this.prismaClient = this.prisma.client;
  }

  createVectorStore(vectorType: VectorType): { vectorStore: VectorStoreResponse; model: string } {
    const { embeddings, model } = this.getEmbeddings(vectorType);

    const vector = PrismaVectorStore.withModel<knowledgeVector>(this.prismaClient).create(
      embeddings,
      {
        prisma: Prisma,
        tableName: this.tableName as any,
        vectorColumnName: this.vectorColumnName,
        columns: {
          id: PrismaVectorStore.IdColumn,
          content: PrismaVectorStore.ContentColumn,
        },
        filter: {
          status: { equals: RecordStatus.ACTIVE },
        },
      },
    );

    return {
      vectorStore: vector,
      model,
    };
  }

  private getEmbeddings(vectorType: VectorType): { embeddings: Embeddings; model: string } {
    const apiKey = process.env.OPENAI_API_KEY.toString();
    const textModel = 'text-embedding-3-large';

    switch (vectorType) {
      case VectorType.TEXT:
        return {
          embeddings: new OpenAIEmbeddings({
            apiKey,
            model: textModel,
          }),
          model: textModel,
        };
      default:
        return {
          embeddings: new OpenAIEmbeddings({
            apiKey,
            model: textModel,
          }),
          model: textModel,
        };
    }
  }
}
