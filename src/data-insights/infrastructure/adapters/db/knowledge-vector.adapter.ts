import { Injectable } from '@nestjs/common';
import { knowledgeVector } from '@prisma/client';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { KnowledgeVectorEntity } from '@data-insights/domain/entities/knowledge-vector.entity';
import { KnowledgeVectorPort } from '@data-insights/infrastructure/ports/db/knowledge-vector.port';
import { KnowledgeVectorData } from '@data-insights/misc/interfaces/in/knowledge-vector';
import { RecordStatus } from '@common/enums';

@Injectable()
export class KnowledgeVectorAdapter
  extends PrismaCommonAdapter<KnowledgeVectorEntity>
  implements KnowledgeVectorPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'knowledgeVector');
  }

  async addVectors(
    data: KnowledgeVectorData[],
    embeddingModel: string,
  ): Promise<knowledgeVector[]> {
    const vectorData = data.map(vector => ({
      businessUserId: vector.businessUserId,
      itemId: vector.itemId,
      originId: vector.originId,
      scopeId: vector.scopeId,
      relatedUserId: vector?.relatedUserId,
      type: vector.type,
      content: vector.vector.pageContent,
      isBackfill: vector.isBackfill,
      embeddingModel,
    }));

    return this.prisma.client.knowledgeVector.createManyAndReturn({
      data: vectorData,
    });
  }

  async deleteByRelatedUserIdAndOriginId(relatedUserId: string, originId: string): Promise<void> {
    await this.prisma.client.knowledgeVector.updateMany({
      where: {
        relatedUserId,
        originId,
      },
      data: {
        status: RecordStatus.DELETED,
      },
    });
  }
}
