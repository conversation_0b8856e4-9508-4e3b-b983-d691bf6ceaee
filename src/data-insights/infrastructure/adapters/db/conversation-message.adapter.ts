import { Injectable } from '@nestjs/common';
import {
  DynamoDBDocumentClient,
  GetCommand,
  GetCommandInput,
  PutCommand,
} from '@aws-sdk/lib-dynamodb';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import { ConversationMessageEntity } from '@data-insights/domain/entities/conversation-message.entity';
import { ConversationMessagePort } from '@data-insights/infrastructure/ports/db/conversation-message.port';

@Injectable()
export class ConversationMessageAdapter implements ConversationMessagePort {
  private readonly dynamoClient: DynamoDBDocumentClient;
  private readonly DYNAMO_TABLE_NAME = 'transcendence_conversation_messages';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async getById(id: string): Promise<ConversationMessageEntity> {
    const params: GetCommandInput = {
      TableName: this.DYNAMO_TABLE_NAME,
      Key: {
        id,
      },
    };

    try {
      const { Item } = await this.dynamoClient.send(new GetCommand(params));
      return Item as ConversationMessageEntity;
    } catch (error) {
      const errorResponse = {
        message: `Error while fetching conversation message for id: ${id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }

  async save(entity: ConversationMessageEntity): Promise<ConversationMessageEntity> {
    const existingMessages = await this.getById(entity.id);

    try {
      if (existingMessages) {
        const messages = [...existingMessages.messages, ...entity.messages];
        await this.dynamoClient.send(
          new PutCommand({
            TableName: this.DYNAMO_TABLE_NAME,
            Item: {
              id: entity.id,
              messages,
            },
          }),
        );

        return entity;
      }

      await this.dynamoClient.send(
        new PutCommand({
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: entity.id,
            messages: entity.messages,
          },
        }),
      );
      return entity;
    } catch (error) {
      throw new DynamoException({
        message: `Error while saving conversation message for id: ${entity.id}, messageCount: ${
          entity.messages?.length || 0
        }. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      });
    }
  }
}
