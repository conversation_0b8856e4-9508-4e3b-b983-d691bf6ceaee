import { Injectable } from '@nestjs/common';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { RecordStatus } from '@common/enums';
import { ConversationEntity } from '@data-insights/domain/entities/conversation.entity';
import { ConversationPort } from '@data-insights/infrastructure/ports/db/conversation.port';

@Injectable()
export class ConversationAdapter
  extends PrismaCommonAdapter<ConversationEntity>
  implements ConversationPort
{
  constructor(private readonly prisma: PrismaService) {
    super(prisma, 'conversation');
  }

  async getAllByOriginIdWithOrder(
    originId: string,
    orderBy: 'asc' | 'desc',
  ): Promise<ConversationEntity[]> {
    const conversations = await this.prisma.client.conversation.findMany({
      where: {
        originId,
      },
      orderBy: {
        createdAt: orderBy,
      },
    });

    return conversations.map(conversation => ({
      ...conversation,
      status: conversation.status as RecordStatus,
    }));
  }

  async getConversationsWithoutSubject(limit: number): Promise<ConversationEntity[]> {
    const conversations = await this.prisma.client.conversation.findMany({
      take: limit,
      where: {
        subject: null,
      },
    });

    return conversations.map(conversation => ({
      ...conversation,
      status: conversation.status as RecordStatus,
    }));
  }
}
