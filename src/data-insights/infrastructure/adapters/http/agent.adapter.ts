import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { ConversationAgentName, LangType } from '@common/enums';
import { handleHttpError } from '@common/utils/handle-http-error';
import { AgentPort } from '@data-insights/infrastructure/ports/http/agent.port';
import { ResponseAgentDto } from '@data-insights/application/dto/out/response-agent.dto';

@Injectable()
export class AgentAdapter implements AgentPort {
  private readonly intelligenceServiceUrl: string;

  constructor(private readonly httpService: HttpService) {
    this.intelligenceServiceUrl = String(process.env.INTELLIGENCE_SERVICE_URL);
  }

  async getByNameAndLang(name: ConversationAgentName, lang: LangType): Promise<ResponseAgentDto> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/agents/name/${name}/lang/${lang}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));
      return data.data;
    } catch (error) {
      handleHttpError(error, 'Agent-adapter');
    }
  }

  async findById(id: string): Promise<ResponseAgentDto> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/agents/${id}`;
      logger.info(`Posting data to ${url}`);
      //TODO: set authentication
      const headers = {
        'Content-Type': 'application/json',
      };

      const { data } = await lastValueFrom(this.httpService.get(url, { headers }));

      return data.data;
    } catch (error) {
      handleHttpError(error, 'Agent-adapter');
    }
  }
}
