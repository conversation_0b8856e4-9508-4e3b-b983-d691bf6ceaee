import { Inject, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { randomUUID as uuidv4 } from 'crypto';
import { logger } from '@edutalent/commons-sdk';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { ConversationPort } from '@data-insights/infrastructure/ports/db/conversation.port';
import { CreateConversationDto } from '@data-insights/application/dto/in/create-conversation.dto';
import { ResponseConversationDto } from '@data-insights/application/dto/out/response-conversation.dto';
import { ConversationEntity } from '@data-insights/domain/entities/conversation.entity';
import { AgentPort } from '@data-insights/infrastructure/ports/http/agent.port';
import { ConversationMessageUseCase } from '@data-insights/application/use-cases/conversation-message.use-case';

@Injectable()
export class ConversationUseCase {
  constructor(
    @Inject('ConversationPort') private readonly conversationAdapter: ConversationPort,
    @Inject('AgentPort') private readonly agentAdapter: AgentPort,
    private readonly conversationMessageUseCase: ConversationMessageUseCase,
  ) {}

  async create(createConversationDto: CreateConversationDto): Promise<ResponseConversationDto> {
    const { agentName, lang, businessUserId, originId, type } = createConversationDto;
    logger.info(
      `Creating conversation for originId: ${originId} and businessUserId: ${businessUserId}`,
    );

    const { id: agentId } = await this.agentAdapter.getByNameAndLang(agentName, lang);

    const conversation = await this.conversationAdapter.create({
      id: uuidv4(),
      sessionId: uuidv4(),
      businessUserId,
      originId,
      type,
      agentId,
    });

    return {
      ...this.createResponseDto(conversation),
      messages: [],
    };
  }

  async findById(conversationId: string): Promise<ResponseConversationDto> {
    logger.info(`Finding conversation with id: ${conversationId}`);

    const conversation = await this.findConversationEntity(conversationId);
    const messages = await this.conversationMessageUseCase.findById(conversation.sessionId);

    const conversationDto = this.createResponseDto(conversation);

    return {
      ...conversationDto,
      messages:
        messages?.messages?.map(message => ({
          text: message.text,
          type: message.type,
          additional_kwargs: message.additional_kwargs,
        })) || [],
    };
  }

  async listAllByOriginId(originId: string): Promise<ResponseConversationDto[]> {
    logger.info(`Listing all conversations for originId: ${originId}`);
    const conversations = await this.conversationAdapter.getAllByOriginIdWithOrder(
      originId,
      'desc',
    );

    return conversations.map(conversation => ({
      ...this.createResponseDto(conversation),
      messages: [],
    }));
  }

  async listAllWithoutSubject(limit: number): Promise<ResponseConversationDto[]> {
    logger.info(`Listing all conversations without subject, limit: ${limit}`);
    const conversations = await this.conversationAdapter.getConversationsWithoutSubject(limit);

    return conversations.map(conversation => {
      return this.createResponseDto(conversation);
    });
  }

  async updateSubject(conversationId: string, subject: string): Promise<ResponseConversationDto> {
    logger.info(`Updating conversation subject with id: ${conversationId} to subject: ${subject}`);
    const conversation = await this.findConversationEntity(conversationId);

    conversation.subject = subject;
    await this.conversationAdapter.update(conversation);

    return this.createResponseDto({ ...conversation, subject });
  }

  async delete(conversationId: string): Promise<ResponseConversationDto> {
    logger.info(`Deleting conversation with id: ${conversationId}`);
    await this.findConversationEntity(conversationId);

    const deletedConversation = await this.conversationAdapter.delete(conversationId);
    return this.createResponseDto(deletedConversation);
  }

  private async findConversationEntity(conversationId: string): Promise<ConversationEntity> {
    const conversation = await this.conversationAdapter.get(conversationId);
    if (!conversation) {
      throw new BusinessException(
        'Conversation-use-case',
        `Conversation with id ${conversationId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }
    return conversation;
  }

  private createResponseDto(conversationEntity: ConversationEntity): ResponseConversationDto {
    return plainToClass(ResponseConversationDto, conversationEntity);
  }
}
