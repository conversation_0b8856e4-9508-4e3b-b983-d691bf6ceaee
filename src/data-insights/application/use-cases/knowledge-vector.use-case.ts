import { Inject, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { logger } from '@edutalent/commons-sdk';
import { VectorType } from '@common/enums';
import { KnowledgeVectorDto } from '@data-insights/application/dto/knowledge-vector.dto';
import { KnowledgeVectorEntity } from '@data-insights/domain/entities/knowledge-vector.entity';
import { KnowledgeVectorPort } from '@data-insights/infrastructure/ports/db/knowledge-vector.port';
import { VectorStorePort } from '@data-insights/infrastructure/ports/db/vector-store.port';

@Injectable()
export class KnowledgeVectorUseCase {
  constructor(
    @Inject('KnowledgeVectorPort') private readonly knowledgeVectorAdapter: KnowledgeVectorPort,
    @Inject('VectorStorePort') private readonly vectorStoreAdapter: VectorStorePort,
  ) {}

  async createMany(createKnowledgeVector: KnowledgeVectorDto[], type: VectorType): Promise<void> {
    const originIds = createKnowledgeVector.map(knowledgeVector => knowledgeVector.originId);
    logger.info(`Creating knowledge vector for originIds: ${originIds}`);

    const { vectorStore, model } = this.vectorStoreAdapter.createVectorStore(type);

    await vectorStore.addModels(
      await this.knowledgeVectorAdapter.addVectors(createKnowledgeVector, model),
    );
  }

  async listByOriginId(originId: string): Promise<KnowledgeVectorDto[]> {
    logger.info(`Listing knowledge vectors for originId: ${originId}`);

    const knowledgeVectors = await this.knowledgeVectorAdapter.getAll({ originId });

    return knowledgeVectors.map(knowledgeVector => {
      return this.createResponseDto(knowledgeVector);
    });
  }

  async listByBusinessUserId(businessUserId: string): Promise<KnowledgeVectorDto[]> {
    logger.info(`Listing knowledge vectors for businessUserId: ${businessUserId}`);

    const knowledgeVectors = await this.knowledgeVectorAdapter.getAll({ businessUserId });

    return knowledgeVectors.map(knowledgeVector => {
      return this.createResponseDto(knowledgeVector);
    });
  }

  async listByScopeId(scopeId: string): Promise<KnowledgeVectorDto[]> {
    logger.info(`Listing knowledge vectors for scopeId: ${scopeId}`);

    const knowledgeVectors = await this.knowledgeVectorAdapter.getAll({ scopeId });

    return knowledgeVectors.map(knowledgeVector => {
      return this.createResponseDto(knowledgeVector);
    });
  }

  async deleteByRelatedUserIdAndOriginId(relatedUserId: string, originId: string): Promise<void> {
    logger.info(
      `Deleting knowledge vectors for relatedUserId: ${relatedUserId} and originId: ${originId}`,
    );

    await this.knowledgeVectorAdapter.deleteByRelatedUserIdAndOriginId(relatedUserId, originId);
  }

  private createResponseDto(knowledgeVectorEntity: KnowledgeVectorEntity): KnowledgeVectorDto {
    return plainToClass(KnowledgeVectorDto, knowledgeVectorEntity);
  }
}
