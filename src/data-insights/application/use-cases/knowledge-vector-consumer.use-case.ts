import { Injectable } from '@nestjs/common';
import { Document } from '@langchain/core/documents';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { VectorType } from '@common/enums';
import { KnowledgeVectorUseCase } from '@data-insights/application/use-cases/knowledge-vector.use-case';

export type KnowledgeVectorConsumerUseCaseMessage = {
  originId: string;
  businessUserId: string;
  scopeId?: string;
  itemId: string;
  relatedUserId: string;
  type: VectorType;
  isBackfill?: boolean;
  jsonData: any;
};

@Injectable()
export class KnowledgeVectorConsumerUseCase {
  constructor(private readonly knowledgeVectorUseCase: KnowledgeVectorUseCase) {}

  async saveTextVectorData(messages: KnowledgeVectorConsumerUseCaseMessage[]): Promise<void> {
    const uniqueMessages = this.filterDuplicateObjects(messages);

    const deletionTasks = uniqueMessages.map(message =>
      this.knowledgeVectorUseCase.deleteByRelatedUserIdAndOriginId(
        message.relatedUserId,
        message.originId,
      ),
    );

    const splitTasks = uniqueMessages.map(message => this.splitJsonData(message.jsonData));

    await Promise.all(deletionTasks);

    const allSplitTexts = await Promise.all(splitTasks);

    const documents = allSplitTexts.flatMap((splitTexts, index) => {
      const message = uniqueMessages[index];
      return splitTexts.map(splitText => ({
        businessUserId: message.businessUserId,
        originId: message.originId,
        scopeId: message?.scopeId,
        itemId: message.itemId,
        relatedUserId: message?.relatedUserId,
        type: message.type,
        vector: splitText,
        isBackfill: message?.isBackfill ?? false,
      }));
    });

    await this.knowledgeVectorUseCase.createMany(documents, VectorType.TEXT);
  }

  private filterDuplicateObjects(
    data: KnowledgeVectorConsumerUseCaseMessage[],
  ): KnowledgeVectorConsumerUseCaseMessage[] {
    const seen = new Set();
    return data.filter(item => {
      const key = `${item.originId}-${item.relatedUserId}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private splitJsonData(jsonData: any): Promise<Document[]> {
    const textData = JSON.stringify(jsonData);
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: 2000,
      chunkOverlap: 200,
    });
    return splitter.createDocuments([textData]);
  }
}
