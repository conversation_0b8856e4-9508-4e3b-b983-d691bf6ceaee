import { Inject, Injectable } from '@nestjs/common';
import { plainToClass } from 'class-transformer';
import { CreateConversationMessageDto } from '@data-insights/application/dto/in/create-conversation-message.dto';
import { ResponseConversationMessageDto } from '@data-insights/application/dto/out/response-conversation-message.dto';
import { ConversationMessageEntity } from '@data-insights/domain/entities/conversation-message.entity';
import { ConversationMessagePort } from '@data-insights/infrastructure/ports/db/conversation-message.port';

@Injectable()
export class ConversationMessageUseCase {
  constructor(
    @Inject('ConversationMessagePort')
    private readonly conversationMessageAdapter: ConversationMessagePort,
  ) {}

  async save(
    conversationMessage: CreateConversationMessageDto,
  ): Promise<ResponseConversationMessageDto> {
    const conversationMessageEntity = await this.conversationMessageAdapter.save({
      id: conversationMessage.sessionId,
      messages: [
        {
          text: conversationMessage.text,
          type: conversationMessage.type,
        },
      ],
    });

    return this.createResponseDto(conversationMessageEntity);
  }

  async findById(sessionId: string): Promise<ResponseConversationMessageDto> {
    const messages = await this.conversationMessageAdapter.getById(sessionId);

    const jsonParsedMessages = messages?.messages?.map(message => ({
      ...message,
      additional_kwargs: message?.additional_kwargs
        ? JSON.parse(message.additional_kwargs as string)
        : {},
    }));

    return this.createResponseDto({ ...messages, messages: jsonParsedMessages });
  }

  private createResponseDto(
    conversationMessageEntity: ConversationMessageEntity,
  ): ResponseConversationMessageDto {
    return plainToClass(ResponseConversationMessageDto, conversationMessageEntity);
  }
}
