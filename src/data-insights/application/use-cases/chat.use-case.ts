import { Inject, Injectable } from '@nestjs/common';
import { ConversationUseCase } from '@data-insights/application/use-cases/conversation.use-case';
import { ConversationalRagPort } from '@data-insights/infrastructure/ports/conversational-rag.port';
import { AgentPort } from '@data-insights/infrastructure/ports/http/agent.port';
import { VectorStorePort } from '@data-insights/infrastructure/ports/db/vector-store.port';
import { ConversationMessageType, VectorType } from '@common/enums';
import { ChatDto } from '@data-insights/application/dto/in/chat.dto';
import { ChatResponseDto } from '@data-insights/application/dto/out/chat-response.dto';

@Injectable()
export class ChatUseCase {
  private readonly messageTable = 'transcendence_conversation_messages';

  constructor(
    private readonly conversationUseCase: ConversationUseCase,
    @Inject('ConversationalRagPort') private readonly conversationRagAdapter: ConversationalRagPort,
    @Inject('AgentPort') private readonly agentAdapter: AgentPort,
    @Inject('VectorStorePort') private readonly vectorStoreAdapter: VectorStorePort,
  ) {}

  async chat(chatDto: ChatDto): Promise<ChatResponseDto> {
    const { conversationId, input, isGlobal } = chatDto;

    const { originId, sessionId, agentId, businessUserId } =
      await this.conversationUseCase.findById(conversationId);
    const { llmModel, backstory } = await this.agentAdapter.findById(agentId);

    const filter = this.getRetrieverFilter(originId, businessUserId, isGlobal);

    const { vectorStore } = this.vectorStoreAdapter.createVectorStore(VectorType.TEXT);
    const dynamicK = isGlobal ? 35 : 25;
    const retriever = vectorStore.asRetriever({
      k: dynamicK,
      filter,
    });

    const responseMessage = await this.conversationRagAdapter.executeConversationRagChain({
      agent: {
        llmModel,
        prompt: backstory,
      },
      sessionId,
      messageTable: this.messageTable,
      retriever,
      input,
      originId,
      isGlobal: isGlobal ?? false,
    });

    return {
      ...responseMessage,
      type: ConversationMessageType.AI,
    };
  }

  private getRetrieverFilter(originId: string, businessUserId: string, isGlobal?: boolean) {
    return isGlobal
      ? { business_user_id: { equals: businessUserId } }
      : { origin_id: { equals: originId } };
  }
}
