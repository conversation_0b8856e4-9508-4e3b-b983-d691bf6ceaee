import { Inject, Injectable } from '@nestjs/common';
import { ConversationUseCase } from '@data-insights/application/use-cases/conversation.use-case';
import { ConversationMessageUseCase } from '@data-insights/application/use-cases/conversation-message.use-case';
import { <PERSON>ron } from '@nestjs/schedule';
import { ChainPort } from '@data-insights/infrastructure/ports/chain.port';
import { ResponseConversationMessageDto } from '@data-insights/application/dto/out/response-conversation-message.dto';

@Injectable()
export class GenerateConversationSubjectJobUseCase {
  private readonly minimumMessages = 2;
  private readonly takConversationLimit = 100;

  constructor(
    private readonly conversationUseCase: ConversationUseCase,
    private readonly conversationMessageUseCase: ConversationMessageUseCase,
    @Inject('ChainPort') private readonly chainAdapter: ChainPort,
  ) {}

  @Cron('*/60 * * * * *', {
    timeZone: 'UTC',
    name: 'every-minute-generate-conversation-subject-task',
  })
  private async startJob(): Promise<void> {
    const conversations = await this.conversationUseCase.listAllWithoutSubject(
      this.takConversationLimit,
    );

    if (!conversations?.length) {
      return;
    }

    await Promise.all(
      conversations.map(async conversation => {
        const messages = await this.conversationMessageUseCase.findById(conversation.sessionId);
        if (!messages?.messages?.length || messages?.messages?.length < this.minimumMessages) {
          return;
        }

        const message = this.generateInlineConversation(messages);

        const subject = await this.chainAdapter.executeSimpleChain({
          message,
          model: 'gpt-4o-mini',
          prompt:
            'Generate a topic for the conversation, according to the following messages, limit yourself to 10 words. (Note: generate the subject in the language used in the conversation)',
        });

        await this.conversationUseCase.updateSubject(conversation.id, subject);
      }),
    );
  }

  private generateInlineConversation(conversationMessage: ResponseConversationMessageDto): string {
    return conversationMessage.messages
      .map(message => `type: ${message.type}\n text:${message.text}\n`)
      .join(' ');
  }
}
