import { Body, Controller, Delete, Get, Param, Post, Put, Version } from '@nestjs/common';
import { AccountRole, UserRoleInAccount } from '@common/enums';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { BaseResponse } from '@common/http/responses/types/base-response.type';
import { ConversationUseCase } from '@data-insights/application/use-cases/conversation.use-case';
import { CreateConversationDto } from '@data-insights/application/dto/in/create-conversation.dto';
import { ResponseConversationDto } from '@data-insights/application/dto/out/response-conversation.dto';

@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.__SYSTEM__)
@Controller('data-insights/conversations')
export class ConversationController {
  constructor(private readonly conversationUseCase: ConversationUseCase) {}

  @Post()
  @Version('1')
  async create(
    @Body() createConversationDto: CreateConversationDto,
  ): Promise<BaseResponse<ResponseConversationDto>> {
    const conversation = await this.conversationUseCase.create(createConversationDto);

    return {
      statusCode: 201,
      data: conversation,
    };
  }

  @Get('/:coversationId')
  @Version('1')
  async findById(
    @Param('coversationId') coversationId: string,
  ): Promise<BaseResponse<ResponseConversationDto>> {
    const conversation = await this.conversationUseCase.findById(coversationId);

    return {
      statusCode: 200,
      data: conversation,
    };
  }

  @Get('originId/:originId')
  @Version('1')
  async listAllByOriginId(
    @Param('originId') originId: string,
  ): Promise<BaseResponse<ResponseConversationDto[]>> {
    const conversations = await this.conversationUseCase.listAllByOriginId(originId);

    return {
      statusCode: 200,
      data: conversations,
    };
  }

  @Put('/:conversationId/subject')
  @Version('1')
  async updateSubject(
    @Param('conversationId') conversationId: string,
    @Body('subject') subject: string,
  ): Promise<any> {
    const conversation = await this.conversationUseCase.updateSubject(conversationId, subject);

    return {
      statusCode: 200,
      data: conversation,
    };
  }

  @Delete('/:conversationId')
  @Version('1')
  async delete(
    @Param('conversationId') conversationId: string,
  ): Promise<BaseResponse<ResponseConversationDto>> {
    const conversation = await this.conversationUseCase.delete(conversationId);

    return {
      statusCode: 200,
      data: conversation,
    };
  }
}
