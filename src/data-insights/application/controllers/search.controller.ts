import { SearchSimilarDto } from '@data-insights/application/dto/in/search-similar.dto';
import { IndexDocumentDto } from '@data-insights/application/dto/in/index-document.dto';
import { SearchUseCase } from '@data-insights/application/use-cases/search.use-case';
import { Body, Controller, HttpStatus, Post, Version } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk/dist/logger/logger';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { AccountRole, UserRoleInAccount } from '@common/enums';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';

@Controller('data-insights/search')
export class SearchController {
  constructor(private readonly searchUseCase: SearchUseCase) {}

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Index a document' })
  @ApiResponse({
    status: 201,
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        data: {
          type: 'object',
          properties: {
            index: { type: 'string', example: 'jobs' },
            id: { type: 'string', example: 'oCWCepcBMxMIFjZH6INQ' },
            version: { type: 'number', example: 1 },
            result: { type: 'string', example: 'created' },
          },
        },
      },
    },
  })
  @ApiBody({ type: IndexDocumentDto })
  @AccountRoles(AccountRole.__SYSTEM__)
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @Post('index')
  @Version('1')
  async index(@Body() indexDocumentDto: IndexDocumentDto): Promise<any> {
    try {
      const document = await this.searchUseCase.indexDocument(indexDocumentDto);

      return {
        statusCode: 201,
        data: document,
      };
    } catch (error) {
      logger.error(`SearchController: error indexing document: ${JSON.stringify(error)}`);
      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
      };
    }
  }

  @ApiBearerAuth()
  @ApiOperation({ summary: 'Search similar documents' })
  @ApiResponse({
    status: 201,
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number' },
        data: { type: 'array', items: { type: 'object' } },
      },
    },
  })
  @ApiBody({ type: SearchSimilarDto })
  @AccountRoles(AccountRole.__SYSTEM__)
  @UserRolesInAccount(UserRoleInAccount.ADMIN)
  @Post('')
  @Version('1')
  async searchSimilar(@Body() searchSimilarDto: SearchSimilarDto): Promise<any> {
    try {
      const result = await this.searchUseCase.searchSimilar(searchSimilarDto);

      return {
        statusCode: 201,
        data: result,
      };
    } catch (error) {
      logger.error(`SearchController: error searching similar: ${JSON.stringify(error)}`);

      return {
        statusCode: HttpStatus.BAD_REQUEST,
        message: error.message,
        data: [],
      };
    }
  }
}
