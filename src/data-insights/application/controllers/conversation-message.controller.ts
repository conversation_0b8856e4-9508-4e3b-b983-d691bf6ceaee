import { Body, Controller, Post, Version } from '@nestjs/common';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { AccountRole, UserRoleInAccount } from '@common/enums';
import { BaseResponse } from '@common/http/responses/types/base-response.type';
import { ConversationMessageUseCase } from '@data-insights/application/use-cases/conversation-message.use-case';
import { CreateConversationMessageDto } from '@data-insights/application/dto/in/create-conversation-message.dto';
import { ChatUseCase } from '@data-insights/application/use-cases/chat.use-case';
import { ChatDto } from '@data-insights/application/dto/in/chat.dto';
import { ResponseConversationMessageDto } from '@data-insights/application/dto/out/response-conversation-message.dto';
import { ChatResponseDto } from '@data-insights/application/dto/out/chat-response.dto';

@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.__SYSTEM__)
@Controller('data-insights/conversations/messages')
export class ConversationMessageController {
  constructor(
    private readonly conversationMessageUseCase: ConversationMessageUseCase,
    private readonly chatUseCase: ChatUseCase,
  ) {}

  @Post()
  @Version('1')
  async create(
    @Body() createConversationMessageDto: CreateConversationMessageDto,
  ): Promise<BaseResponse<ResponseConversationMessageDto>> {
    const conversationMessage = await this.conversationMessageUseCase.save(
      createConversationMessageDto,
    );

    return {
      statusCode: 201,
      data: conversationMessage,
    };
  }

  @Post('/chat')
  @Version('1')
  async chat(@Body() chatDto: ChatDto): Promise<BaseResponse<ChatResponseDto>> {
    const responseMessage = await this.chatUseCase.chat(chatDto);

    return {
      statusCode: 200,
      data: responseMessage,
    };
  }
}
