import { Message } from '@aws-sdk/client-sqs';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { SQSService } from '@common/sqs/sqs.service';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { sanitizeMessageBody } from '@common/utils/message-sanitization.util';
import { VectorType } from '@common/enums';
import { randomUUID as uuidv4 } from 'crypto';
import {
  KnowledgeVectorConsumerUseCase,
  KnowledgeVectorConsumerUseCaseMessage,
} from '@data-insights/application/use-cases/knowledge-vector-consumer.use-case';

@Injectable()
export class SQSConsumerService implements OnModuleInit {
  private readonly sqsBatchSize = 10;

  constructor(
    private readonly sqsService: SQSService,
    private readonly knowledgeVectorConsumerUseCase: KnowledgeVectorConsumerUseCase,
  ) {}

  onModuleInit() {
    logger.info('Starting SQS consumer for the data_insights module...');

    const queueConsumerFunctionMap = new Map<string, (messages: Message[]) => void>();

    queueConsumerFunctionMap.set(
      process.env.DATA_INSIGHTS_VECTOR_STORE_HIRING_QUEUE_URL,
      this.consumeData.bind(this),
    );

    // TODO: Add more queues here for other products: collectcash, saleszap, etc.

    for (const [queueUrl, handler] of queueConsumerFunctionMap.entries()) {
      this.sqsService.createConsumer(queueUrl, this.sqsBatchSize, handler);
    }
  }

  private async consumeData(messages: Message[]): Promise<void> {
    const batchStartTime = Date.now();
    const batchCorrelationId = `batch-${uuidv4()}`;

    logger.info('SQS vector data consumer batch started', {
      traceId: batchCorrelationId,
      operation: 'consumeVectorData',
      batchSize: messages.length,
      timestamp: new Date().toISOString(),
      layer: 'SQS_CONSUMER',
    });

    try {
      const allData: KnowledgeVectorConsumerUseCaseMessage[] = messages.map(message =>
        JSON.parse(message.Body),
      );
      const textData = allData.filter(data => data.type === VectorType.TEXT);

      logger.info('Vector data messages parsed', {
        traceId: batchCorrelationId,
        operation: 'consumeVectorData',
        totalMessages: allData.length,
        textDataCount: textData.length,
        messageBody: sanitizeMessageBody(allData),
        layer: 'SQS_CONSUMER',
      });

      if (textData?.length) {
        await CorrelationContextService.run(
          { traceId: batchCorrelationId, layer: 'SQS_CONSUMER', operation: 'saveTextVectorData' },
          async () => {
            await this.knowledgeVectorConsumerUseCase.saveTextVectorData(textData);
          },
        );
      }

      const batchDuration = Date.now() - batchStartTime;
      logger.info('SQS vector data consumer batch completed successfully', {
        traceId: batchCorrelationId,
        operation: 'consumeVectorData',
        batchSize: messages.length,
        textDataCount: textData?.length || 0,
        duration: `${batchDuration}ms`,
        timestamp: new Date().toISOString(),
        layer: 'SQS_CONSUMER',
      });
    } catch (error) {
      const batchDuration = Date.now() - batchStartTime;
      logger.error('Error processing vector data messages', {
        traceId: batchCorrelationId,
        operation: 'consumeVectorData',
        batchSize: messages.length,
        duration: `${batchDuration}ms`,
        error: error.message,
        errorType: error.constructor?.name,
        severity: 'HIGH',
        retryable: true,
        stack: process.env.NODE_ENV !== 'production' ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        layer: 'SQS_CONSUMER',
      });
    }
  }
}
