import { v4 as uuidv4 } from 'uuid';
import { DynamoDBChatMessageHistory } from '@langchain/community/stores/message/dynamodb';
import { AIMessage, HumanMessage } from '@langchain/core/messages';
import { Injectable } from '@nestjs/common';

@Injectable()
export class CustomDynamoDBChatMessageHistory extends DynamoDBChatMessageHistory {
  constructor(tableName: string, partitionKey: string, sessionId: string) {
    super({
      tableName,
      partitionKey,
      sessionId,
      messageAttributeName: 'messages',
    });
  }

  async addCustomMessages(
    humanMessage: string,
    iaMessage: string,
    additionalData?: Record<string, unknown>,
  ): Promise<void> {
    const createdAt = new Date().toISOString();
    const messages = [
      new HumanMessage({
        content: humanMessage,
        additional_kwargs: { created_at: createdAt, id: uuidv4() },
      }),
      new AIMessage({
        content: iaMessage,
        additional_kwargs: { created_at: createdAt, id: uuidv4(), ...additionalData },
      }),
    ];

    await super.addMessages(messages);
  }
}
