import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import { ConversationMessageType } from '@common/enums';

export class ResponseConversationMessageDto {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  messages: Message[];

  constructor(id: string, messages: Message[]) {
    this.id = id;
    this.messages = messages;
  }
}

export class Message {
  @IsEnum(ConversationMessageType)
  @IsNotEmpty()
  type: ConversationMessageType;

  @IsString()
  @IsNotEmpty()
  text: string;

  @IsOptional()
  additional_kwargs?: Record<string, unknown> | string;
}
