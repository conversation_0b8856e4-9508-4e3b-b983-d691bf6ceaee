import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>al, IsString, IsUUI<PERSON> } from 'class-validator';
import { OmitType } from '@nestjs/mapped-types';
import { CreateConversationDto } from '@data-insights/application/dto/in/create-conversation.dto';
import { Message } from '@data-insights/application/dto/out/response-conversation-message.dto';

export class ResponseConversationDto extends OmitType(CreateConversationDto, [
  'agentName',
  'lang',
] as const) {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsUUID('4')
  @IsNotEmpty()
  agentId: string;

  @IsUUID('4')
  @IsNotEmpty()
  sessionId: string;

  @IsString()
  @IsOptional()
  subject?: string;

  @IsEnum(Message)
  @IsOptional()
  messages?: Message[];

  @IsDate()
  createdAt: Date;

  @IsDate()
  updatedAt: Date;
}
