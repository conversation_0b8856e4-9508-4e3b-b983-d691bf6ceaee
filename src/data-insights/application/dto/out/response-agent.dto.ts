import { IsEnum, <PERSON>NotEmpty, <PERSON>Optional, IsString, IsUUID } from 'class-validator';
import { MessageType, RecordStatus } from '@common/enums';

export class ResponseAgentDto {
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @IsString()
  @IsNotEmpty()
  role: string;

  @IsString()
  @IsNotEmpty()
  backstory: string;

  @IsString()
  @IsNotEmpty()
  llmModel: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  outputType: MessageType;

  @IsString()
  @IsNotEmpty()
  lang: string;

  @IsOptional()
  voice: null | string;

  @IsOptional()
  status?: RecordStatus;

  constructor(
    role: string,
    backstory: string,
    llm: string,
    outputType: MessageType,
    lang: string,
    voice: string,
    status: RecordStatus,
  ) {
    this.role = role;
    this.backstory = backstory;
    this.llmModel = llm;
    this.outputType = outputType;
    this.lang = lang;
    this.voice = voice;
    this.status = status;
  }
}
