import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { RecordStatus, VectorType } from '@common/enums';

export class KnowledgeVectorDto {
  @IsString()
  @IsOptional()
  readonly id?: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly originId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly businessUserId: string;

  @IsUUID('4')
  @IsOptional()
  readonly scopeId?: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly itemId: string;

  @IsUUID('4')
  @IsOptional()
  readonly relatedUserId?: string;

  @IsEnum(VectorType)
  @IsNotEmpty()
  readonly type: VectorType;

  @IsString()
  @IsOptional()
  readonly content?: string;

  @IsOptional()
  readonly vector: any;

  @IsBoolean()
  @IsNotEmpty()
  readonly isBackfill: boolean = false;

  @IsOptional()
  status?: RecordStatus;

  @IsDate()
  createdAt?: Date;

  @IsDate()
  updatedAt?: Date;
}
