import { IsNotEmpty, IsObject, IsString } from 'class-validator';

export class IndexDocumentDto {
  @IsString()
  @IsNotEmpty({ message: 'index is required' })
  index: string;

  @IsObject()
  @IsNotEmpty({ message: 'document is required' })
  document: Record<string, any>;

  constructor(collection: string, document: Record<string, any>) {
    this.index = collection;
    this.document = document;
  }
}
