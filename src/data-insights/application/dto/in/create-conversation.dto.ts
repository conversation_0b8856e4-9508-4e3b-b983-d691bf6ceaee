import { ConversationAgentName, ConversationType, LangType } from '@common/enums';
import { IsEnum, IsNotEmpty, IsUUID } from 'class-validator';

export class CreateConversationDto {
  @IsUUID('4')
  @IsNotEmpty({ message: 'originId is required' })
  originId: string;

  @IsEnum(ConversationAgentName, {
    message: 'agentN<PERSON> must be a valid ConversationAgentName',
  })
  @IsNotEmpty({ message: 'agentName is required' })
  agentName: ConversationAgentName;

  @IsEnum(LangType, { message: 'lang must be a valid LangType' })
  @IsNotEmpty({ message: 'lang is required' })
  lang: LangType;

  @IsUUID('4')
  @IsNotEmpty({ message: 'businessUserId is required' })
  businessUserId: string;

  @IsEnum(ConversationType, { message: 'type must be a valid ConversationType' })
  @IsNotEmpty({ message: 'type is required' })
  type: ConversationType;
}
