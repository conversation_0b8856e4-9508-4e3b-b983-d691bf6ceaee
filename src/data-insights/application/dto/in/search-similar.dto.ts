import { IsNotEmptyObject } from '@common/validator/custom-validators';
import {
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
  ArrayMinSize,
  Validate,
} from 'class-validator';

export class SearchSimilarDto {
  @IsString()
  @IsNotEmpty({ message: 'index is required' })
  index: string;

  @IsNumber()
  @IsNotEmpty({ message: 'size is required' })
  size: number;

  @IsString()
  @IsNotEmpty({ message: 'like is required' })
  like: string;

  @IsArray()
  @ArrayMinSize(1, { message: 'fields is required' })
  @IsString({ each: true })
  fields: string[];

  @IsObject()
  @Validate(IsNotEmptyObject)
  filters: Record<string, any>;

  constructor(
    index: string,
    size: number,
    like: string,
    fields: string[],
    filters: Record<string, any>,
  ) {
    this.index = index;
    this.size = size;
    this.like = like;
    this.fields = fields;
    this.filters = filters;
  }
}
