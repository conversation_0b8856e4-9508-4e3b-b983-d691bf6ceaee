import { IsBoolean, <PERSON><PERSON>otE<PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsString, IsUUID } from 'class-validator';

export class ChatDto {
  @IsUUID('4')
  @IsNotEmpty({ message: 'conversationId is required' })
  conversationId: string;

  @IsString()
  @IsNotEmpty({ message: 'input is required' })
  input: string;

  @IsBoolean()
  @IsOptional()
  isGlobal?: boolean;

  constructor(conversationId: string, input: string, isGlobal?: boolean) {
    this.conversationId = conversationId;
    this.input = input;
    this.isGlobal = isGlobal;
  }
}
