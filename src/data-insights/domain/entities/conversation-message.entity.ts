import { ConversationMessageType } from '@common/enums';
import { IsEnum, IsNotEmpty, IsOptional, IsUUID } from 'class-validator';

export class ConversationMessageEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  readonly messages: Message[];
}

class Message {
  @IsEnum(ConversationMessageType)
  @IsNotEmpty()
  readonly type: ConversationMessageType;

  @IsOptional()
  readonly additional_kwargs?: Record<string, unknown> | string;

  @IsNotEmpty()
  readonly text: string;
}
