import { RecordStatus } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class ConversationEntity {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly sessionId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly originId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly agentId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly businessUserId: string;

  @IsString()
  subject?: string;

  @IsString()
  @IsNotEmpty()
  readonly type: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status?: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    sessionId: string,
    originId: string,
    agentId: string,
    businessUserId: string,
    subject: string,
    type: string,
    status?: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.sessionId = sessionId;
    this.originId = originId;
    this.agentId = agentId;
    this.businessUserId = businessUserId;
    this.subject = subject;
    this.type = type;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
