import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { RecordStatus, VectorType } from '@common/enums';

export class KnowledgeVectorEntity {
  @IsString()
  @IsNotEmpty()
  readonly id: string;

  /**
   * Id referring to the origin of the item (example: triagem_id)
   */
  @IsUUID('4')
  @IsNotEmpty()
  readonly originId: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly businessUserId: string;

  /**
   * Id referring to the scope of the vectorized item (example: workspace_id)
   */
  @IsUUID('4')
  @IsOptional()
  readonly scopeId?: string;

  /**
   * Id referring to the individual item saved in the vector (example: attempt_id)
   */
  @IsUUID('4')
  @IsNotEmpty()
  readonly itemId: string;

  /**
   * Id referring to the user that the vector is related to (example: user_id)
   */
  @IsUUID('4')
  @IsOptional()
  readonly relatedUserId?: string;

  @IsEnum(VectorType)
  @IsNotEmpty()
  readonly type: VectorType;

  @IsString()
  @IsNotEmpty()
  readonly embeddingModel: string;

  @IsString()
  @IsNotEmpty()
  readonly content: string;

  readonly vector?: any;

  @IsBoolean()
  @IsNotEmpty()
  readonly isBackfill: boolean = false;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status?: RecordStatus = RecordStatus.ACTIVE;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;
}
