import { MessageType, RecordStatus } from '@common/enums';
import { IsUUID, IsNotEmpty, IsString, IsEnum, IsDate, IsOptional } from 'class-validator';

export class AgentEntity {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  role: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsNotEmpty()
  backstory: string;

  @IsString()
  @IsNotEmpty()
  llmModel: string;

  @IsNotEmpty()
  @IsEnum(MessageType)
  outputType: MessageType;

  @IsString()
  @IsNotEmpty()
  lang: string;

  voice: null | string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    role: string,
    backstory: string,
    llm: string,
    outputType: MessageType,
    lang: string,
    voice: string,
    status: RecordStatus,
    name?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.role = role;
    this.backstory = backstory;
    this.llmModel = llm;
    this.outputType = outputType;
    this.lang = lang;
    this.voice = voice;
    this.status = status;
    this.name = name;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
