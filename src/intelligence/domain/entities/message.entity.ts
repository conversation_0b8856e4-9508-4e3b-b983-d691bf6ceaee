import { MessageType, RoleType } from '@common/enums';
import { IsUUID, IsNotEmpty, IsString, IsDate, IsEnum } from 'class-validator';

export class MessageHistoryEntity {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsNotEmpty()
  role: RoleType;

  @IsString()
  @IsNotEmpty()
  messageText: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  messageType: MessageType;

  @IsString()
  @IsNotEmpty()
  lang: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: string;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: string;

  @IsString()
  readonly fileUrl?: string;

  constructor(
    id: string,
    role: RoleType,
    messageText: string,
    messageType: MessageType,
    lang: string,
    fileUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.role = role;
    this.messageText = messageText;
    this.messageType = messageType;
    this.lang = lang;
    this.fileUrl = fileUrl;
    this.createdAt = createdAt ? createdAt.toString() : new Date().toString();
    this.updatedAt = updatedAt ? createdAt.toString() : new Date().toString();
  }
}
