import { RecordStatus } from '@common/enums';
import { IsUUID, IsNotEmpty, IsString, IsDate, IsEnum, IsOptional } from 'class-validator';

export class TaskEntity {
  @IsUUID('4')
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  agent: string;

  @IsString()
  @IsNotEmpty()
  responseTemplate: string;

  @IsString()
  managerAgentId: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    name: string,
    description: string,
    agent: string,
    responseTemplate: string,
    managerAgentId: string,
    status: RecordStatus,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.agent = agent;
    this.responseTemplate = responseTemplate;
    this.managerAgentId = managerAgentId;
    this.status = status;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
