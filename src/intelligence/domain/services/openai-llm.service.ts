import { Inject, Injectable } from '@nestjs/common';
import OpenA<PERSON>, { toFile } from 'openai';
import { MessageHistoryEntity } from '@intelligence/domain/entities/message.entity';
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { IllmService } from '@intelligence/domain/services/interfaces/IllmService';
import { ConversationHistoryAdapter } from '@intelligence/infrastructure/adapters/db/conversation-history.adapter';
import { AgentEntity } from '@intelligence/domain/entities/agent.entity';
import axios from 'axios';
import { ElevenLabsClient } from 'elevenlabs';
import { logger, SlackMessage } from '@edutalent/commons-sdk';
import { RoleType } from '@common/enums';
import { S3Service } from '@common/s3/s3.service';
import { CorrelationContextService } from '@common/services/correlation-context.service';

@Injectable()
export class OpenAILlmService implements IllmService {
  private readonly openai: OpenAI;
  private readonly elevenlabs: ElevenLabsClient;
  private readonly slackApiKey: string;

  constructor(
    @Inject('ConversationHistoryPort')
    private readonly conversationHistoryAdapter: ConversationHistoryAdapter,
    private readonly s3Service: S3Service,
    private readonly slackMessage: SlackMessage,
  ) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY.toString(),
    });

    this.elevenlabs = new ElevenLabsClient({
      apiKey: process.env.ELEVENLABS_API_KEY.toString(),
    });
    this.slackApiKey = process.env.SLACK_TOKEN;
  }

  async chatCompletion(
    agent: AgentEntity,
    taskDescription: string,
    conversationHistory: MessageHistoryEntity[],
  ): Promise<string> {
    logger.info(`Chat completion with agent ${agent.role}`);
    let messages: ChatCompletionMessageParam[] = [];

    if (conversationHistory.length === 0) {
      messages = [{ role: 'system', content: agent.backstory }];
    } else {
      messages = messages.concat(
        conversationHistory.map(message => ({
          role: this.convertRoleTypeToOpenAI(message.role),
          content: message.messageText,
        })),
      );
    }

    messages.push({ role: 'user', content: taskDescription });

    const stream = await this.openai.chat.completions.create(
      {
        model: agent.llmModel,
        messages,
        stream: true,
      },
      {
        maxRetries: 2,
      },
    );

    let llmResponseText = '';

    for await (const chunk of stream) {
      llmResponseText += chunk.choices[0]?.delta?.content || '';
    }

    return llmResponseText;
  }

  async convertAudioToText(audioFileUrl: string, lang: string): Promise<string> {
    logger.info(`Converting audio to text in ${lang} language`);
    const audioFileBuffer = await this.s3Service.getFileStreamFromPath(audioFileUrl);

    if (!audioFileBuffer) {
      const traceId = CorrelationContextService.getTraceId();
      logger.info(
        `Could not convert audio to text in ${lang} because audio file buffer is missing. File path: ${audioFileUrl}`,
        { traceId },
      );

      // Check if traceId is a portfolioItemId (UUID format) for better context
      const isPortfolioItemId =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(traceId);

      const messageSlack = `
        :alert: Transcedence! Está acontecendo alguma coisa, fica de olho! :alert:
        ${isPortfolioItemId ? `PortfolioItemId: ${traceId}` : `TraceId: ${traceId}`}
        Could not convert audio to text in ${lang} because audio file buffer is missing. File path: ${audioFileUrl}`;

      try {
        await this.slackMessage.sendMessage(this.slackApiKey, 'C06B0KX9M3R', messageSlack);
      } catch (error) {
        logger.error(`Error sending slack message. Error: ${JSON.stringify(error)}`, { traceId });
      }

      return;
    }

    const file = await toFile(audioFileBuffer, 'File.ogg');

    try {
      const transcription = await this.openai.audio.transcriptions.create(
        {
          file,
          model: 'whisper-1',
          prompt: `Transcribe the following audio in ${lang} language.`,
        },
        {
          maxRetries: 2,
        },
      );

      return transcription.text;
    } catch (error) {
      logger.error(`Error transcribing audio: ${JSON.stringify(error)}`, error);
      throw new Error('Error transcribing audio');
    }
  }

  async convertTextToAudio(text: string, voice: string): Promise<Buffer> {
    logger.info(`Converting text to audio with voice ${voice}`);
    const audioStream = await this.elevenlabs.generate(
      {
        voice,
        model_id: 'eleven_multilingual_v2',
        text,
      },
      {
        maxRetries: 2,
      },
    );

    const chunks: Buffer[] = [];
    for await (const chunk of audioStream) {
      chunks.push(chunk);
    }

    const content = Buffer.concat(chunks);
    return content;
  }

  private async downloadAudioFile(url: string): Promise<Buffer> {
    logger.info(`Downloading audio file from ${url}`);
    const response = await axios({
      url,
      method: 'GET',
      responseType: 'stream',
    });
    const chunks: Buffer[] = [];

    return new Promise((resolve, reject) => {
      response.data.on('data', (chunk: Buffer) => chunks.push(chunk));
      response.data.on('end', () => resolve(Buffer.concat(chunks)));
      response.data.on('error', reject);
    });
  }

  private convertRoleTypeToOpenAI(role: RoleType): RoleTypeGpt {
    switch (role) {
      case RoleType.SYSTEM:
        return RoleTypeGpt.SYSTEM;
      case RoleType.ASSISTANT:
        return RoleTypeGpt.ASSISTANT;
      case RoleType.USER:
        return RoleTypeGpt.USER;
      default:
        return RoleTypeGpt.USER;
    }
  }
}

enum RoleTypeGpt {
  SYSTEM = 'system',
  ASSISTANT = 'assistant',
  USER = 'user',
}
