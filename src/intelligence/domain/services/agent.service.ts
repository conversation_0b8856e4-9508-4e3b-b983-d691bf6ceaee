import { Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { validateKeys } from '@common/utils/validate-key-value';

@Injectable()
export class AgentService {
  compileBackstory(backStory: string, lang: string, params: Record<string, any>): string {
    logger.info(`Compiling back story with language ${lang}`);
    validateKeys(backStory, params);
    for (const [key, value] of Object.entries(params)) {
      const valueString = JSON.stringify(value);
      backStory = backStory.replace(`{{${key}}}`, valueString);
    }

    return (backStory += `\nThe output message must be in ${lang} language.`);
  }
}
