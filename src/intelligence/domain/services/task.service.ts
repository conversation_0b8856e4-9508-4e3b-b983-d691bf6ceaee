import { Injectable } from '@nestjs/common';
import { logger } from '@edutalent/commons-sdk';
import { validateKeys } from '@common/utils/validate-key-value';

@Injectable()
export class TaskService {
  compileTaskDescription(taskDescription: string, params: Record<string, any>): string {
    logger.info(`Compiling task description...`);
    validateKeys(taskDescription, params);

    for (const [key, value] of Object.entries(params)) {
      let valueString = value;
      if (typeof value === 'object') {
        valueString = JSON.stringify(value);
      }
      taskDescription = taskDescription.replace(`{{${key}}}`, valueString);
    }

    return taskDescription + '\n';
  }
}
