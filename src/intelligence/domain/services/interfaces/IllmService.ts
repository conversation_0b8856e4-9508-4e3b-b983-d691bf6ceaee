import { MessageHistoryEntity } from '@intelligence/domain/entities/message.entity';
import { AgentEntity } from '@intelligence/domain/entities/agent.entity';

export interface IllmService {
  chatCompletion(
    agent: AgentEntity,
    taskDescription: string,
    conversationHistory: MessageHistoryEntity[],
  ): Promise<string>;
  convertAudioToText(audioFileUrl: string, voice: string): Promise<string>;
  convertTextToAudio(text: string, lang: string): Promise<Buffer>;
}
