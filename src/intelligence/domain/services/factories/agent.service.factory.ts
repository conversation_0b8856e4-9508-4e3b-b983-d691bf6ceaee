import { IllmService } from '@intelligence/domain/services/interfaces/IllmService';
import { OpenAILlmService } from '@intelligence/domain/services/openai-llm.service';
import { Injectable } from '@nestjs/common';

@Injectable()
export class llmServiceFactory {
  constructor(private readonly openAILlmService: OpenAILlmService) {}

  create(serviceType: string): IllmService {
    if (serviceType.includes('gpt-')) {
      return this.openAILlmService;
    }
    throw new Error('Invalid service type');
  }
}
