import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TaskAdapter } from '@intelligence/infrastructure/adapters/db/task.adapter';
import { AgentUseCase } from '@intelligence/application/use-cases/agent.use-case';
import { TaskUseCase } from '@intelligence/application/use-cases/task.use-case';
import { AgentAdapter } from '@intelligence/infrastructure/adapters/db/agent.adapter';
import { TaskController } from '@intelligence/application/controllers/task.controller';
import { TaskService } from '@intelligence/domain/services/task.service';
import { ConversationHistoryAdapter } from '@intelligence/infrastructure/adapters/db/conversation-history.adapter';
import { llmServiceFactory } from '@intelligence/domain/services/factories/agent.service.factory';
import { OpenAILlmService } from '@intelligence/domain/services/openai-llm.service';
import { AgentController } from '@intelligence/application/controllers/agent.controller';
import { AgentService } from '@intelligence/domain/services/agent.service';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { S3Service } from '@common/s3/s3.service';
import { SlackMessage } from '@edutalent/commons-sdk';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 100000,
    maxRedirects: 10,
  }),
});

@Module({
  imports: [httpModule],
  providers: [
    { provide: 'TaskPort', useClass: TaskAdapter },
    { provide: 'AgentPort', useClass: AgentAdapter },
    {
      provide: 'ConversationHistoryPort',
      useClass: ConversationHistoryAdapter,
    },
    TaskUseCase,
    TaskService,
    AgentUseCase,
    AgentService,
    llmServiceFactory,
    OpenAILlmService,
    DynamoService,
    S3Service,
    SlackMessage,
  ],
  controllers: [TaskController, AgentController],
})
export class IntelligenceModule {}
