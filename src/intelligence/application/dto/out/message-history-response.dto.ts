import { MessageType, RoleType } from '@common/enums';
import { IsDate, IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class MessageHistoryResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsString()
  @IsNotEmpty()
  readonly role: RoleType;

  @IsString()
  @IsNotEmpty()
  readonly messageText: string;

  @IsEnum(MessageType)
  @IsNotEmpty()
  readonly messageType: MessageType;

  @IsString()
  @IsNotEmpty()
  readonly lang: string;

  @IsString()
  readonly fileUrl?: string;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt?: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt?: Date;

  constructor(
    id: string,
    role: RoleType,
    messageText: string,
    messageType: MessageType,
    lang: string,
    fileUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.role = role;
    this.messageText = messageText;
    this.messageType = messageType;
    this.lang = lang;
    this.fileUrl = fileUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
