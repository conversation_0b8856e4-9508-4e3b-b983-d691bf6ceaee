import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class ExecuteTaskSimplifiedRequestDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly taskId: string;

  @IsString()
  @IsOptional()
  readonly thread: string | null;

  @IsNotEmpty()
  readonly params: Record<string, any>;

  constructor(taskId: string, thread: string, params: Record<string, any>) {
    this.taskId = taskId;
    this.thread = thread;
    this.params = params;
  }
}
