import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { MessageType, RoleType } from '@common/enums';

export class SendDirectMessageDto {
  @IsString()
  @IsNotEmpty({ message: 'message is required' })
  readonly message: string;

  @IsString()
  @IsNotEmpty({ message: 'message type is required' })
  readonly messageType: MessageType;

  @IsString()
  @IsNotEmpty({ message: 'role type is required' })
  readonly roleType: RoleType;

  @IsOptional()
  @IsString()
  readonly fileUrl?: string;

  constructor(message: string, messageType: MessageType, roleType: RoleType, fileUrl?: string) {
    this.message = message;
    this.messageType = messageType;
    this.roleType = roleType;
    this.fileUrl = fileUrl;
  }
}
