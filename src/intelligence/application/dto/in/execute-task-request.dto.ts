import { MessageType } from '@common/enums';
import { IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class ExecuteTaskRequestDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly taskId: string;

  @IsOptional()
  @IsString()
  readonly thread: string | null;

  @IsNotEmpty()
  readonly params: Record<string, any>;

  @IsString()
  @IsOptional()
  readonly fileUrl: string | null;

  @IsString()
  @IsNotEmpty()
  readonly lang: string;

  @IsNotEmpty()
  readonly inputType: MessageType;

  constructor(
    taskId: string,
    thread: string | null,
    params: Record<string, any>,
    fileUrl: string | null,
    lang: string,
    inputType: MessageType,
  ) {
    this.taskId = taskId;
    this.thread = thread;
    this.params = params;
    this.fileUrl = fileUrl;
    this.lang = lang;
    this.inputType = inputType;
  }
}
