import { Body, Controller, Delete, Get, Param, Post, Put, Version } from '@nestjs/common';
import { TaskUseCase } from '@intelligence/application/use-cases/task.use-case';
import { ExecuteTaskRequestDto } from '@intelligence/application/dto/in/execute-task-request.dto';
import { ExecuteAgentResponseDto } from '@intelligence/application/dto/out/execute-task-response.dto';
import { TaskDto } from '@intelligence/application/dto/task.dto';
import { ExecuteTaskSimplifiedRequestDto } from '@intelligence/application/dto/in/execute-task-simplified-request.dto';
import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { ApiOperation, ApiParam, ApiResponse } from '@nestjs/swagger';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('intelligence/tasks')
export class TaskController {
  constructor(private readonly taskUseCase: TaskUseCase) {}

  @Post()
  @Version('1')
  async create(@Body() createTaskDto: TaskDto): Promise<any> {
    const task = await this.taskUseCase.create(createTaskDto);

    return {
      statusCode: 201,
      data: task,
    };
  }

  @Get('/:taskId')
  @Version('1')
  async FindById(@Param('taskId') taskId: string): Promise<any> {
    const task = await this.taskUseCase.findById(taskId);

    return {
      statusCode: 200,
      data: task,
    };
  }

  @ApiOperation({ summary: 'Get variables for tasks and agents' })
  @ApiResponse({
    status: 200,
    description: 'Variables for tasks and agents',
    schema: {
      type: 'object',
      properties: {
        statusCode: {
          type: 'number',
          example: 200,
        },
        data: {
          type: 'array',
          items: {
            type: 'string',
          },
          example: [
            'NOME_DO_CLIENTE',
            'CPF_DO_CLIENTE',
            'CPF_DO_CLIENTE_DIGITOS',
            'DATA_DE_NASCIMENTO_DO_CLIENTE',
            'VALOR_DIVIDA_ORIGINAL',
            'VALOR_DIVIDA_CORRIGIDO',
            'DIAS_ATRASO',
            'NOME_LOJA',
            'VENC_PRIM_PARCELA',
            'NOME_DO_CARTAO',
            'ID_DO_CONTRATO',
            'TITULOS',
          ],
        },
      },
    },
  })
  @ApiParam({
    name: 'taskId',
    description: 'Task ID',
    type: String,
    required: true,
    example: 'f3758997-dd53-4629-aa22-1b7ebf124150',
  })
  @Get('/:taskId/variables')
  @Version('1')
  async getTasksAndAgentsBackstoryVariables(@Param('taskId') taskId: string): Promise<any> {
    const variables = await this.taskUseCase.getTasksAndAgentsBackstoryVariables(taskId);
    return {
      statusCode: 200,
      data: variables,
    };
  }

  @Post('execute')
  @Version('1')
  async execute(
    @Body() executeAgentRequest: ExecuteTaskRequestDto,
  ): Promise<ExecuteAgentResponseDto> {
    return await this.taskUseCase.execute(executeAgentRequest);
  }

  @Post('execute-simplified')
  @Version('1')
  async executeSimplified(
    @Body() executeAgentRequest: ExecuteTaskSimplifiedRequestDto,
  ): Promise<any> {
    return await this.taskUseCase.executeSimplified(executeAgentRequest);
  }

  @Put('/:taskId')
  @Version('1')
  async update(@Param('taskId') taskId: string, @Body() updateTaskDto: TaskDto): Promise<any> {
    const updatedTask = await this.taskUseCase.update(taskId, updateTaskDto);

    return {
      statusCode: 200,
      data: updatedTask,
    };
  }

  @Delete('/:taskId')
  @Version('1')
  async delete(@Param('taskId') taskId: string): Promise<any> {
    await this.taskUseCase.delete(taskId);
    return {
      statusCode: 200,
    };
  }

  @Get()
  @Version('1')
  async findByAll(): Promise<any> {
    const taskListDtoReturn = await this.taskUseCase.findAll();
    return {
      statusCode: 200,
      data: taskListDtoReturn,
    };
  }
}
