import { Inject, Injectable } from '@nestjs/common';
import { randomUUID as uuidv4 } from 'crypto';
import { ExecuteTaskRequestDto } from '@intelligence/application/dto/in/execute-task-request.dto';
import { ExecuteAgentResponseDto } from '@intelligence/application/dto/out/execute-task-response.dto';
import { MessageType, RecordStatus, RoleType } from '@common/enums';
import { TaskEntity } from '@intelligence/domain/entities/task.entity';
import { TaskService } from '@intelligence/domain/services/task.service';
import { ConversationHistoryAdapter } from '@intelligence/infrastructure/adapters/db/conversation-history.adapter';
import { MessageHistoryEntity } from '@intelligence/domain/entities/message.entity';
import { llmServiceFactory } from '@intelligence/domain/services/factories/agent.service.factory';
import { AgentEntity } from '@intelligence/domain/entities/agent.entity';
import { AgentDto } from '@intelligence/application/dto/agent.dto';
import { UpdateAgentDto } from '@intelligence/application/dto/in/update-agent.dto';
import { AgentService } from '@intelligence/domain/services/agent.service';
import { logger } from '@edutalent/commons-sdk';
import { ExecuteTaskSimplifiedRequestDto } from '@intelligence/application/dto/in/execute-task-simplified-request.dto';
import { AgentPort } from '@intelligence/infrastructure/ports/db/agent.port';
import { MessageHistoryResponseDto } from '@intelligence/application/dto/out/message-history-response.dto';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';
import { SendDirectMessageDto } from '@intelligence/application/dto/in/send-direct-message.dto';

@Injectable()
export class AgentUseCase {
  constructor(
    @Inject('AgentPort')
    private readonly agentAdapter: AgentPort,
    @Inject('ConversationHistoryPort')
    private readonly conversationHistoryAdapter: ConversationHistoryAdapter,
    private readonly taskService: TaskService,
    private readonly agentService: AgentService,
    private readonly llmServiceFactory: llmServiceFactory,
  ) {}

  async create(createAgentDto: AgentDto): Promise<AgentDto> {
    logger.info(`Creating agent with role ${createAgentDto.role}`);
    createAgentDto.id = uuidv4();
    createAgentDto.status = RecordStatus.ACTIVE;

    await this.agentAdapter.create(
      new AgentEntity(
        createAgentDto.id,
        createAgentDto.role,
        createAgentDto.backstory,
        createAgentDto.llmModel,
        createAgentDto.outputType,
        createAgentDto.lang,
        createAgentDto.voice,
        createAgentDto.status,
        createAgentDto.name,
      ),
    );

    return createAgentDto;
  }

  async findById(agentId: string): Promise<AgentDto> {
    logger.info(`Fetching agent with id ${agentId}`);
    const agentEntity = await this.agentAdapter.get(agentId);

    if (!agentEntity) {
      throw new BusinessException(
        'Portfolio-use-case',
        `Agent with id ${agentId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const agentReturn = new AgentDto(
      agentEntity.role,
      agentEntity.backstory,
      agentEntity.llmModel,
      agentEntity.outputType,
      agentEntity.lang,
      agentEntity.voice,
      agentEntity.status,
      agentEntity.name,
    );
    agentReturn.id = agentEntity.id;

    return agentReturn;
  }

  async findAll(): Promise<AgentDto[]> {
    logger.info(`Fetching all agents`);
    const agentList = await this.agentAdapter.getAll();
    const agentListDtoReturn = agentList.map(agent => {
      const agentReturn = new AgentDto(
        agent.role,
        agent.backstory,
        agent.llmModel,
        agent.outputType,
        agent.lang,
        agent.voice,
        agent.status,
        agent.name,
      );
      agentReturn.id = agent.id;
      return agentReturn;
    });

    return agentListDtoReturn;
  }

  async update(agentId: string, updateAgentDto: UpdateAgentDto): Promise<AgentDto> {
    logger.info(`Updating agent with id ${agentId}`);
    const agentEntity = await this.agentAdapter.get(agentId);

    if (!agentEntity) {
      throw new Error(`Agent with id ${agentId} not found`);
    }

    const updatedAgentEntity = await this.agentAdapter.update(
      new AgentEntity(
        agentId,
        updateAgentDto.role,
        updateAgentDto.backstory,
        updateAgentDto.llmModel,
        updateAgentDto.outputType,
        updateAgentDto.lang,
        updateAgentDto.voice,
        agentEntity.status,
        updateAgentDto.name,
      ),
    );

    const updatedAgentDto = new AgentDto(
      updatedAgentEntity.role,
      updatedAgentEntity.backstory,
      updatedAgentEntity.llmModel,
      updatedAgentEntity.outputType,
      updatedAgentEntity.lang,
      updatedAgentEntity.voice,
      updatedAgentEntity.status,
      updatedAgentEntity.name,
    );
    updatedAgentDto.id = updatedAgentEntity.id;

    return updatedAgentDto;
  }

  async delete(agentId: string): Promise<void> {
    logger.info(`Updating agent with id ${agentId}`);
    const agentEntity = await this.agentAdapter.get(agentId);

    if (!agentEntity) {
      throw new Error(`Agent with id ${agentId} not found`);
    }

    await this.agentAdapter.delete(agentId);
  }

  async execute(
    executeTaskRequest: ExecuteTaskRequestDto,
    task: TaskEntity,
  ): Promise<ExecuteAgentResponseDto> {
    logger.info(`Executing task with thread ${executeTaskRequest.thread}`);

    const agent: AgentEntity = await this.agentAdapter.get(task.agent);

    const llmService = this.llmServiceFactory.create(agent.llmModel);

    let taskDescription = executeTaskRequest.params['prompt'] ?? '';

    let conversationHistory: MessageHistoryEntity[] = [];
    conversationHistory = await this.conversationHistoryAdapter.get(executeTaskRequest.thread);

    const isFirstIteration = this.isFirstIteration(conversationHistory);

    if (isFirstIteration) {
      logger.info(`First iteration for thread ${executeTaskRequest.thread}`);
      taskDescription = this.taskService.compileTaskDescription(
        task.description,
        executeTaskRequest.params,
      );

      agent.backstory = this.agentService.compileBackstory(
        agent.backstory,
        agent.lang,
        executeTaskRequest.params,
      );

      await this.conversationHistoryAdapter.save(
        new MessageHistoryEntity(
          executeTaskRequest.thread,
          RoleType.SYSTEM,
          agent.backstory,
          MessageType.TEXT,
          executeTaskRequest.lang,
        ),
      );
    }

    if (
      !isFirstIteration &&
      executeTaskRequest.inputType.toUpperCase() === MessageType.AUDIO &&
      executeTaskRequest.fileUrl
    ) {
      logger.info(`Converting audio to text for thread ${executeTaskRequest.thread}`);

      taskDescription +=
        '\n' +
        (await llmService.convertAudioToText(executeTaskRequest.fileUrl, executeTaskRequest.lang));
    }

    await this.conversationHistoryAdapter.save(
      new MessageHistoryEntity(
        executeTaskRequest.thread,
        RoleType.USER,
        taskDescription,
        executeTaskRequest.inputType,
        executeTaskRequest.lang,
      ),
    );

    const llmResponseText = await llmService.chatCompletion(
      agent,
      taskDescription,
      conversationHistory,
    );

    await this.conversationHistoryAdapter.save(
      new MessageHistoryEntity(
        executeTaskRequest.thread,
        RoleType.ASSISTANT,
        llmResponseText,
        agent.outputType,
        agent.lang,
      ),
    );

    // Check if response contains links or sequential numbers
    const llmResponseTextConstains = this.constainsEspecialCases(llmResponseText);

    let outputType = MessageType.TEXT;

    let outputBuffer: Buffer;

    if (
      executeTaskRequest.inputType.toUpperCase() === MessageType.AUDIO &&
      agent.outputType === MessageType.AUDIO &&
      !llmResponseTextConstains &&
      !isFirstIteration
    ) {
      logger.info(`Converting text to audio for thread ${executeTaskRequest.thread}`);

      const audioBuffer = await llmService.convertTextToAudio(llmResponseText, agent.voice);
      outputBuffer = Buffer.from(audioBuffer);
      outputType = MessageType.AUDIO;
    } else {
      outputBuffer = Buffer.from(llmResponseText);
      logger.info(`Returning text response for thread ${executeTaskRequest.thread}`);
    }

    const response = new ExecuteAgentResponseDto();
    response.thread = executeTaskRequest.thread;
    response.output = outputBuffer;
    response.outputType = outputType;

    return response;
  }

  async executeSimplified(
    executeTaskRequest: ExecuteTaskSimplifiedRequestDto,
    task: TaskEntity,
  ): Promise<any> {
    logger.info(`Executing task simplified with thread ${executeTaskRequest.thread}`);

    if (executeTaskRequest.thread && task.description.includes('{{conversationHistory}}')) {
      await this.loadConversationHistoryInParams(executeTaskRequest);
    }

    let taskDescription = this.taskService.compileTaskDescription(
      task.description,
      executeTaskRequest.params,
    );

    if (task.responseTemplate) {
      logger.info(
        `Adding response template to task description for thread: ${executeTaskRequest.thread}`,
      );
      taskDescription += `The output **must** a JSON with the following structure **without** any other addicional text or comment: ${task.responseTemplate}`;
    }

    const agent: AgentEntity = await this.agentAdapter.get(task.agent);

    agent.backstory = this.agentService.compileBackstory(
      agent.backstory,
      agent.lang,
      executeTaskRequest.params,
    );

    const llmService = this.llmServiceFactory.create(agent.llmModel);

    const llmResponseText = await llmService.chatCompletion(agent, taskDescription, []);
    logger.info(`LLM response for thread ${executeTaskRequest.thread}: ${llmResponseText}`);
    // const response = JSON.parse(llmResponseText);

    return llmResponseText;
  }

  async getConversationHistory(workflowExecutionId: string): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Fetching conversation history for thread ${workflowExecutionId}`);

    let conversationHistory = await this.conversationHistoryAdapter.get(workflowExecutionId);

    //Removing system messages
    conversationHistory = conversationHistory.filter(m => m.role !== RoleType.SYSTEM);

    if (conversationHistory.length > 0 && conversationHistory[0].role === RoleType.USER) {
      conversationHistory.shift();
    }

    const response = conversationHistory.map(entity =>
      this.createResponseConversationHistoryDto(entity),
    );

    // Sort by createdAt in ascending order (newest last)
    response.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    return response;
  }

  async createConversationHistory(
    workflowExecutionId: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistoryResponseDto[]> {
    logger.info(`Creating conversation history for thread ${workflowExecutionId}`);

    await this.conversationHistoryAdapter.save(
      new MessageHistoryEntity(
        workflowExecutionId,
        sendDirectMessageDto.roleType,
        sendDirectMessageDto.message,
        sendDirectMessageDto.messageType,
        'pt-BR',
        sendDirectMessageDto.fileUrl,
      ),
    );

    return await this.getConversationHistory(workflowExecutionId);
  }

  async getByNameAndLang(name: string, lang: string): Promise<AgentDto> {
    logger.info(`Fetching agent with name ${name} and lang ${lang}`);
    const agentEntity = (await this.agentAdapter.getAll({ name, lang })).at(0);

    if (!agentEntity) {
      throw new BusinessException(
        'Agent-use-case',
        `Agent with name ${name} and lang ${lang} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const agentReturn = new AgentDto(
      agentEntity.role,
      agentEntity.backstory,
      agentEntity.llmModel,
      agentEntity.outputType,
      agentEntity.lang,
      agentEntity.voice,
      agentEntity.status,
      agentEntity.name,
    );
    agentReturn.id = agentEntity.id;

    return agentReturn;
  }

  private isFirstIteration = (conversationHistory: MessageHistoryEntity[]): boolean =>
    conversationHistory.length === 0;

  private constainsEspecialCases(text: string): boolean {
    // Check for URLs (http, https, www)
    const urlPattern = /(https?:\/\/[^\s]+)|(www\.[^\s]+)/g;
    if (urlPattern.test(text)) {
      return true;
    }

    // Check for email addresses
    const emailPattern = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    if (emailPattern.test(text)) {
      return true;
    }

    // Check for sequential numbers (7 or more consecutive digits)
    const sequentialNumberPattern = /\d{7,}/g;
    if (sequentialNumberPattern.test(text)) {
      return true;
    }

    return false;
  }

  private async loadConversationHistoryInParams(
    executeTaskRequest: ExecuteTaskSimplifiedRequestDto,
  ) {
    let conversationHistory: MessageHistoryEntity[] = [];
    conversationHistory = await this.conversationHistoryAdapter.get(executeTaskRequest.thread);

    const conversationHistoryInText = JSON.stringify(conversationHistory);

    executeTaskRequest.params['conversationHistory'] = conversationHistoryInText;
  }

  private createResponseConversationHistoryDto(
    conversationHistoryEntity: MessageHistoryEntity,
  ): MessageHistoryResponseDto {
    return new MessageHistoryResponseDto(
      conversationHistoryEntity.id,
      conversationHistoryEntity.role,
      conversationHistoryEntity.messageText,
      conversationHistoryEntity.messageType,
      conversationHistoryEntity.lang,
      conversationHistoryEntity.fileUrl,
      new Date(conversationHistoryEntity.createdAt),
      new Date(conversationHistoryEntity.updatedAt),
    );
  }
}
