import { Inject, Injectable } from '@nestjs/common';
import { ExecuteTaskRequestDto } from '@intelligence/application/dto/in/execute-task-request.dto';
import { ExecuteAgentResponseDto } from '@intelligence/application/dto/out/execute-task-response.dto';
import { AgentUseCase } from '@intelligence/application/use-cases/agent.use-case';
import { logger } from '@edutalent/commons-sdk';
import { TaskEntity } from '@intelligence/domain/entities/task.entity';
import { randomUUID as uuidv4 } from 'crypto';
import { TaskDto } from '@intelligence/application/dto/task.dto';
import { ExecuteTaskSimplifiedRequestDto } from '@intelligence/application/dto/in/execute-task-simplified-request.dto';
import { TaskPort } from '@intelligence/infrastructure/ports/db/task.port';
import { RecordStatus } from '@common/enums';
import {
  BusinessException,
  BusinessExceptionStatus,
} from '@common/exception/types/BusinessException';

@Injectable()
export class TaskUseCase {
  constructor(
    @Inject('TaskPort')
    private readonly taskAdapter: TaskPort,
    private readonly agentUseCase: AgentUseCase,
  ) {}

  async execute(executetaskRequest: ExecuteTaskRequestDto): Promise<ExecuteAgentResponseDto> {
    const task = await this.taskAdapter.get(executetaskRequest.taskId);

    const response = await this.agentUseCase.execute(executetaskRequest, task);

    return response;
  }

  async executeSimplified(executetaskRequest: ExecuteTaskSimplifiedRequestDto): Promise<any> {
    const task = await this.taskAdapter.get(executetaskRequest.taskId);

    const response = await this.agentUseCase.executeSimplified(executetaskRequest, task);

    return response;
  }

  async create(createTaskDto: TaskDto): Promise<TaskDto> {
    logger.info(`Creating task with description ${createTaskDto.description}`);
    createTaskDto.id = uuidv4();

    await this.taskAdapter.create(
      new TaskEntity(
        createTaskDto.id,
        createTaskDto.name,
        createTaskDto.description,
        createTaskDto.agent,
        createTaskDto.responseTemplate,
        createTaskDto.managerAgentId,
        RecordStatus.ACTIVE,
      ),
    );

    return createTaskDto;
  }

  async findById(taskId: string): Promise<TaskDto> {
    logger.info(`Fetching task with id ${taskId}`);
    const taskEntity = await this.taskAdapter.get(taskId);

    if (!taskEntity) {
      throw new BusinessException(
        'Task-use-case',
        `Task with id ${taskId} not found`,
        BusinessExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const taskReturn = new TaskDto(
      taskEntity.description,
      taskEntity.name,
      taskEntity.agent,
      taskEntity.responseTemplate,
      taskEntity.managerAgentId,
      taskEntity.status,
    );

    taskReturn.id = taskEntity.id;

    return taskReturn;
  }

  async findAll(): Promise<TaskDto[]> {
    logger.info(`Fetching all tasks`);
    const taskList = await this.taskAdapter.getAll();
    const tasktListDtoReturn = taskList.map(task => {
      const taskReturn = new TaskDto(
        task.description,
        task.name,
        task.agent,
        task.responseTemplate,
        task.managerAgentId,
        task.status,
      );
      taskReturn.id = task.id;
      return taskReturn;
    });

    return tasktListDtoReturn;
  }

  async update(taskId: string, updateTaskDto: TaskDto): Promise<TaskDto> {
    logger.info(`Updating task with id ${taskId}`);
    const taskEntity = await this.taskAdapter.get(taskId);
    if (!taskEntity) {
      throw new Error(`Task with id ${taskId} not found`);
    }

    const updatedTaskEntity = new TaskEntity(
      taskId,
      updateTaskDto.name,
      updateTaskDto.description,
      updateTaskDto.agent,
      updateTaskDto.responseTemplate,
      updateTaskDto.managerAgentId,
      taskEntity.status,
    );

    await this.taskAdapter.update(updatedTaskEntity);

    const updatedTaskDto = new TaskDto(
      updatedTaskEntity.description,
      updatedTaskEntity.name,
      updatedTaskEntity.agent,
      updatedTaskEntity.responseTemplate,
      updatedTaskEntity.managerAgentId,
      updatedTaskEntity.status,
    );
    updatedTaskDto.id = taskId;

    return updatedTaskDto;
  }

  async delete(taskId: string): Promise<void> {
    logger.info(`Updating task with id ${taskId}`);
    const taskEntity = await this.taskAdapter.get(taskId);

    if (!taskEntity) {
      throw new Error(`Task with id ${taskId} not found`);
    }

    await this.taskAdapter.delete(taskId);
  }

  async getTasksAndAgentsBackstoryVariables(taskId: string): Promise<string[]> {
    logger.info('Getting task variables for task: ', taskId);
    let variables = [];
    const keysToAppend = ['PHONE_NUMBER'];
    const keysToRemove = ['PROTOCOL_NUMBER', 'DATA_ATUAL'];

    const task = await this.taskAdapter.get(taskId);
    const taskCsvHeaders = this.validateKeys(task.description);
    const agent = await this.agentUseCase.findById(task.agent);
    const agentCsvHeaders = this.validateKeys(agent.backstory);
    variables.push(...taskCsvHeaders, ...agentCsvHeaders);

    //append keysToAppend to variables
    variables.push(...keysToAppend);

    //remove keysToRemove from variables
    variables = variables.filter(variable => !keysToRemove.includes(variable));
    return variables;
  }

  private validateKeys(text: string): string[] {
    const regex = /\{\{(\w+)\}\}/g;
    const templateCsvHeaders = [];
    let match: string[];

    while ((match = regex.exec(text)) !== null) {
      templateCsvHeaders.push(match[1]);
    }

    return templateCsvHeaders.map(header => header.toUpperCase());
  }
}
