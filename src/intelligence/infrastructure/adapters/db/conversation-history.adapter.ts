import { Injectable } from '@nestjs/common';
import { MessageHistoryEntity } from '@intelligence/domain/entities/message.entity';
import { ConversationHistoryPort } from '@intelligence/infrastructure/ports/db/conversation-history.port';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoException } from '@common/exception/types/DynamoException';

@Injectable()
export class ConversationHistoryAdapter implements ConversationHistoryPort {
  private dynamoClient: DynamoDBDocumentClient;
  DYNAMO_TABLE_NAME = 'transcendence_message_history';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async get(id: string): Promise<MessageHistoryEntity[]> {
    const params = {
      TableName: this.DYNAMO_TABLE_NAME,
      Key: {
        id: id,
      },
    };

    try {
      const { Item } = await this.dynamoClient.send(new GetCommand(params));
      if (Item === undefined) {
        return Promise.resolve([]);
      } else {
        return Promise.resolve(Item.messages as MessageHistoryEntity[]);
      }
    } catch (error) {
      const errorResponse = {
        message: `Error while fetching conversation history for id: ${id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }

  async save(conversationHistoryEntity: MessageHistoryEntity): Promise<MessageHistoryEntity> {
    const existingMessages = this.get(conversationHistoryEntity.id);

    try {
      if (existingMessages) {
        const messages = [...(await existingMessages), conversationHistoryEntity];
        const params = {
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: conversationHistoryEntity.id,
            messages: messages,
          },
        };
        await this.dynamoClient.send(new PutCommand(params));
      } else {
        const params = {
          TableName: this.DYNAMO_TABLE_NAME,
          Item: {
            id: conversationHistoryEntity.id,
            messages: [conversationHistoryEntity],
          },
        };

        await this.dynamoClient.send(new PutCommand(params));
      }

      return Promise.resolve(conversationHistoryEntity);
    } catch (error) {
      const errorResponse = {
        message: `Error while saving conversation history for id: ${
          conversationHistoryEntity.id
        }, messageType: ${conversationHistoryEntity.messageType}, role: ${
          conversationHistoryEntity.role
        }. Error: ${error.message || error}. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }
}
