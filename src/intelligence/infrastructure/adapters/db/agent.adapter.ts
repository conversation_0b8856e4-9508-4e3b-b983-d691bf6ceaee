import { Injectable } from '@nestjs/common';
import { AgentEntity } from '@intelligence/domain/entities/agent.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { AgentPort } from '@intelligence/infrastructure/ports/db/agent.port';

@Injectable()
export class AgentAdapter extends PrismaCommonAdapter<AgentEntity> implements AgentPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'agent');
  }
}
