import { Injectable } from '@nestjs/common';
import { TaskEntity } from '@intelligence/domain/entities/task.entity';
import { PrismaService } from '@common/prisma/prisma.service';
import { PrismaCommonAdapter } from '@common/db/adapters/prisma-common.adapter';
import { TaskPort } from '@intelligence/infrastructure/ports/db/task.port';

@Injectable()
export class TaskAdapter extends PrismaCommonAdapter<TaskEntity> implements TaskPort {
  constructor(prisma: PrismaService) {
    super(prisma, 'task');
  }
}
