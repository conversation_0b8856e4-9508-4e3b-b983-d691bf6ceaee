import { Injectable } from '@nestjs/common';
import { NectarPort } from '@orchestrator/tool/integrations/ports/soap/nectar.port';
import * as soap from 'soap';
import { logger } from '@edutalent/commons-sdk';
import { NectarAuthenticationResponseDto } from '@orchestrator/tool/integrations/dto/out/nectar-authentication-response.dto';
import { NectarGetDadosDividaResponse } from '@orchestrator/tool/integrations/dto/out/nectar-getdadosdivida-response.dto';
import {
  NectarGetOpcoesNegociacaoResponse,
  NectarOpcoesNegociacao,
} from '@orchestrator/tool/integrations/dto/out/nectar-getopcoesnegociacao-response.dto';
import {
  IntegrationException,
  IntegrationExceptionStatus,
} from '@common/exception/types/IntegrationException';

@Injectable()
export class NectarAdapter implements NectarPort {
  private nectarAuthentication: NectarAuthenticationResponseDto;

  constructor() {}

  async getDadosDivida(document: string): Promise<NectarGetDadosDividaResponse> {
    const token = await this.getToken();

    const client = await this.createSoapClient();
    client.on('request', xml => {
      logger.info('NectarAdapter - GetDadosDivida - SOAP Request Sent:', xml);
    });

    const requestData = {
      cnpjcpf: document,
      codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
      codigoToken: token,
    };

    return new Promise((resolve, reject) => {
      client.GetDadosDivida(requestData, (err, result) => {
        if (err || !result?.GetDadosDividaResult?.Contrato?.Contrato) {
          const responseError = { ...err, ...result };

          logger.error('NectarAdapter - GetDadosDivida Error in: ' + JSON.stringify(responseError));

          reject(
            new IntegrationException(
              `Nectar GetDadosDivida Error: ${JSON.stringify(
                result?.GetDadosDividaResult?.Resultado,
              )}`,
              IntegrationExceptionStatus.ITEM_NOT_FOUND,
            ),
          );
        } else {
          logger.info('NectarAdapter - GetDadosDivida Result: ' + JSON.stringify(result));

          const response = result as NectarGetDadosDividaResponse;

          if (!Array.isArray(response.GetDadosDividaResult.Contrato.Contrato)) {
            response.GetDadosDividaResult.Contrato.Contrato = [
              response.GetDadosDividaResult.Contrato.Contrato,
            ];
          }

          for (const contrato of response.GetDadosDividaResult.Contrato.Contrato) {
            if (!Array.isArray(contrato.Divida.Divida)) {
              contrato.Divida.Divida = [contrato.Divida.Divida];
            }

            const listDivida = contrato.Divida.Divida.filter(
              divida => divida.Descricao.toLowerCase() == 'carne',
            );

            if (listDivida.length > 0) {
              contrato.Divida.Divida = listDivida;
            }

            if (contrato.Acordo?.Acordo) {
              if (!Array.isArray(contrato.Acordo.Acordo)) {
                contrato.Acordo.Acordo = [contrato.Acordo.Acordo];
              }

              contrato.Acordo.Acordo.forEach(acordo => {
                if (!Array.isArray(acordo.AcordoParcela.AcordoParcela)) {
                  acordo.AcordoParcela.AcordoParcela = [acordo.AcordoParcela.AcordoParcela];
                }
              });
            }
          }

          resolve(response);
        }
      });
    });
  }

  async getOpcoesNegociacao(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    tpDesconto: string,
    percDescAplicNoPrincipal: string,
    percDescAplicNaCorrecao: string,
    vencPrimParcela: string,
  ): Promise<NectarGetOpcoesNegociacaoResponse> {
    const token = await this.getToken();

    const client = await this.createSoapClient();
    client.on('request', xml => {
      logger.info('NectarAdapter - GetOpcoesNegociacao - SOAP Request Sent:', xml);
    });

    const requestData = {
      idCon: idCon,
      idServ: 1,
      titulos: titulos,
      parcelasNum: parcelasNum,
      vencPrimParcela: vencPrimParcela,
      tiponegociacao: 3,
      tpDesconto: tpDesconto,
      percDescAplicNoPrincipal: percDescAplicNoPrincipal,
      percDescAplicNaCorrecao: percDescAplicNaCorrecao,
      codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
      codigoToken: token,
    };

    return new Promise((resolve, reject) => {
      client.GetOpcoesNegociacao(requestData, (err, result) => {
        if (err || !result?.GetOpcoesNegociacaoResult?.OpcoesNegociacao?.OpcoesNegociacao) {
          const responseError = { ...err, ...result };

          logger.error(
            'NectarAdapter - GetOpcoesNegociacao Error in: ' + JSON.stringify(responseError),
          );

          reject(
            new IntegrationException(
              `Nectar GetOpcoesNegociacao Error: ${JSON.stringify(
                result?.GetOpcoesNegociacaoResult?.Resultado,
              )}`,
              IntegrationExceptionStatus.ITEM_NOT_FOUND,
            ),
          );
        } else {
          logger.info('NectarAdapter - GetOpcoesNegociacao Result: ' + JSON.stringify(result));

          const response = result as NectarGetOpcoesNegociacaoResponse;

          if (
            !Array.isArray(response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao)
          ) {
            response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao = [
              response.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao,
            ];
          }

          for (const opcao of response.GetOpcoesNegociacaoResult.OpcoesNegociacao
            .OpcoesNegociacao) {
            if (!Array.isArray(opcao.Parcelas.Parcelas)) {
              opcao.Parcelas.Parcelas = [opcao.Parcelas.Parcelas];
            }
          }

          resolve(response);
        }
      });
    });
  }

  async getOpcoesNegociacaoWithMaxDiscountLessOne(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    vencPrimParcela: string,
  ): Promise<NectarOpcoesNegociacao[]> {
    logger.info('NectarAdapter - getOpcoesNegociacaoWithMaxDiscount');

    const getOpcoesNegociacaoResponse = await this.getOpcoesNegociacao(
      idCon,
      titulos,
      parcelasNum,
      '2',
      '',
      '',
      vencPrimParcela,
    );

    const listOpcoesNegociacaoUpdated: NectarOpcoesNegociacao[] = [];

    for (
      let i = 0;
      i <
      getOpcoesNegociacaoResponse.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao
        .length;
      i++
    ) {
      const opcaoNegociacao =
        getOpcoesNegociacaoResponse.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao[i];

      logger.info(
        `NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Applying Discount to CodigoFaixa: ${opcaoNegociacao.CodigoFaixa}`,
      );

      let percDescAplicNaCorrecao = opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao;

      if (Number(opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao) >= 1) {
        percDescAplicNaCorrecao = (
          Number(opcaoNegociacao.PercentualMaximoDeDescontoNaCorrecao) - 1
        ).toString();
      }

      const getOpcoesNegociacao = await this.getOpcoesNegociacao(
        idCon,
        titulos,
        parcelasNum,
        '2',
        opcaoNegociacao.PercentualMaximoDeDescontoNoPrincipal,
        percDescAplicNaCorrecao,
        vencPrimParcela,
      );

      const opcaoNegociacaoUpdated =
        getOpcoesNegociacao.GetOpcoesNegociacaoResult.OpcoesNegociacao.OpcoesNegociacao.filter(
          opcao => opcao.CodigoFaixa == opcaoNegociacao.CodigoFaixa,
        )[0];

      logger.info(
        `NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Discount Applied - CodigoFaixa: ${opcaoNegociacao.CodigoFaixa}`,
      );

      listOpcoesNegociacaoUpdated.push(opcaoNegociacaoUpdated);
    }

    logger.info(
      'NectarAdapter - getOpcoesNegociacaoWithMaxDiscount - Result: ' +
        JSON.stringify(listOpcoesNegociacaoUpdated),
    );

    return listOpcoesNegociacaoUpdated;
  }
  private async getToken(): Promise<string> {
    try {
      const client = await this.createSoapClient();
      client.on('request', xml => {
        logger.info('NectarAdapter - GetToken - SOAP Request Sent:', xml);
      });

      return new Promise((resolve, reject) => {
        if (this.nectarAuthentication && this.nectarAuthentication.ValidadeToken > new Date()) {
          resolve(this.nectarAuthentication.CodigoToken);
          return;
        }

        const nectarLoginInfo = {
          cnpj: process.env.NECTAR_LOGIN_CNPJ.toString(),
          codigoParceiro: process.env.NECTAR_LOGIN_CODIGO_PARCEIRO,
          usu: process.env.NECTAR_LOGIN_USU.toString(),
          pass: process.env.NECTAR_LOGIN_PASS.toString(),
        };

        client.GetToken(nectarLoginInfo, (err, result) => {
          if (err || !result.GetTokenResult) {
            logger.error('NectarAdapter - GetToken Error: ' + JSON.stringify(err));
            reject(err);
          } else {
            logger.info('NectarAdapter - GetToken Result:', result);
            this.nectarAuthentication = result.GetTokenResult;
            resolve(this.nectarAuthentication.CodigoToken);
          }
        });
      });
    } catch (error) {
      logger.error('NectarAdapter - Error creating SOAP client:', error);
      throw error;
    }
  }

  private async createSoapClient(): Promise<any> {
    const nectarEndpoint = process.env.NECTAR_WSDL_URL.toString();

    return new Promise((resolve, reject) => {
      soap.createClient(
        nectarEndpoint,
        {
          disableCache: true,
          forceSoap12Headers: false,
          // Setting the timeout here
          wsdl_options: {
            timeout: 60000, // 1 minute timeout
          },
        },
        (err, client) => {
          if (err) {
            reject(err);
          } else {
            resolve(client);
          }
        },
      );
    });
  }
}
