import { NectarGetDadosDividaResponse } from '@orchestrator/tool/integrations/dto/out/nectar-getdadosdivida-response.dto';
import {
  NectarGetOpcoesNegociacaoResponse,
  NectarOpcoesNegociacao,
} from '@orchestrator/tool/integrations/dto/out/nectar-getopcoesnegociacao-response.dto';

export interface NectarPort {
  getDadosDivida(document: string): Promise<NectarGetDadosDividaResponse>;
  getOpcoesNegociacao(
    idCon: string,
    titulo: string,
    parcelasNum: string,
    tpDesconto: string,
    percDescAplicNoPrincipal: string,
    percDescAplicNaCorrecao: string,
    vencPrimParcela: string,
  ): Promise<NectarGetOpcoesNegociacaoResponse>;
  getOpcoesNegociacaoWithMaxDiscountLessOne(
    idCon: string,
    titulos: string,
    parcelasNum: string,
    vencPrimParcela: string,
  ): Promise<NectarOpcoesNegociacao[]>;
}
