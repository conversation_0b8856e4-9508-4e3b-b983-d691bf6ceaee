import { Inject } from '@nestjs/common';

export class IntegrationToolFactory {
  constructor(
    @Inject('IntegrationToolFactory')
    private readonly integrations: IntegrationTool[],
  ) {}

  createIntegration(integrationName: string): IntegrationTool {
    const integrationInstance = this.integrations.find(i => i.canHandle(integrationName));
    if (!integrationInstance) {
      throw new Error(`Integration ${integrationName} not found`);
    }
    return integrationInstance;
  }
}

export interface IntegrationTool {
  canHandle(params: any): boolean;
  handle(params: any): Promise<any>;
}
