import { NectarPort } from '@orchestrator/tool/integrations/ports/soap/nectar.port';
import {
  IntegrationException,
  IntegrationExceptionStatus,
} from '@common/exception/types/IntegrationException';
import { Inject } from '@nestjs/common';
import { IntegrationTool } from '@orchestrator/tool/integrations/services/integration-tool-factory';

export class NectarIntegration implements IntegrationTool {
  constructor(
    @Inject('NectarPort')
    private readonly nectarAdapter: NectarPort,
  ) {}

  canHandle(integrationToolName: string): boolean {
    return integrationToolName === 'nectar-integration';
  }

  async handle(params: any): Promise<any> {
    return await this.retrieveCustomDataByIntegration(params);
  }

  private async retrieveCustomDataByIntegration(data: any) {
    const customDataFromIntegration = {};
    const document = data['CPF_DO_CLIENTE'];

    const getDadosDividaResponse = await this.nectarAdapter.getDadosDivida(document);

    const contractList = getDadosDividaResponse.GetDadosDividaResult.Contrato.Contrato.filter(
      c => !c.Acordo,
    );

    if (contractList.length <= 0) {
      throw new IntegrationException(
        `Nenhum contrato sem acordo encontrado para o CPF ${document}`,
        IntegrationExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    const contractInfo = contractList[0];

    customDataFromIntegration['DIAS_ATRASO'] = contractInfo.Divida.Divida[0].DiasAtraso;

    const informacoesAdicionais = this.extrairInformacoesAdicionais(
      contractInfo.Divida.Divida[0].InformacoesAdicionais,
    );

    customDataFromIntegration['NOME_LOJA'] = informacoesAdicionais['Rede'];

    customDataFromIntegration['NOME_DO_CARTAO'] = contractInfo.NomeFantasia;
    customDataFromIntegration['ID_DO_CONTRATO'] = contractInfo.IDCON;

    const titulos = contractInfo.Divida.Divida.map(divida => divida.IDTRA).join(',');

    customDataFromIntegration['TITULOS'] = titulos;

    const vencPrimParcela = data['VENC_PRIM_PARCELA'];

    const opcoesNegociacaoList = await this.nectarAdapter.getOpcoesNegociacaoWithMaxDiscountLessOne(
      contractInfo.IDCON,
      titulos,
      '',
      vencPrimParcela,
    );

    if (opcoesNegociacaoList.length <= 0) {
      throw new IntegrationException(
        `Nenhuma opção de pagamento encontrada para o contrato ${contractInfo.IDCON} - CPF: ${document}`,
        IntegrationExceptionStatus.ITEM_NOT_FOUND,
      );
    }

    customDataFromIntegration['VALOR_DIVIDA_ORIGINAL'] = opcoesNegociacaoList[0].ValorOriginal;
    customDataFromIntegration['VALOR_DIVIDA_CORRIGIDO'] = opcoesNegociacaoList[0].ValorCorrigido;

    const opcaoPagamentoList = opcoesNegociacaoList.flatMap(({ Parcelas, ValorDesconto }) =>
      Parcelas.Parcelas.map(parcela => {
        const opcaoPagamento = new OpcaoPagamentoPrompt();

        opcaoPagamento.valorDesconto = ValorDesconto;
        opcaoPagamento.numeroParcelas = parcela.Numero;
        opcaoPagamento.valorPrimeiraParcela = parcela.ValorEntrada;
        opcaoPagamento.valorDemaisParcelas = parcela.ValorDemaisParcelas ?? '0';
        opcaoPagamento.dataVencimentoPrimeiraParcela = new Date(
          parcela.DataVencimento,
        ).toLocaleDateString('pt-BR');
        opcaoPagamento.valorTotalAcordo = parcela.ValorNegociarParcela;

        return opcaoPagamento;
      }),
    );

    customDataFromIntegration['OPCOES_DE_PAGAMENTO'] = JSON.stringify(opcaoPagamentoList);

    return customDataFromIntegration;
  }

  private extrairInformacoesAdicionais(texto: string): Record<string, string> {
    // Split the text into lines
    const linhas = texto.split('<BR>');

    // Create an empty dictionary
    const dados: Record<string, string> = {};

    linhas.forEach(linha => {
      const [chave, valor] = linha.split(':').map(item => item.trim());
      if (chave && valor) {
        dados[chave] = valor;
      }
    });

    // Format the date if it exists
    if (dados['Data último pagamento']) {
      dados['Data último pagamento'] = this.formatarData(dados['Data último pagamento']);
    }

    return dados;
  }

  private formatarData(data: string): string {
    return data.length === 8
      ? `${data.substring(6, 8)}/${data.substring(4, 6)}/${data.substring(0, 4)}`
      : data;
  }
}
class OpcaoPagamentoPrompt {
  valorPrimeiraParcela: string;
  dataVencimentoPrimeiraParcela: string;
  numeroParcelas: string;
  valorDemaisParcelas: string;
  valorTotalAcordo: string;
  valorDesconto: string;
}
