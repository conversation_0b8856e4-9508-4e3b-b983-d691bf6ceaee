import { StepExecutionStatus } from '@common/enums';
import { Type } from 'class-transformer';
import { IsUUID, IsNotEmpty, ValidateNested, IsNumber, IsEnum } from 'class-validator';

export class WorkflowExecution {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsUUID('4')
  @IsNotEmpty()
  currentStepExecutionId: string;

  @ValidateNested({ each: true })
  @Type(() => StepExecution)
  stepExecutions: StepExecution[];

  constructor(
    id: string,
    workflowId: string,
    currentStepExecutionId: string,
    stepExecutions: StepExecution[],
  ) {
    this.id = id;
    this.workflowId = workflowId;
    this.currentStepExecutionId = currentStepExecutionId;
    this.stepExecutions = stepExecutions;
  }
}

export class StepExecution {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly stepId: string;

  @IsNumber()
  @IsNotEmpty()
  readonly order: number;

  @IsEnum(StepExecutionStatus)
  @IsNotEmpty()
  status: StepExecutionStatus;

  constructor(id: string, stepId: string, order: number, status: StepExecutionStatus) {
    this.id = id;
    this.stepId = stepId;
    this.order = order;
    this.status = status;
  }
}
