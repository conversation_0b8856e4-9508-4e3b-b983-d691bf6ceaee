import { MiddlewareToolCategory, MiddlewareType, RecordStatus } from '@common/enums';
import { Type } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsNumber, IsUUID, ValidateNested } from 'class-validator';

export class Workflow {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @ValidateNested({ each: true })
  @Type(() => Step)
  readonly steps: Step[];

  constructor(id: string, name: string, description: string, status: RecordStatus, steps: Step[]) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.status = status;
    this.steps = steps;
  }
}

export class Step {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsNotEmpty()
  readonly description: string;

  @IsNumber()
  @IsNotEmpty()
  readonly order: number;

  @IsNotEmpty()
  readonly taskId: string;

  readonly params: Record<string, any>;

  readonly middlewares: Middleware[];

  constructor(
    id: string,
    description: string,
    order: number,
    taskId: string,
    params: Record<string, any>,
    middlewares: Middleware[] = [],
  ) {
    this.id = id;
    this.description = description;
    this.order = order;
    this.taskId = taskId;
    this.params = params;
    this.middlewares = middlewares;
  }
}

export class Middleware {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsNotEmpty()
  readonly taskId: string;

  readonly params: Record<string, any>;

  readonly type: MiddlewareType;

  readonly showOff: boolean;

  readonly category: MiddlewareToolCategory;

  constructor(
    id: string,
    name: string,
    description: string,
    taskId: string,
    params: Record<string, any>,
    type: MiddlewareType,
    showOff: boolean,
    category: MiddlewareToolCategory = MiddlewareToolCategory.TASK,
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.taskId = taskId;
    this.params = params;
    this.type = type;
    this.showOff = showOff;
    this.category = category;
  }
}
