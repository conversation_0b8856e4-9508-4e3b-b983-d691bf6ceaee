import { MessageType, RoleType } from '@common/enums';

export class MessageHistory {
  readonly id: string;
  readonly role: RoleType;
  readonly messageText: string;
  readonly messageType: MessageType;
  readonly lang: string;
  readonly fileUrl?: string;
  readonly createdAt?: Date;
  readonly updatedAt?: Date;

  constructor(
    id: string,
    role: RoleType,
    messageText: string,
    messageType: MessageType,
    lang: string,
    fileUrl?: string,
    createdAt?: Date,
    updatedAt?: Date,
  ) {
    this.id = id;
    this.role = role;
    this.messageText = messageText;
    this.messageType = messageType;
    this.lang = lang;
    this.fileUrl = fileUrl;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }
}
