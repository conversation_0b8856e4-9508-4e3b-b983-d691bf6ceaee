import { MessageType } from '@common/enums';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class ExecuteTask {
  @IsUUID('4')
  @IsNotEmpty()
  readonly taskId: string;

  readonly thread: string | null;

  @IsNotEmpty()
  readonly inputType: MessageType;

  readonly params: Record<string, any>;

  readonly fileUrl: string | null;

  readonly lang: string;

  constructor(
    taskId: string,
    thread: string | null,
    inputType: MessageType,
    params: Record<string, any>,
    fileUrl: string | null,
    lang: string,
  ) {
    this.taskId = taskId;
    this.thread = thread;
    this.params = params;
    this.inputType = inputType;
    this.fileUrl = fileUrl;
    this.lang = lang;
  }
}
