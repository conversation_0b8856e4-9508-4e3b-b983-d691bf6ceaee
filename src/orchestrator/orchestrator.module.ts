import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { WorkflowController } from '@orchestrator/application/controllers/workflow.controller';
import { WorkFlowAdapter } from '@orchestrator/infrastructure/adapters/db/workflow.adapter';
import { WorkflowUseCase } from '@orchestrator/application/use-cases/workflow.use-case';
import { WorkFlowExecutionAdapter } from '@orchestrator/infrastructure/adapters/db/workflow-execution.adapter';
import { TaskAdapter } from '@orchestrator/infrastructure/adapters/http/task.adapter';
import { WorkflowExecutionUseCase } from '@orchestrator/application/use-cases/workflow-execution.use-case';
import { WorkflowExecutionController } from '@orchestrator/application/controllers/workflow-execution.controller';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { AgentAdapter } from '@orchestrator/infrastructure/adapters/http/agent.adapter';
import { NectarAdapter } from '@orchestrator/tool/integrations/adapters/soap/nectar.adapter';
import { NectarIntegration } from '@orchestrator/tool/integrations/services/nectar-integration';
import { IntegrationToolFactory } from '@orchestrator/tool/integrations/services/integration-tool-factory';

const httpModule = HttpModule.registerAsync({
  useFactory: () => ({
    timeout: 50000,
    maxRedirects: 5,
  }),
});

@Module({
  imports: [httpModule],
  providers: [
    WorkflowUseCase,
    WorkflowExecutionUseCase,
    DynamoService,
    NectarIntegration,
    { provide: 'WorkflowPort', useClass: WorkFlowAdapter },
    { provide: 'WorkFlowExecutionPort', useClass: WorkFlowExecutionAdapter },
    { provide: 'TaskPort', useClass: TaskAdapter },
    { provide: 'AgentPort', useClass: AgentAdapter },
    { provide: 'NectarPort', useClass: NectarAdapter },
    {
      provide: 'IntegrationToolFactory',
      useFactory: (nectarIntegration: NectarIntegration) => {
        return [nectarIntegration];
      },
      inject: [NectarIntegration],
    },
    IntegrationToolFactory,
  ],
  controllers: [WorkflowController, WorkflowExecutionController],
})
export class OrchestratorModule {}
