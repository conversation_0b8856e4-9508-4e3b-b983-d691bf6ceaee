import { MessageHistory } from '@orchestrator/misc/interfaces/out/message-history';
import { SendDirectMessageDto } from '@orchestrator/application/dto/out/send-direct-message.dto';

export interface AgentPort {
  createConversationHistory(
    workfloExecutionIdList: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistory[]>;

  getConversationHistory(workfloExecutionIdList: string): Promise<MessageHistory[]>;
}
