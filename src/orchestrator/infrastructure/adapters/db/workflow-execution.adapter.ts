import { DynamoDBDocumentClient, GetCommand, PutCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoException } from '@common/exception/types/DynamoException';
import { Injectable } from '@nestjs/common';
import { WorkflowExecution } from '@orchestrator/domain/entities/workflow-execution.entity';
import { WorkFlowExecutionPort } from '@orchestrator/infrastructure/ports/db/workflow-execution.port';

@Injectable()
export class WorkFlowExecutionAdapter implements WorkFlowExecutionPort {
  private dynamoClient: DynamoDBDocumentClient;
  DYANMO_TABLE_NAME = 'transcendence_workflow_executions';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async save(workflowExecution: WorkflowExecution): Promise<WorkflowExecution> {
    const params = {
      TableName: this.DYANMO_TABLE_NAME,
      Item: {
        id: workflowExecution.id,
        workflowExecution,
      },
    };

    try {
      await this.dynamoClient.send(new PutCommand(params));
      return Promise.resolve(workflowExecution);
    } catch (error) {
      const errorResponse = {
        message: `Error while saving workflow execution with id: ${workflowExecution.id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }

  async update(workflowExecution: WorkflowExecution): Promise<WorkflowExecution> {
    const params = {
      TableName: this.DYANMO_TABLE_NAME,
      Item: {
        id: workflowExecution.id,
        workflowExecution,
      },
    };

    try {
      await this.dynamoClient.send(new PutCommand(params));
      return Promise.resolve(workflowExecution);
    } catch (error) {
      const errorResponse = {
        message: 'Error while updating workflow execution',
        error: error,
      };
      throw new DynamoException(errorResponse);
    }
  }

  async getById(key: string): Promise<WorkflowExecution> {
    const params = {
      TableName: this.DYANMO_TABLE_NAME,
      Key: {
        id: key,
      },
    };

    try {
      const { Item } = await this.dynamoClient.send(new GetCommand(params));
      return Promise.resolve(Item.workflowExecution as WorkflowExecution);
    } catch (error) {
      const errorResponse = {
        message: `Error while fetching workflow execution with key: ${key}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }
}
