import { Injectable } from '@nestjs/common';
import { WorkflowPort } from '@orchestrator/infrastructure/ports/db/workflow.port';
import { Workflow } from '@orchestrator/domain/entities/workflow.entity';
import { DynamoService } from '@common/dynamo/dynamo.service';
import { DynamoDBDocumentClient, GetCommand, PutCommand, ScanCommand } from '@aws-sdk/lib-dynamodb';
import { DynamoException } from '@common/exception/types/DynamoException';
import { RecordStatus } from '@common/enums';

@Injectable()
export class WorkFlowAdapter implements WorkflowPort {
  private dynamoClient: DynamoDBDocumentClient;
  DYNAMO_TABLE_NAME = 'transcendence_workflows';

  constructor(private readonly dynamoService: DynamoService) {
    this.dynamoClient = this.dynamoService.dynamoClient;
  }

  async get(key: string): Promise<Workflow> {
    const params = {
      TableName: this.DYNAMO_TABLE_NAME,
      Key: {
        id: key,
      },
    };

    try {
      const { Item } = await this.dynamoClient.send(new GetCommand(params));
      return Promise.resolve(Item.workflow as Workflow);
    } catch (error) {
      const errorResponse = {
        message: `Error while fetching workflow with id: ${key}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }

  async save(workflow: Workflow): Promise<Workflow> {
    const params = {
      TableName: this.DYNAMO_TABLE_NAME,
      Item: {
        id: workflow.id,
        workflow,
      },
    };

    try {
      await this.dynamoClient.send(new PutCommand(params));
      return Promise.resolve(workflow);
    } catch (error) {
      const errorResponse = {
        message: `Error while saving workflow with id: ${workflow.id}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }

  async delete(workflow: Workflow): Promise<Workflow> {
    workflow.status = RecordStatus.DELETED;

    return Promise.resolve(this.save(workflow));
  }

  async getByName(name: string): Promise<Workflow> {
    const params = {
      TableName: this.DYNAMO_TABLE_NAME,
      FilterExpression: 'workflow.#name = :name',
      ExpressionAttributeNames: {
        '#name': 'name', // Use alias for reserved keyword
      },
      ExpressionAttributeValues: {
        ':name': name,
      },
    };

    try {
      const { Items } = await this.dynamoClient.send(new ScanCommand(params));
      if (!Items || Items.length === 0) {
        return Promise.resolve(null);
      }
      return Promise.resolve(Items[0].workflow as Workflow);
    } catch (error) {
      const errorResponse = {
        message: `Error while fetching workflow with name: ${name}. Error: ${
          error.message || error
        }. Type: ${error.constructor?.name || 'Unknown'}${
          error.stack && process.env.NODE_ENV !== 'production' ? `. Stack: ${error.stack}` : ''
        }`,
        error: error.message || error.toString(),
      };
      throw new DynamoException(errorResponse);
    }
  }
}
