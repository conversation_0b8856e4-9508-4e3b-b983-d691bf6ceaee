import { Injectable } from '@nestjs/common';
import { lastValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';
import { AxiosResponse } from 'axios';
import { handleHttpError } from '@common/utils/handle-http-error';
import { AgentPort } from '@orchestrator/infrastructure/ports/http/agent.port';
import { MessageHistory } from '@orchestrator/misc/interfaces/out/message-history';
import { SendDirectMessageDto } from '@orchestrator/application/dto/out/send-direct-message.dto';

@Injectable()
export class AgentAdapter implements AgentPort {
  private intelligenceServiceUrl: string;

  constructor(private httpService: HttpService) {
    this.intelligenceServiceUrl = process.env.INTELLIGENCE_SERVICE_URL.toString();
  }

  async createConversationHistory(
    stepExecutionId: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistory[]> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/agents/conversation-history/${stepExecutionId}`;
      const headers = {
        'Content-Type': 'application/json',
      };

      const response: AxiosResponse<any> = await lastValueFrom(
        this.httpService.post(url, sendDirectMessageDto, { headers }),
      );

      return response.data.data as MessageHistory[];
    } catch (error) {
      handleHttpError(error, 'Agent-adapter');
    }
  }

  async getConversationHistory(stepExecutionId: string): Promise<MessageHistory[]> {
    try {
      const url = `${this.intelligenceServiceUrl}/api/v1/intelligence/agents/conversation-history/${stepExecutionId}`;
      const headers = {
        'Content-Type': 'application/json',
      };

      const response: AxiosResponse<any> = await lastValueFrom(
        this.httpService.get(url, { headers }),
      );

      return response.data.data as MessageHistory[];
    } catch (error) {
      handleHttpError(error, 'Agent-adapter');
    }
  }
}
