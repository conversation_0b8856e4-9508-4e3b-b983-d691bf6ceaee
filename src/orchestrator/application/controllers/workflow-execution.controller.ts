import { AuthnGuard } from '@common/auth/authn.guard';
import { AuthzAccountGuard } from '@common/auth/authz-account.guard';
import { AuthzUserInAccountGuard } from '@common/auth/authz-user-in-account.guard';
import { ExcludeGuards } from '@common/auth/decorators/exclude-guard.decorator';
import { Body, Controller, Get, Param, Post, Version } from '@nestjs/common';
import { WorkflowExecutionUseCase } from '@orchestrator/application/use-cases/workflow-execution.use-case';
import { SendDirectMessageDto } from '@orchestrator/application/dto/in/send-direct-message.dto';

@ExcludeGuards(AuthnGuard.name, AuthzAccountGuard.name, AuthzUserInAccountGuard.name)
@Controller('orchestrator/workflow-executions')
export class WorkflowExecutionController {
  constructor(private readonly workflowExecutionUseCase: WorkflowExecutionUseCase) {}

  @Get('/:workflowExecutionId')
  @Version('1')
  async getWorkflowDetails(
    @Param('workflowExecutionId') workflowExecutionId: string,
  ): Promise<any> {
    const workflowExecutionDetailsResponse =
      await this.workflowExecutionUseCase.getWorkflowExecutionDetails(workflowExecutionId);
    return {
      statusCode: 200,
      data: workflowExecutionDetailsResponse,
    };
  }

  @Post('/conversation-history/:workfloExecutionId')
  @Version('1')
  async createConversationHistory(
    @Param('workfloExecutionId') workfloExecutionId: string,
    @Body() sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<any> {
    const conversationHistory = await this.workflowExecutionUseCase.createConversationHistory(
      workfloExecutionId,
      sendDirectMessageDto,
    );

    return {
      statusCode: 201,
      data: conversationHistory,
    };
  }

  @Get('/conversation-history/:workflowExecutionId')
  @Version('1')
  async findConversationHistory(
    @Param('workflowExecutionId') workflowExecutionId: string,
  ): Promise<any> {
    const conversationHistory = await this.workflowExecutionUseCase.getConversationHistory(
      workflowExecutionId,
    );

    return {
      statusCode: 200,
      data: conversationHistory,
    };
  }
}
