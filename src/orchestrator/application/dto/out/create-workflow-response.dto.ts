import { MiddlewareToolCategory, MiddlewareType, RecordStatus } from '@common/enums';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class WorkflowResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsNotEmpty()
  readonly status: RecordStatus;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => StepWorkflowResponseDto)
  readonly steps: StepWorkflowResponseDto[];

  constructor(
    workflowId: string,
    name: string,
    description: string,
    status: RecordStatus,
    steps: StepWorkflowResponseDto[],
  ) {
    this.workflowId = workflowId;
    this.name = name;
    this.description = description;
    this.status = status;
    this.steps = steps;
  }
}

export class StepWorkflowResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly stepId: string;

  @IsNotEmpty()
  readonly description: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly taskId: string;

  @IsNumber()
  @IsNotEmpty()
  readonly order: number;

  readonly middlewares: MiddlewareResponseDto[];

  constructor(
    stepId: string,
    description: string,
    taskId: string,
    order: number,
    middlewares: MiddlewareResponseDto[] = [],
  ) {
    this.stepId = stepId;
    this.description = description;
    this.taskId = taskId;
    this.order = order;
    this.middlewares = middlewares;
  }
}

export class MiddlewareResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsNotEmpty()
  readonly taskId: string;

  readonly params: Record<string, any>;

  readonly type: MiddlewareType;

  readonly showOff: boolean;

  readonly category: MiddlewareToolCategory;

  constructor(
    id: string,
    name: string,
    description: string,
    taskId: string,
    params: Record<string, any>,
    type: MiddlewareType,
    showOff: boolean,
    category: MiddlewareToolCategory = MiddlewareToolCategory.TASK,
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.taskId = taskId;
    this.params = params;
    this.type = type;
    this.showOff = showOff;
    this.category = category;
  }
}
