import { MessageType, StepExecutionStatus } from '@common/enums';
import { Type } from 'class-transformer';
import { IsUUID, IsNotEmpty, ValidateNested, IsNumber } from 'class-validator';

export class WorkflowExecutionDetailsResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowId: string;

  @IsUUID('4')
  @IsNotEmpty()
  currentStepExecutionId: string;

  @ValidateNested({ each: true })
  @Type(() => StepExecutionDetailsResponseDto)
  stepExecutions: StepExecutionDetailsResponseDto[];

  constructor(
    id: string,
    workflowId: string,
    currentStepExecutionId: string,
    stepExecutions: StepExecutionDetailsResponseDto[],
  ) {
    this.id = id;
    this.workflowId = workflowId;
    this.currentStepExecutionId = currentStepExecutionId;
    this.stepExecutions = stepExecutions;
  }
}

export class StepExecutionDetailsResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly id: string;

  @IsUUID('4')
  @IsNotEmpty()
  readonly stepId: string;

  @IsNumber()
  @IsNotEmpty()
  readonly order: number;

  @IsNotEmpty()
  status: StepExecutionStatus;

  thread: string;

  output: Buffer;

  outputType: MessageType;

  constructor(id: string, stepId: string, order: number, status: StepExecutionStatus) {
    this.id = id;
    this.stepId = stepId;
    this.order = order;
    this.status = status;
  }
}
