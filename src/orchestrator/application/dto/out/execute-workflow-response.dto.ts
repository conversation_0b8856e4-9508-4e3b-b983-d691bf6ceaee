import { MessageType } from '@common/enums';
import { IsNotEmpty, IsNumber, IsUUID } from 'class-validator';

export class ExecuteWorkflowResponseDto {
  @IsNotEmpty()
  @IsUUID('4')
  readonly workflowExecutionId: string;

  @IsNotEmpty()
  @IsUUID('4')
  readonly stepExecutionId: string;

  @IsNotEmpty()
  @IsNumber()
  readonly stepExecutionOrder: number;

  readonly output: Buffer;

  readonly middlewaresResponse: Record<string, any>;

  @IsNotEmpty()
  outputType: MessageType;

  constructor(
    workFlowExecutionId: string,
    stepExecutionId: string,
    orderExecution: number,
    output: Buffer,
    outputType: MessageType,
    middlewaresResponse: Record<string, any>,
  ) {
    this.workflowExecutionId = workFlowExecutionId;
    this.stepExecutionId = stepExecutionId;
    this.stepExecutionOrder = orderExecution;
    this.output = output;
    this.outputType = outputType;
    this.outputType = outputType;
    this.middlewaresResponse = middlewaresResponse;
  }
}
