import { IsNotEmpty, <PERSON><PERSON><PERSON>ber, IsUUID } from 'class-validator';

export class StartWorkflowResponseDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowExecutionId: string;

  @IsNotEmpty()
  @IsUUID('4')
  readonly stepExecutionId: string;

  @IsNotEmpty()
  @IsNumber()
  readonly stepExecutionOrder: number;

  constructor(workflowExecutionId: string, stepExecutionId: string, stepExecutionOrder: number) {
    this.workflowExecutionId = workflowExecutionId;
    this.stepExecutionId = stepExecutionId;
    this.stepExecutionOrder = stepExecutionOrder;
  }
}
