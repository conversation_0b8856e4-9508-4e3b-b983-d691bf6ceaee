import { MessageType } from '@common/enums';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class ExecuteWorkflowRequestDto {
  @IsUUID('4')
  @IsNotEmpty()
  readonly workflowExecutionId: string;

  @IsOptional()
  readonly params: Record<string, any> | null;

  @IsOptional()
  @IsString()
  readonly fileUrl: string | null;

  @IsString()
  @IsNotEmpty()
  readonly lang: string;

  @IsNotEmpty()
  @IsEnum(MessageType)
  readonly messageType: MessageType;

  constructor(
    workflowExecutionId: string,
    params: Record<string, any> | null,
    fileUrl: string | null,
    lang: string,
    messageType: MessageType,
  ) {
    this.workflowExecutionId = workflowExecutionId;
    this.params = params;
    this.fileUrl = fileUrl;
    this.lang = lang;
    this.messageType = messageType;
  }
}
