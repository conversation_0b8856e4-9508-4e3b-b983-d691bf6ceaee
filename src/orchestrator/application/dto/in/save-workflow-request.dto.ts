import { MiddlewareToolCategory, MiddlewareType } from '@common/enums';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsUUID,
  ValidateNested,
} from 'class-validator';

export class SaveWorkflowRequestDto {
  @IsUUID('4')
  @IsOptional()
  id?: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateStepDto)
  readonly steps: CreateStepDto[];

  constructor(name: string, description: string, steps: CreateStepDto[]) {
    this.name = name;
    this.description = description;
    this.steps = steps;
  }
}

export class CreateMiddlewareDto {
  @IsUUID('4')
  @IsOptional()
  readonly id?: string;

  @IsNotEmpty()
  readonly name: string;

  @IsNotEmpty()
  readonly description: string;

  @IsOptional()
  readonly taskId?: string;

  @IsOptional()
  readonly params?: Record<string, any>;

  @IsEnum(MiddlewareType)
  @IsNotEmpty()
  readonly type: MiddlewareType;

  @IsBoolean()
  @IsNotEmpty()
  readonly showOff: boolean;

  @IsOptional()
  readonly category: MiddlewareToolCategory;

  constructor(
    id: string,
    name: string,
    description: string,
    taskId: string,
    params: Record<string, any>,
    type: MiddlewareType,
    showOff: boolean,
    toolCategory: MiddlewareToolCategory = MiddlewareToolCategory.TASK,
  ) {
    this.id = id;
    this.name = name;
    this.description = description;
    this.taskId = taskId;
    this.params = params;
    this.type = type;
    this.showOff = showOff;
    this.category = toolCategory;
  }
}

class CreateStepDto {
  @IsNotEmpty()
  readonly description: string;

  @IsNumber()
  @IsNotEmpty()
  readonly order: number;

  @IsNotEmpty()
  readonly taskId: string;

  @IsOptional()
  readonly params?: Record<string, any>;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateMiddlewareDto)
  @IsOptional()
  readonly middlewares?: CreateMiddlewareDto[];

  constructor(
    description: string,
    order: number,
    taskId: string,
    params: Record<string, any>,
    middlewares: CreateMiddlewareDto[] = [],
  ) {
    this.description = description;
    this.order = order;
    this.taskId = taskId;
    this.params = params;
    this.middlewares = middlewares;
  }
}
