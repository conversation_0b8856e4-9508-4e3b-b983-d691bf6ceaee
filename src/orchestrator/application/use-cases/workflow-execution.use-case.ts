import { Inject, Injectable } from '@nestjs/common';
import { WorkFlowExecutionPort } from '@orchestrator/infrastructure/ports/db/workflow-execution.port';
import {
  StepExecutionDetailsResponseDto,
  WorkflowExecutionDetailsResponseDto,
} from '@orchestrator/application/dto/out/workflow-execution-details-response.dto';
import { logger } from '@edutalent/commons-sdk';
import { MessageHistoryResponseDto } from '@orchestrator/application/dto/out/message-history-response.dto';
import { AgentPort } from '@orchestrator/infrastructure/ports/http/agent.port';
import { SendDirectMessageDto } from '@orchestrator/application/dto/in/send-direct-message.dto';

@Injectable()
export class WorkflowExecutionUseCase {
  constructor(
    @Inject('WorkFlowExecutionPort')
    private readonly workFlowExecutionAdapter: WorkFlowExecutionPort,
    @Inject('AgentPort')
    private readonly agentAdapter: AgentPort,
  ) {}

  async getWorkflowExecutionDetails(
    workflowExecutionId: string,
  ): Promise<WorkflowExecutionDetailsResponseDto> {
    logger.info(`Getting workflow execution details for id: ${workflowExecutionId}`);
    const workflowExecution = await this.workFlowExecutionAdapter.getById(workflowExecutionId);

    const stepExecutionDetails = workflowExecution.stepExecutions.map(stepExecution => {
      return new StepExecutionDetailsResponseDto(
        stepExecution.id,
        stepExecution.stepId,
        stepExecution.order,
        stepExecution.status,
      );
    });

    return new WorkflowExecutionDetailsResponseDto(
      workflowExecution.id,
      workflowExecution.workflowId,
      workflowExecution.currentStepExecutionId,
      stepExecutionDetails,
    );
  }

  async getConversationHistory(workflowExecutionId: string): Promise<MessageHistoryResponseDto[]> {
    const workflowExecution = await this.workFlowExecutionAdapter.getById(workflowExecutionId);

    const stepExecutionDetails = workflowExecution.stepExecutions.map(
      stepExecution => stepExecution.id,
    );

    const response: MessageHistoryResponseDto[] = [];

    for (const stepExecutionId of stepExecutionDetails) {
      const conversationHistory = await this.agentAdapter.getConversationHistory(stepExecutionId);
      response.push(...conversationHistory);
    }

    return response;
  }

  async createConversationHistory(
    workflowExecutionId: string,
    sendDirectMessageDto: SendDirectMessageDto,
  ): Promise<MessageHistoryResponseDto[]> {
    const workflowExecution = await this.workFlowExecutionAdapter.getById(workflowExecutionId);

    await this.agentAdapter.createConversationHistory(
      workflowExecution.currentStepExecutionId,
      sendDirectMessageDto,
    );

    const stepExecutionDetails = workflowExecution.stepExecutions.map(
      stepExecution => stepExecution.id,
    );

    const response: MessageHistoryResponseDto[] = [];

    for (const stepExecutionId of stepExecutionDetails) {
      const conversationHistory = await this.agentAdapter.getConversationHistory(stepExecutionId);
      response.push(...conversationHistory);
    }

    return response;
  }
}
