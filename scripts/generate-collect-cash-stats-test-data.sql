-- Generate 1,000 test records for collect_cash_stats table
-- Based on existing record structure and Prisma schema

-- Using the existing values from the database for consistency
-- customer_id: 5e829edb-ab9d-4295-b255-30ea63a4f650
-- portfolio_id: 4103863d-dc7d-40e3-b089-ec95216ac7b1
-- workflow_id: 0ea8d39a-c341-43a9-8131-6f0f92eb7391

WITH test_data AS (
    SELECT
        gen_random_uuid() as id,
        '5e829edb-ab9d-4295-b255-30ea63a4f650'::uuid as customer_id,
        '4103863d-dc7d-40e3-b089-ec95216ac7b1'::uuid as portfolio_id,
        gen_random_uuid() as portfolio_item_id,
        '0ea8d39a-c341-43a9-8131-6f0f92eb7391'::uuid as workflow_id,
        -- deal_value: Random values between 10,000 and 1,000,000 cents (100 to 10,000 BRL)
        (random() * 990000 + 10000)::int as deal_value,
        -- installments: Random values between 1 and 60
        (random() * 59 + 1)::int as installments,
        -- original_debt: Random values between 5,000 and 800,000 cents (50 to 8,000 BRL)
        (random() * 795000 + 5000)::int as original_debt,
        'ACTIVE' as status,
        -- created_at: Random timestamps in the last 90 days
        NOW() - (random() * interval '90 days') as base_created_at
    FROM generate_series(1, 1000)
)
INSERT INTO business_base.collect_cash_stats (
    id,
    customer_id,
    portfolio_id,
    portfolio_item_id,
    workflow_id,
    deal_value,
    installments,
    original_debt,
    status,
    created_at,
    updated_at
)
SELECT
    id,
    customer_id,
    portfolio_id,
    portfolio_item_id,
    workflow_id,
    deal_value,
    installments,
    original_debt,
    status,
    base_created_at as created_at,
    -- updated_at: created_at + random interval (0 to 30 days) to ensure updated_at >= created_at
    base_created_at + (random() * interval '30 days') as updated_at
FROM test_data;
