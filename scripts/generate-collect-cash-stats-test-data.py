#!/usr/bin/env python3
"""
Generate test data for collect_cash_stats table for performance testing.
This script creates realistic test data based on existing records structure.
"""

import uuid
import random
import psycopg2
from datetime import datetime, timedelta
from typing import List, Tuple

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': 5442,
    'database': 'transcendence_db',
    'user': 'postgres',
    'password': 'password123'
}

# Constants from existing data
CUSTOMER_ID = '5e829edb-ab9d-4295-b255-30ea63a4f650'
PORTFOLIO_ID = '4103863d-dc7d-40e3-b089-ec95216ac7b1'
WORKFLOW_ID = '0ea8d39a-c341-43a9-8131-6f0f92eb7391'

def generate_test_record() -> Tuple:
    """Generate a single test record with realistic values."""
    
    # Generate random values based on realistic ranges
    deal_value = random.randint(10000, 1000000)  # 100 to 10,000 BRL in cents
    installments = random.randint(1, 60)  # 1 to 60 installments
    original_debt = random.randint(5000, 800000)  # 50 to 8,000 BRL in cents
    
    # Generate timestamps - created_at in last 90 days, updated_at after created_at
    days_ago = random.randint(0, 90)
    created_at = datetime.now() - timedelta(days=days_ago)
    
    # updated_at is created_at + 0 to 30 days
    update_days = random.randint(0, 30)
    updated_at = created_at + timedelta(days=update_days)
    
    return (
        str(uuid.uuid4()),  # id
        CUSTOMER_ID,        # customer_id
        PORTFOLIO_ID,       # portfolio_id
        str(uuid.uuid4()),  # portfolio_item_id
        WORKFLOW_ID,        # workflow_id
        deal_value,         # deal_value
        installments,       # installments
        original_debt,      # original_debt
        'ACTIVE',          # status
        created_at,        # created_at
        updated_at         # updated_at
    )

def generate_batch_insert_sql(records: List[Tuple]) -> str:
    """Generate batch INSERT SQL statement."""
    
    sql = """
    INSERT INTO business_base.collect_cash_stats (
        id, customer_id, portfolio_id, portfolio_item_id, workflow_id,
        deal_value, installments, original_debt, status, created_at, updated_at
    ) VALUES
    """
    
    values = []
    for record in records:
        value_str = "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)"
        values.append(value_str)
    
    sql += ",\n    ".join(values)
    return sql

def insert_test_data(num_records: int = 1000, batch_size: int = 100):
    """Insert test data into the database in batches."""
    
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print(f"Generating {num_records} test records...")
        
        # Generate and insert data in batches
        for i in range(0, num_records, batch_size):
            current_batch_size = min(batch_size, num_records - i)
            
            # Generate batch of records
            records = [generate_test_record() for _ in range(current_batch_size)]
            
            # Create SQL and execute
            sql = generate_batch_insert_sql(records)
            cursor.execute(sql, [item for record in records for item in record])
            
            print(f"Inserted batch {i//batch_size + 1}: {current_batch_size} records")
        
        # Commit transaction
        conn.commit()
        print(f"Successfully inserted {num_records} test records!")
        
        # Verify insertion
        cursor.execute("SELECT COUNT(*) FROM business_base.collect_cash_stats")
        total_count = cursor.fetchone()[0]
        print(f"Total records in table: {total_count}")
        
    except Exception as e:
        print(f"Error: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def generate_sql_file(num_records: int = 1000, filename: str = "collect_cash_stats_test_data.sql"):
    """Generate SQL file with INSERT statements."""
    
    print(f"Generating SQL file with {num_records} records...")
    
    with open(filename, 'w') as f:
        f.write("-- Generated test data for collect_cash_stats table\n")
        f.write(f"-- {num_records} records for performance testing\n\n")
        
        # Generate records in batches to avoid memory issues
        batch_size = 100
        for i in range(0, num_records, batch_size):
            current_batch_size = min(batch_size, num_records - i)
            records = [generate_test_record() for _ in range(current_batch_size)]
            
            sql = generate_batch_insert_sql(records)
            f.write(sql + ";\n\n")
            
            print(f"Generated batch {i//batch_size + 1}")
    
    print(f"SQL file '{filename}' generated successfully!")

if __name__ == "__main__":
    import sys
    
    # Default to 1000 records
    num_records = 1000
    if len(sys.argv) > 1:
        num_records = int(sys.argv[1])
    
    print("Choose an option:")
    print("1. Insert directly into database")
    print("2. Generate SQL file")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        insert_test_data(num_records)
    elif choice == "2":
        generate_sql_file(num_records)
    else:
        print("Invalid choice. Exiting.")
