#!/bin/bash

# Script to generate test data for collect_cash_stats table
# Usage: ./run-test-data-generation.sh [number_of_records]

set -e

# Default number of records
NUM_RECORDS=${1:-1000}

# Database connection string
DB_URL="postgres://postgres:password123@localhost:5442/transcendence_db"

echo "🚀 Generating $NUM_RECORDS test records for collect_cash_stats table..."

# Check current record count
echo "📊 Current record count:"
psql "$DB_URL" -c "SELECT COUNT(*) as current_records FROM business_base.collect_cash_stats;"

echo ""
echo "⏳ Generating test data..."

# Create temporary SQL file with the specified number of records
cat > /tmp/temp_test_data.sql << EOF
-- Generate $NUM_RECORDS test records for collect_cash_stats table
-- Based on existing record structure and Prisma schema

WITH test_data AS (
    SELECT 
        gen_random_uuid() as id,
        '5e829edb-ab9d-4295-b255-30ea63a4f650'::uuid as customer_id,
        '4103863d-dc7d-40e3-b089-ec95216ac7b1'::uuid as portfolio_id,
        gen_random_uuid() as portfolio_item_id,
        '0ea8d39a-c341-43a9-8131-6f0f92eb7391'::uuid as workflow_id,
        -- deal_value: Random values between 10,000 and 1,000,000 cents (100 to 10,000 BRL)
        (random() * 990000 + 10000)::int as deal_value,
        -- installments: Random values between 1 and 60
        (random() * 59 + 1)::int as installments,
        -- original_debt: Random values between 5,000 and 800,000 cents (50 to 8,000 BRL)
        (random() * 795000 + 5000)::int as original_debt,
        'ACTIVE' as status,
        -- created_at: Random timestamps in the last 90 days
        NOW() - (random() * interval '90 days') as base_created_at
    FROM generate_series(1, $NUM_RECORDS)
)
INSERT INTO business_base.collect_cash_stats (
    id,
    customer_id,
    portfolio_id,
    portfolio_item_id,
    workflow_id,
    deal_value,
    installments,
    original_debt,
    status,
    created_at,
    updated_at
)
SELECT 
    id,
    customer_id,
    portfolio_id,
    portfolio_item_id,
    workflow_id,
    deal_value,
    installments,
    original_debt,
    status,
    base_created_at as created_at,
    -- updated_at: created_at + random interval (0 to 30 days) to ensure updated_at >= created_at
    base_created_at + (random() * interval '30 days') as updated_at
FROM test_data;
EOF

# Execute the SQL
psql "$DB_URL" -f /tmp/temp_test_data.sql

echo ""
echo "✅ Test data generation completed!"

# Show final record count
echo "📊 Final record count:"
psql "$DB_URL" -c "SELECT COUNT(*) as total_records FROM business_base.collect_cash_stats;"

# Show data distribution
echo ""
echo "📈 Data distribution summary:"
psql "$DB_URL" -c "
SELECT 
    MIN(deal_value) as min_deal_value,
    MAX(deal_value) as max_deal_value,
    AVG(deal_value)::int as avg_deal_value,
    MIN(installments) as min_installments,
    MAX(installments) as max_installments,
    AVG(installments)::int as avg_installments,
    MIN(original_debt) as min_original_debt,
    MAX(original_debt) as max_original_debt,
    AVG(original_debt)::int as avg_original_debt
FROM business_base.collect_cash_stats;
"

# Clean up
rm -f /tmp/temp_test_data.sql

echo ""
echo "🎉 Done! You can now run performance tests on the collect_cash_stats table."
echo ""
echo "💡 Tips for performance testing:"
echo "   - Test queries with different WHERE clauses using the indexed columns"
echo "   - Try queries filtering by customer_id, portfolio_id, workflow_id"
echo "   - Test date range queries using created_at"
echo "   - Monitor query execution plans with EXPLAIN ANALYZE"
