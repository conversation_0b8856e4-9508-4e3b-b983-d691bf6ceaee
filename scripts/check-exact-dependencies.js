const fs = require('fs');

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const dependencies = {...packageJson.dependencies, ...packageJson.devDependencies};

// RegEx para una versión exacta, permitiendo un sufijo opcional después del número de parche
const versionRegex = /^\d+(\.\d+){2}(-[\dA-Za-z-.]*)?$/;

for (const [pkg, version] of Object.entries(dependencies)) {
    if (!versionRegex.test(version)) {
        console.error(`Dependency "${pkg}" has no exact version: ${version}`);
        process.exit(1);
    }
}

console.log('All dependencies have exact version.');
