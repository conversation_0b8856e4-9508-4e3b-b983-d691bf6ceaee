const { CloudWatchLogsClient, StartQueryCommand, GetQueryResultsCommand } = require("@aws-sdk/client-cloudwatch-logs");

// Configure the CloudWatch Logs client
const client = new CloudWatchLogsClient({
  region: process.env.AWS_REGION,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
  }
});

async function runQuery() {
  const startTime = Date.now() - (1000 * 60 * 15);
  const endTime = Date.now();
  const queryString = "fields @timestamp, @message, @logStream, @log | filter @message like 'migrate found failed'";

  const startQueryParams = {
    logGroupName: process.env.CLOUDWATCH_LOG_GROUP_NAME,
    startTime: startTime,
    endTime: endTime,
    queryString: queryString,
  };

  try {
    // Start the query
    const startQueryCommand = new StartQueryCommand(startQueryParams);
    const startQueryResponse = await client.send(startQueryCommand);
    const queryId = startQueryResponse.queryId;

    // Wait for the query to complete
    let queryStatus = 'Running';
    let results;
    while (queryStatus === 'Running' || queryStatus === 'Scheduled') {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait for 1 second.
      const getQueryResultsCommand = new GetQueryResultsCommand({ queryId });
      const queryResults = await client.send(getQueryResultsCommand);
      queryStatus = queryResults.status;
      if (queryStatus === 'Complete') {
        results = queryResults.results;
      }
    }

    // Check the number of results
    if (results && results.length > 0) {
      console.error(`We found ${results.length} migration error(s) in the logs.\n`);
      console.error('Migration fail(s) - log detail:', results);
      process.exit(1); // Fail if there are results
    } else {
      console.log('No migration error found in the logs.');
    }
  } catch (error) {
    console.error('Error executing the query:', error);
    process.exit(1); // Fail in case of error
  }
}

runQuery();
