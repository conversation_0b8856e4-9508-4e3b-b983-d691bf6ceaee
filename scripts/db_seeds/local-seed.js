const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const { v4: uuidv4 } = require('uuid');
const AWS = require('aws-sdk');
const bcrypt = require('bcrypt');

const { DynamoDBClient } = require('@aws-sdk/client-dynamodb');
const { DynamoDBDocumentClient, PutCommand } = require('@aws-sdk/lib-dynamodb');
//const {S3Service} = require('/src/business-base/services/s3.service');

const fs = require('fs');
const yaml = require('js-yaml');

const WORKFLOW_TABLE_NAME = 'transcendence_workflows';
const CUSTOMER_CHANNEL_INTEGRATION_DATA_DEFINITION_TABLE_NAME = 'transcendence_customer_channel_integration_data';
const CUSTOMER_PREFERENCES_TABLE_NAME = 'transcendence_customer_preferences';

const marshallOptions = {
  // Whether to automatically convert empty strings, blobs, and sets to `null`.
  // convertEmptyValues: false, // false, by default.
  // Whether to remove undefined values while marshalling.
  removeUndefinedValues: true, // false, by default.
  // Whether to convert typeof object to map attribute.
  convertClassInstanceToMap: true, // false, by default. <---- Set this flag
};
const unmarshallOptions = {
  // Whether to return numbers as a string instead of converting them to native JavaScript numbers.
  // wrapNumbers: false, // false, by default.
};

const seedDataDirectory = `${process.cwd()}/scripts/db_seeds/data`;

async function main() {
  const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
    endpoint: process.env.AWS_ENDPOINT_URL,
    sslEnabled: false,
    s3ForcePathStyle: true,
  });

  try {

    await prisma.$connect();
    console.log('Successfully connected to the database.');

    const result = await prisma.$queryRaw`SELECT current_database();`;
    const dbName = result[0].current_database;
    console.log('Database name:', dbName);

    const expectedDbsName = ['transcendence_db', 'transcendence_e2e_db'];

    if (!expectedDbsName.includes(dbName)) {
      throw new Error(`Connected to the wrong database: ${dbName}`);
    }

    const agentsYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/agents.yml`, 'utf8'));
    const agentsData = agentsYamlData.agents;
    const agents = await prisma.agent.createMany({
      data: agentsData,
    });

    console.log('Seed data inserted agents:', agents);


    const tasksYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/tasks.yml`, 'utf8'));
    const taskData = tasksYamlData.tasks;
    const tasks = await prisma.task.createMany({
      data: taskData,
    });

    console.log('Seed data inserted tasks:', tasks);

    const customersYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/customers.yml`, 'utf8'));
    const customerData = customersYamlData.customers;
    const customers = await prisma.customer.createMany({
      data: customerData,
    });

    console.log('Seed data inserted customers:', customers);

    const portfolioYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/portfolio.yml`, 'utf8'));
    const portfolioData = portfolioYamlData.portfolios;
    const portfolios = await prisma.portfolio.createMany({
      data: portfolioData,
    });

    console.log('Seed data inserted portfolios:', portfolios);

    const customerWorkflowsYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/customer-workflows.yml`, 'utf8'));
    const customerWorkflowsData = customerWorkflowsYamlData.customerWorkflows;
    const customerWorkflows = await prisma.customerWorkflow.createMany({
      data: customerWorkflowsData,
    });

    console.log('Seed data inserted customer workflows:', customerWorkflows);

    const accountsYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/accounts.yml`, 'utf8'));
    const accountsData = accountsYamlData.accounts;
    const accounts = await prisma.account.createMany({
      data: accountsData,
    });

    console.log('Seed data inserted accounts:', accounts);

    const usersYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/users.yml`, 'utf8'));
    const usersData = usersYamlData.users.map(user => {
      const salt = bcrypt.genSaltSync(10);
      const hashedPassword = bcrypt.hashSync(user.password, salt);
      user.password = hashedPassword;
      user.salt = salt;
      return user;
    });
    const users = await prisma.user.createMany({
      data: usersData,
    });

    console.log('Seed data inserted users:', users);

    const portfolioItemStatusMappingYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/portfolio-item-status-mapping.yml`, 'utf8'));
    const mappingsData = portfolioItemStatusMappingYamlData.mappings;
    const mappings = await prisma.portfolioItemStatusMapping.createMany({
      data: mappingsData,
    });

    console.log('Seed data inserted portfolio-item-status-mappings:', mappings);

    const conversationsYmlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/conversations.yml`, 'utf8'));
    const conversationsData = conversationsYmlData.conversations;
    const conversations = await prisma.conversation.createMany({
      data: conversationsData,
    });

    console.log('Seed data inserted conversations:', conversations);

    const phoneWhatsAppYmlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/customer-phones.yml`, 'utf8'));
    const phoneWhatsAppData = phoneWhatsAppYmlData.customerPhones;
    const phoneWhatsApp = await prisma.customerPhone.createMany({
      data: phoneWhatsAppData,
    });

    console.log('Seed data inserted customer whatsapp phones:', phoneWhatsApp);

    const translateConfig = { marshallOptions, unmarshallOptions };
    const client = new DynamoDBClient({
      region: process.env.AWS_REGION,
    });

    const dynamoDBDocumentClient = DynamoDBDocumentClient.from(client, translateConfig);
    const workFlowYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/workflows.yml`, 'utf8'));
    const workFlowsData = workFlowYamlData.workflows;

    workFlowsData.forEach(async workflow => {
      const params = {
        TableName: WORKFLOW_TABLE_NAME,
        Item: {
          id: workflow.id,
          workflow,
        },
      };

      await dynamoDBDocumentClient.send(new PutCommand(params));
    });

    console.log('Seed data inserted workflows:', workFlowsData);

    const customerChannelIntegrationDataDefinitionYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/customer-channel-integration-data-definition.yml`, 'utf8'));
    const customerChannelIntegrationDataDefinitionData = customerChannelIntegrationDataDefinitionYamlData.customerChannelIntegrationDataDefinitions;

    customerChannelIntegrationDataDefinitionData.forEach(async customerChannelIntegrationDataDefinition => {
      const params = {
        TableName: CUSTOMER_CHANNEL_INTEGRATION_DATA_DEFINITION_TABLE_NAME,
        Item: customerChannelIntegrationDataDefinition,
      };

      await dynamoDBDocumentClient.send(new PutCommand(params));
    });

    console.log('Seed data inserted customer channel integration data definition:', customerChannelIntegrationDataDefinitionData);

    const customerPreferencesYamlData = yaml.load(fs.readFileSync(`${seedDataDirectory}/customer-preferences.yml`, 'utf8'));
    const customerPreferencesData = customerPreferencesYamlData.customerPreferences;

    customerPreferencesData.forEach(async customerPreference => {
      const now = new Date().toISOString();
      const params = {
        TableName: CUSTOMER_PREFERENCES_TABLE_NAME,
        Item: {
          ...customerPreference,
          status: 'ACTIVE',
          createdAt: now,
          updatedAt: now,
        },
      };

      await dynamoDBDocumentClient.send(new PutCommand(params));
    });

    console.log('Seed data inserted customer preferences:', customerPreferencesData);

    const sqs = new AWS.SQS({
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION,
      endpoint: process.env.AWS_ENDPOINT_URL,
      sslEnabled: false,
      s3ForcePathStyle: true,
    });

    console.log(`Creating import item queues for customers: ${JSON.stringify(customerData.map(customer => customer.id))}`);
    customerData.map(async customer => {
      const customerImportItemQueueName = `${process.env.IMPORT_ITEM_QUEUE_PREFIX}${customer.id}${process.env.IMPORT_ITEM_QUEUE_SUFIX}`;
      console.log(`Creating import item queue: ${customerImportItemQueueName}`);
      await sqs.createQueue({ QueueName: customerImportItemQueueName },
        (err, data) => {
          if (err) {
            console.log(`Error creating queue for customer portfolio item import: ${customerImportItemQueueName}`, err);
          } else {
            console.log(`Success: Created queue for customer portfolio item import: ${customerImportItemQueueName}`, data);
          }
        });
    });

    console.log(`Uploading audio file to S3 bucket: ${process.env.DIRECT_MESSAGE_FILES_BUCKET}`);
    const audioSeedData = fs.readFileSync(`${seedDataDirectory}/interview-answer.mp3`);
    const s3UploadParams = {
      Bucket: process.env.DIRECT_MESSAGE_FILES_BUCKET,
      Key: 'interview-answer.mp3',
      Body: audioSeedData,
    };

    try {
      const data = await s3.upload(s3UploadParams).promise();
      console.log(data);
    } catch (err) {
      console.log(err);
    }

    if (process.env.NODE_ENV === 'local') {
      console.log('Seed S3 bucket sample CSV:');



      const portfolioCsvData = fs.readFileSync(`${seedDataDirectory}/portfolio.csv`, 'utf8');
      const s3UploadParams = {
        Bucket: process.env.PORTFOLIO_IMPORT_FILES_BUCKET,
        Key: '4cd6d515-2604-4c2c-adad-435acbef1f5c/30b0e1fb-64c7-4519-93aa-4eb5dd33087e.csv',
        Body: portfolioCsvData,
      };

      try {
        const data = await s3.upload(s3UploadParams).promise();
        console.log(data);
      } catch (err) {
        console.log(err);
      }




      console.log('Producing message to portfolio import in collectcash segment, for portfolio: ', `${seedDataDirectory}/portfolio.csv`);

      sqs.sendMessage({
        QueueUrl: process.env.PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL,
        MessageBody: JSON.stringify({
          'phoneNumberColumn': 'phone_number',
          'portfolioId': '30b0e1fb-64c7-4519-93aa-4eb5dd33087e',
          'fileKey': '4cd6d515-2604-4c2c-adad-435acbef1f5c/30b0e1fb-64c7-4519-93aa-4eb5dd33087e.csv',
        }),
      },
        (err, data) => {
          if (err) {
            console.log('Error', err);
          } else {
            console.log('Success', data.MessageId);
          }
        });

      console.log('Producing message to portfolio2 import in collectcash segment, for portfolio: ', `${seedDataDirectory}/portfolio2.csv`);
      sqs.sendMessage({
        QueueUrl: process.env.PORTFOLIO_IMPORT_COLLECTCASH_QUEUE_URL,
        MessageBody: JSON.stringify({
          'phoneNumberColumn': 'phone_number',
          'portfolioId': '0f88fd47-331a-42bc-b07a-d082cdc4fbb2',
          'fileKey': '4cd6d515-2604-4c2c-adad-435acbef1f5c/0f88fd47-331a-42bc-b07a-d082cdc4fbb2.csv',
        }),
      },
        (err, data) => {
          if (err) {
            console.log('Error', err);
          } else {
            console.log('Success', data.MessageId);
          }
        });
    }
  } catch (error) {
    console.error('Error in seed script:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
