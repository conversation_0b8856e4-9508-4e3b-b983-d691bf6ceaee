workflows:
  - id: 43e3ceed-58da-4ff2-bddf-67cb79d4433f
    name: 'digai-negociador-divida-workflow'
    description:
    steps:
      - id: 799271ac-a4d9-45f2-a1a7-2a1ee3e13b37
        description: 'Negociar dívida'
        order: 1
        taskId: f3f15c15-51c5-40cb-8ea3-6caccaa44557
        params:
        middlewares:
          - taskId: d3b2e22d-a2ae-4f3d-b712-4ae9927ff62e
            name: 'interaction-status'
            description: 'Extrair o status da negociação da conversa fornecida em formato JSON e o output deve ser ON_GOING ou ACCEPTED'
            type: 'POST_EXECUTION'
            showOff: false
          - taskId: d3b2e22d-a2ae-4f3d-b712-4ae9927ff63f
            name: 'follow-up-scheduler'
            description: 'Extrair o status da negociação da conversa fornecida em formato JSON e extrair as datas de follow-up'
            type: 'POST_EXECUTION'
            showOff: true
          - taskId: d3b2e22d-a2ae-4f3d-b712-4ae9927ff64f
            name: 'deal-info'
            description: 'Extrair o status da negociação da conversa fornecida em formato JSON e extrair as datas de follow-up'
            type: 'POST_EXECUTION'
            showOff: true

  - id: c1314615-94bf-49d1-9b55-046a391ae0b4
    name: negociador-divida-follow-up
    description: Follow up da dívida
    steps:
      - description: Follow up da dívida
        order: 1
        taskId: e6f70558-7457-40d9-b18c-8a0831a95b2a
        middlewares:
          - taskId: c367dd4e-c995-4440-a3f9-85e31213733d
            name: mustFollow
            description: Verificar se deve ser feito o follow up com base no histórico da
              conversa fornecida em formato JSON e o output deve ser DONT_FOLLOW_UP ou FOLLOW_UP
            type: POST_EXECUTION
            showOff: true
