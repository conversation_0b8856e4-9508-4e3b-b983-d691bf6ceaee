customerPreferences:
  # DigAI - Main customer with comprehensive preferences
  - customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c
    portfolio:
      defaultWorkflowId: 6f413811-4aa8-43f4-8c48-d00143dd226d
      timezoneUTC: "-3"
      importCronExpression: "0 9 * * 1-5"
      followUpWorkflowId: 7f413811-4aa8-43f4-8c48-d00143dd226e
      followUpCronExpression: "0 */2 * * *"
      followUpQuantity: 3
      followUpIntervalMinutes: 120
      exportColumns: ["name", "phone", "status", "lastInteraction", "followUpCount"]
      daysBeforePaymentReminder: 3
      paymentReminderInDay: true

      customImportConfig:
        delimiter: "|"
        taxRules:
          penaltyFee: "2.69" # percentage (2.69%, will be converted to 0.0269)
          dailyFee: "0.0333" # percentage
        additionalHeaders: [VALOR_DIVIDA_CORRIGIDO]